/**
 * DashboardPage - Page de gestion des visualisations sauvegardées
 * Permet de voir, rouvrir et supprimer les visualisations sauvegardées
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  BrAInBIHeader,
  DarkCard,
  SectionHeader,
  PrimaryButton,
  SecondaryButton,
  DarkSpinner,
  DarkBadge
} from './YellowMindUI';

const API_BASE_URL = 'http://localhost:8000';

const DashboardPage = () => {
  const navigate = useNavigate();
  
  // États
  const [visualizations, setVisualizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deletingId, setDeletingId] = useState(null);
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });

  // Fonction utilitaire pour afficher les toasts
  const showToast = (message, type = 'success') => {
    setToast({ show: true, message, type });
    setTimeout(() => {
      setToast({ show: false, message: '', type: 'success' });
    }, 4000);
  };

  // Charger les visualisations sauvegardées
  const loadVisualizations = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.get(`${API_BASE_URL}/saved-visuals`);
      
      if (response.data.success) {
        setVisualizations(response.data.visualizations);
      } else {
        setError('Erreur lors du chargement des visualisations');
      }
    } catch (err) {
      console.error('Erreur lors du chargement:', err);
      setError('Erreur lors du chargement: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  // Supprimer une visualisation
  const deleteVisualization = async (vizId, vizName) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer la visualisation "${vizName}" ?`)) {
      return;
    }

    try {
      setDeletingId(vizId);
      
      const response = await axios.delete(`${API_BASE_URL}/saved-visuals/${vizId}`);
      
      if (response.data.success) {
        setVisualizations(prev => prev.filter(viz => viz.id !== vizId));
        showToast(response.data.message);
      }
    } catch (err) {
      console.error('Erreur lors de la suppression:', err);
      showToast('Erreur lors de la suppression: ' + (err.response?.data?.detail || err.message), 'error');
    } finally {
      setDeletingId(null);
    }
  };

  // Rouvrir une visualisation dans l'éditeur
  const openVisualization = (vizId) => {
    navigate(`/drag-visual?load=${vizId}`);
  };

  // Charger les visualisations au montage du composant
  useEffect(() => {
    loadVisualizations();
  }, []);

  // Fonction pour formater la date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Fonction pour obtenir l'icône du type de graphique
  const getChartIcon = (chartType) => {
    const icons = {
      bar: '📊',
      line: '📈',
      pie: '🥧',
      scatter: '🔵',
      stacked_bar: '📊'
    };
    return icons[chartType] || '📊';
  };

  // Fonction pour obtenir la couleur du type de graphique
  const getChartColor = (chartType) => {
    const colors = {
      bar: 'bg-blue-600/20 text-blue-400 border-blue-500',
      line: 'bg-green-600/20 text-green-400 border-green-500',
      pie: 'bg-yellow-600/20 text-yellow-400 border-yellow-500',
      scatter: 'bg-purple-600/20 text-purple-400 border-purple-500',
      stacked_bar: 'bg-indigo-600/20 text-indigo-400 border-indigo-500'
    };
    return colors[chartType] || 'bg-gray-600/20 text-gray-400 border-gray-500';
  };

  return (
    <div className="min-h-screen bg-gray-950 text-gray-100">
      <BrAInBIHeader hasData={true} />
      
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
                  <span className="text-xl">📊</span>
                </div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  Mes Visualisations
                </h1>
              </div>
              <p className="text-gray-400">
                Gérez vos visualisations sauvegardées et créez-en de nouvelles
              </p>
            </div>
            
            <div className="flex space-x-3">
              <SecondaryButton
                onClick={() => loadVisualizations()}
                icon="🔄"
                disabled={loading}
              >
                Actualiser
              </SecondaryButton>
              <PrimaryButton
                onClick={() => navigate('/drag-visual')}
                icon="➕"
                className="!py-3 !px-6"
              >
                Créer un nouveau visuel
              </PrimaryButton>
            </div>
          </div>
        </div>

        {/* Toast de notification */}
        {toast.show && (
          <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${
            toast.type === 'success' 
              ? 'bg-green-900/90 border-green-700 text-green-100' 
              : 'bg-red-900/90 border-red-700 text-red-100'
          } backdrop-blur-sm`}>
            <div className="flex items-center space-x-2">
              <span>{toast.type === 'success' ? '✅' : '❌'}</span>
              <span>{toast.message}</span>
            </div>
          </div>
        )}

        {/* Contenu principal */}
        {loading ? (
          <DarkCard>
            <div className="flex items-center justify-center py-16">
              <div className="flex items-center space-x-3">
                <DarkSpinner />
                <span className="text-gray-300">Chargement des visualisations...</span>
              </div>
            </div>
          </DarkCard>
        ) : error ? (
          <DarkCard>
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="w-16 h-16 bg-red-600/20 rounded-xl mb-4 flex items-center justify-center">
                <span className="text-2xl">❌</span>
              </div>
              <h3 className="text-xl font-semibold text-red-400 mb-2">Erreur</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <SecondaryButton onClick={() => loadVisualizations()} icon="🔄">
                Réessayer
              </SecondaryButton>
            </div>
          </DarkCard>
        ) : visualizations.length === 0 ? (
          <DarkCard>
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center">
                <span className="text-4xl">📊</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-100 mb-4">
                Aucune visualisation sauvegardée
              </h3>
              <p className="text-gray-400 max-w-md mb-6">
                Commencez par créer votre première visualisation avec l'éditeur drag & drop
              </p>
              <PrimaryButton
                onClick={() => navigate('/drag-visual')}
                icon="➕"
                className="!py-3 !px-6"
              >
                Créer ma première visualisation
              </PrimaryButton>
            </div>
          </DarkCard>
        ) : (
          <div>
            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <DarkCard className="!p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
                    <span className="text-xl text-blue-400">📊</span>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-100">{visualizations.length}</div>
                    <div className="text-sm text-gray-400">Visualisation{visualizations.length > 1 ? 's' : ''}</div>
                  </div>
                </div>
              </DarkCard>
              
              <DarkCard className="!p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
                    <span className="text-xl text-green-400">📈</span>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-100">
                      {new Set(visualizations.map(v => v.chart_type)).size}
                    </div>
                    <div className="text-sm text-gray-400">Type{new Set(visualizations.map(v => v.chart_type)).size > 1 ? 's' : ''} de graphique</div>
                  </div>
                </div>
              </DarkCard>
              
              <DarkCard className="!p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
                    <span className="text-xl text-purple-400">🕒</span>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-100">
                      {visualizations.length > 0 ? formatDate(Math.max(...visualizations.map(v => new Date(v.created_at)))).split(' ')[0] : '-'}
                    </div>
                    <div className="text-sm text-gray-400">Dernière création</div>
                  </div>
                </div>
              </DarkCard>
            </div>

            {/* Liste des visualisations */}
            <DarkCard>
              <SectionHeader
                title="Visualisations sauvegardées"
                subtitle={`${visualizations.length} visualisation${visualizations.length > 1 ? 's' : ''} disponible${visualizations.length > 1 ? 's' : ''}`}
                icon="📋"
              />
              
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {visualizations.map((viz) => (
                  <div
                    key={viz.id}
                    className="bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:border-purple-500/50 transition-all duration-200 group"
                  >
                    {/* Header de la carte */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getChartColor(viz.chart_type)}`}>
                          <span className="text-lg">{getChartIcon(viz.chart_type)}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-100 truncate group-hover:text-purple-400 transition-colors">
                            {viz.name}
                          </h3>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">
                            {viz.chart_type.replace('_', ' ')}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    {viz.description && (
                      <p className="text-sm text-gray-400 mb-4 line-clamp-2">
                        {viz.description}
                      </p>
                    )}

                    {/* Tags */}
                    {viz.tags && viz.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {viz.tags.slice(0, 3).map((tag, index) => (
                          <DarkBadge key={index} className="!text-xs">
                            {tag}
                          </DarkBadge>
                        ))}
                        {viz.tags.length > 3 && (
                          <DarkBadge className="!text-xs">
                            +{viz.tags.length - 3}
                          </DarkBadge>
                        )}
                      </div>
                    )}

                    {/* Métadonnées */}
                    <div className="text-xs text-gray-500 mb-4">
                      <div>Créé le {formatDate(viz.created_at)}</div>
                      <div>Par {viz.user_id}</div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      <PrimaryButton
                        onClick={() => openVisualization(viz.id)}
                        icon="👁️"
                        className="flex-1 !py-2 !text-sm"
                      >
                        Ouvrir
                      </PrimaryButton>
                      <SecondaryButton
                        onClick={() => deleteVisualization(viz.id, viz.name)}
                        icon={deletingId === viz.id ? null : "🗑️"}
                        disabled={deletingId === viz.id}
                        className="!py-2 !px-3 !text-red-400 !border-red-600 hover:!bg-red-600/20"
                      >
                        {deletingId === viz.id ? <DarkSpinner className="!w-4 !h-4" /> : null}
                      </SecondaryButton>
                    </div>
                  </div>
                ))}
              </div>
            </DarkCard>
          </div>
        )}

        {/* Navigation */}
        <div className="mt-8 flex justify-center space-x-4">
          <SecondaryButton onClick={() => navigate('/tables')} icon="📋">
            Explorer les tables
          </SecondaryButton>
          <SecondaryButton onClick={() => navigate('/visual-builder')} icon="📊">
            Visual Builder
          </SecondaryButton>
          <SecondaryButton onClick={() => navigate('/')} icon="🏠">
            Accueil
          </SecondaryButton>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
