{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Routes, Route, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport './App.css';\nimport { BrAInBIHeader, HeroTitle, PrimaryButton, SecondaryButton, DarkCard, KPICard, ChatBubble, DarkInput, DarkBadge, DarkSpinner, SectionHeader } from './components/YellowMindUI';\nimport TablesOverview from './components/TablesOverview';\nimport TablesListPage from './components/TablesListPage';\nimport TableDetailPage from './components/TableDetailPage';\nimport VisualBuilderPage from './components/VisualBuilderPage';\nimport DragVisualPage from './components/DragVisualPage';\nimport DashboardPage from './components/DashboardPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\n\n// Composant principal de la page d'accueil\nfunction HomePage() {\n  _s();\n  var _dataInfo$total_rows, _dataInfo$columns, _dataInfo$columns2, _dataInfo$columns3, _dataInfo$sample_data, _fullSchemaData$datab, _fullSchemaData$datab2, _fullSchemaData$datab3, _fullSchemaData$datab4;\n  const navigate = useNavigate();\n  const [dataInfo, setDataInfo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [question, setQuestion] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [dataQuality, setDataQuality] = useState(null);\n  const [kpiExplanation, setKpiExplanation] = useState('');\n  const [showKpiModal, setShowKpiModal] = useState(false);\n  const [dataLoaded, setDataLoaded] = useState(false);\n  const [showTablesOverview, setShowTablesOverview] = useState(false);\n  const [fullSchemaData, setFullSchemaData] = useState(null);\n\n  // Fonction pour charger les informations des données SQL Server\n  const loadDataInfo = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/data/info`);\n      setDataInfo(response.data);\n      setDataQuality(response.data.data_quality);\n      setDataLoaded(true);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Erreur lors du chargement des données SQL Server: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n      setDataLoaded(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction pour charger l'aperçu complet des tables\n  const loadFullSchema = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/schema/full`);\n      setFullSchemaData(response.data);\n      setShowTablesOverview(true);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Erreur lors du chargement de l\\'aperçu des tables: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger automatiquement les données au démarrage\n  React.useEffect(() => {\n    loadDataInfo();\n  }, []);\n\n  // Poser une question personnalisée\n  const handleAskQuestion = async () => {\n    if (!question.trim()) {\n      setError('Veuillez saisir une question');\n      return;\n    }\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n    setLoading(true);\n    setError('');\n\n    // Ajouter la question de l'utilisateur au chat\n    const newHistory = [...chatHistory, {\n      type: 'user',\n      content: question,\n      timestamp: new Date().toLocaleTimeString()\n    }];\n    setChatHistory(newHistory);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/ask`, {\n        question: question\n      });\n\n      // Ajouter la réponse de l'IA au chat\n      setChatHistory([...newHistory, {\n        type: 'ai',\n        content: response.data.analysis,\n        chart: response.data.chart_data,\n        chartJson: response.data.chart_json,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n      setQuestion('');\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || 'Erreur lors de l\\'analyse');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mode pédagogique - Expliquer un KPI\n  const handleExplainKPI = async kpiName => {\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await axios.post(`${API_BASE_URL}/explain-kpi`, {\n        kpi_name: kpiName,\n        context: `Données de la table SQL Server ${(dataInfo === null || dataInfo === void 0 ? void 0 : dataInfo.table_name) || 'Sales'}`\n      });\n      setKpiExplanation(response.data.analysis);\n      setShowKpiModal(true);\n\n      // Ajouter à l'historique du chat\n      setChatHistory(prev => [...prev, {\n        type: 'pedagogical',\n        content: response.data.analysis,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail) || 'Erreur lors de l\\'explication');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effacer l'historique de conversation\n  const clearHistory = async () => {\n    try {\n      await axios.delete(`${API_BASE_URL}/conversation/history`);\n      setChatHistory([]);\n    } catch (err) {\n      console.error('Erreur lors de l\\'effacement de l\\'historique:', err);\n    }\n  };\n\n  // Rendu d'un graphique dynamique\n  const renderDynamicChart = chartData => {\n    if (!chartData) return null;\n    const plotData = [];\n    const layout = {\n      title: chartData.title || 'Graphique',\n      autosize: true,\n      height: 400,\n      margin: {\n        l: 50,\n        r: 50,\n        t: 50,\n        b: 50\n      }\n    };\n    switch (chartData.type) {\n      case 'bar':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'pie':\n        plotData.push({\n          labels: chartData.labels,\n          values: chartData.values,\n          type: 'pie',\n          marker: {\n            colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']\n          }\n        });\n        break;\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: {\n            color: '#667eea',\n            size: 8\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'histogram':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || 'Fréquence'\n        };\n        break;\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Type de graphique non support\\xE9: \", chartData.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 16\n        }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Plot, {\n      data: plotData,\n      layout: layout,\n      config: {\n        responsive: true\n      },\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-950 text-gray-100 fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n      hasData: dataLoaded\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"slide-in-top\",\n        children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n          title: \"BrAInBI\",\n          subtitle: \"Explore your Data. Amplified by AI.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/brAInBI-logo.png\",\n            alt: \"BrAInBI Logo\",\n            className: \"w-24 h-24 mx-auto mb-4 rounded-2xl shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-300 max-w-3xl mx-auto\",\n            children: \"Plateforme de Business Intelligence augment\\xE9e par l'IA pour explorer, comprendre et visualiser vos donn\\xE9es simplement via le langage naturel.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), !dataLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDDC4\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-100 mb-4\",\n              children: \"Connexion SQL Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 mb-8 max-w-md mx-auto\",\n              children: \"BrAInBI se connecte \\xE0 votre base de donn\\xE9es SQL Server en mode live\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-3 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: \"Connexion en cours...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-red-400 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u274C Erreur de connexion\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm mt-2 max-w-md mx-auto\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: loadDataInfo,\n                icon: \"\\uD83D\\uDD04\",\n                className: \"text-lg px-8 py-4\",\n                children: \"R\\xE9essayer la connexion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), dataLoaded && dataInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Aper\\xE7u des donn\\xE9es SQL Server\",\n          subtitle: \"Statistiques principales de votre base de donn\\xE9es\",\n          icon: \"\\uD83D\\uDCC8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Lignes totales\",\n            value: ((_dataInfo$total_rows = dataInfo.total_rows) === null || _dataInfo$total_rows === void 0 ? void 0 : _dataInfo$total_rows.toLocaleString()) || '0',\n            icon: \"\\uD83D\\uDCCA\",\n            trend: \"up\",\n            change: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Colonnes\",\n            value: ((_dataInfo$columns = dataInfo.columns) === null || _dataInfo$columns === void 0 ? void 0 : _dataInfo$columns.length) || '0',\n            icon: \"\\uD83D\\uDCCB\",\n            trend: \"neutral\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Tables\",\n            value: `${dataInfo.tables_count || 0} tables`,\n            icon: \"\\uD83D\\uDDC4\\uFE0F\",\n            trend: \"neutral\",\n            onClick: () => navigate('/tables'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Visualisations\",\n            value: \"Drag & Drop\",\n            icon: \"\\uD83C\\uDFAF\",\n            trend: \"up\",\n            change: \"Nouveau\",\n            onClick: () => navigate('/drag-visual'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Statut\",\n            value: \"Connect\\xE9\",\n            icon: \"\\u2705\",\n            trend: \"up\",\n            change: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n              title: \"Structure des donn\\xE9es\",\n              subtitle: \"Explorez les colonnes et la structure compl\\xE8te de la base\",\n              icon: \"\\uD83C\\uDFF7\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/drag-visual'),\n                disabled: loading,\n                icon: \"\\uD83C\\uDFAF\",\n                className: \"!py-2 !px-4\",\n                children: \"Drag & Drop\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/visual-builder'),\n                disabled: loading,\n                icon: \"\\uD83D\\uDCCA\",\n                className: \"!py-2 !px-4\",\n                children: \"Visual Builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: () => navigate('/tables'),\n                disabled: loading,\n                icon: \"\\uD83D\\uDD0D\",\n                className: \"!py-2 !px-4\",\n                children: \"Explorer les tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-3\",\n            children: dataInfo.tables && Object.keys(dataInfo.tables).length > 0 ? Object.entries(dataInfo.tables).slice(0, 1).map(([tableName, tableData]) => {\n              var _tableData$columns;\n              return (_tableData$columns = tableData.columns) === null || _tableData$columns === void 0 ? void 0 : _tableData$columns.map((col, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(DarkBadge, {\n                  variant: \"info\",\n                  className: \"cursor-pointer hover:bg-blue-800/70 transition-colors\",\n                  onClick: () => handleExplainKPI(col),\n                  children: col\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                  onClick: () => handleExplainKPI(col),\n                  className: \"!p-1 !min-w-0 w-6 h-6 text-xs\",\n                  icon: \"?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 25\n                }, this)]\n              }, `${tableName}-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 23\n              }, this));\n            }) : (_dataInfo$columns2 = dataInfo.columns) === null || _dataInfo$columns2 === void 0 ? void 0 : _dataInfo$columns2.map((col, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(DarkBadge, {\n                variant: \"info\",\n                className: \"cursor-pointer hover:bg-blue-800/70 transition-colors\",\n                onClick: () => handleExplainKPI(col),\n                children: col\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => handleExplainKPI(col),\n                className: \"!p-1 !min-w-0 w-6 h-6 text-xs\",\n                icon: \"?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), dataQuality && /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"mb-8 border-yellow-500/30 bg-yellow-900/10\",\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Qualit\\xE9 des donn\\xE9es\",\n            subtitle: \"Analyse automatique de la qualit\\xE9\",\n            icon: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300 whitespace-pre-wrap leading-relaxed\",\n            children: dataQuality\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Aper\\xE7u des donn\\xE9es SQL Server\",\n            subtitle: \"5 premi\\xE8res lignes de votre table\",\n            icon: \"\\uD83D\\uDC41\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-700\",\n                  children: (_dataInfo$columns3 = dataInfo.columns) === null || _dataInfo$columns3 === void 0 ? void 0 : _dataInfo$columns3.map((col, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-300 bg-gray-800/50\",\n                    children: col\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (_dataInfo$sample_data = dataInfo.sample_data) === null || _dataInfo$sample_data === void 0 ? void 0 : _dataInfo$sample_data.map((row, rowIndex) => {\n                  var _dataInfo$columns4;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"border-b border-gray-800 hover:bg-gray-800/30\",\n                    children: (_dataInfo$columns4 = dataInfo.columns) === null || _dataInfo$columns4 === void 0 ? void 0 : _dataInfo$columns4.map((col, colIndex) => /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4 text-gray-400\",\n                      children: row[col] || 'N/A'\n                    }, colIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 27\n                    }, this))\n                  }, rowIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this), showTablesOverview && fullSchemaData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-900 rounded-xl max-w-6xl w-full min-h-[90vh] my-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sticky top-0 bg-gray-900 rounded-t-xl border-b border-gray-800 z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-100\",\n                  children: \"Aper\\xE7u des Tables\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 mt-1\",\n                  children: [\"Base de donn\\xE9es: \", (_fullSchemaData$datab = fullSchemaData.database_info) === null || _fullSchemaData$datab === void 0 ? void 0 : _fullSchemaData$datab.name, \" \\u2022 \", (_fullSchemaData$datab2 = fullSchemaData.database_info) === null || _fullSchemaData$datab2 === void 0 ? void 0 : _fullSchemaData$datab2.tables_count, \" tables \\u2022 \", (_fullSchemaData$datab3 = fullSchemaData.database_info) === null || _fullSchemaData$datab3 === void 0 ? void 0 : (_fullSchemaData$datab4 = _fullSchemaData$datab3.total_rows) === null || _fullSchemaData$datab4 === void 0 ? void 0 : _fullSchemaData$datab4.toLocaleString(), \" lignes totales\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => setShowTablesOverview(false),\n                icon: \"\\u2715\",\n                className: \"!p-2 hover:bg-gray-800 transition-colors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(TablesOverview, {\n              data: fullSchemaData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this), dataLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Assistant IA BrAInBI\",\n            subtitle: \"Posez vos questions sur vos donn\\xE9es en langage naturel\",\n            icon: \"\\uD83D\\uDCAC\",\n            action: /*#__PURE__*/_jsxDEV(SecondaryButton, {\n              onClick: clearHistory,\n              disabled: loading || chatHistory.length === 0,\n              icon: \"\\uD83D\\uDDD1\\uFE0F\",\n              className: \"!py-2 !px-4\",\n              children: \"Effacer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-96 overflow-y-auto mb-6 p-4 bg-gray-900/30 rounded-lg border border-gray-800\",\n            children: chatHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center h-full text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-2xl mb-4 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl\",\n                  children: \"\\uD83E\\uDD16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-300 mb-2\",\n                children: \"Pr\\xEAt \\xE0 analyser vos donn\\xE9es avec BrAInBI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 max-w-md\",\n                children: \"Posez votre premi\\xE8re question pour commencer l'analyse intelligente de vos donn\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: chatHistory.map((message, index) => /*#__PURE__*/_jsxDEV(ChatBubble, {\n                type: message.type,\n                timestamp: message.timestamp,\n                children: [message.content, message.chart && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(Plot, {\n                    data: message.chart.data,\n                    layout: {\n                      ...message.chart.layout,\n                      autosize: true,\n                      height: 400,\n                      paper_bgcolor: 'rgba(0,0,0,0)',\n                      plot_bgcolor: 'rgba(0,0,0,0)',\n                      font: {\n                        color: '#F3F4F6'\n                      }\n                    },\n                    config: {\n                      responsive: true\n                    },\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 27\n                }, this), message.chartJson && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\",\n                  children: renderDynamicChart(JSON.parse(message.chartJson))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 27\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-400 mb-3\",\n              children: \"\\uD83D\\uDCA1 Suggestions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: ['Montre-moi un graphique en barres', 'Analyse les tendances', 'Y a-t-il des anomalies ?', 'Crée un diagramme circulaire', 'Résume les données'].map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setQuestion(suggestion),\n                disabled: loading || !dataLoaded,\n                className: \"px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full border border-gray-700 hover:border-gray-600 transition-colors disabled:opacity-50\",\n                children: suggestion\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(DarkInput, {\n                placeholder: \"Posez votre question sur les donn\\xE9es...\",\n                value: question,\n                onChange: e => setQuestion(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && !loading && dataLoaded && question.trim() && handleAskQuestion(),\n                disabled: loading || !dataLoaded,\n                icon: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n              onClick: handleAskQuestion,\n              disabled: loading || !dataLoaded || !question.trim(),\n              loading: loading,\n              icon: !loading ? \"🚀\" : null,\n              className: \"!py-3\",\n              children: \"Envoyer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this), showKpiModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"max-w-2xl w-full max-h-[80vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-100\",\n              children: \"\\uD83D\\uDCDA Explication p\\xE9dagogique\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowKpiModal(false),\n              className: \"w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300 whitespace-pre-wrap leading-relaxed\",\n            children: kpiExplanation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-lg p-4 shadow-xl z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-300 font-medium\",\n            children: \"Traitement en cours...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n}\n\n// Composant App principal avec routing\n_s(HomePage, \"eO0ou2YLznpJQiceDxAPq2MBhIA=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/tables\",\n      element: /*#__PURE__*/_jsxDEV(TablesListPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/table/:tableName\",\n      element: /*#__PURE__*/_jsxDEV(TableDetailPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 48\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/visual-builder\",\n      element: /*#__PURE__*/_jsxDEV(VisualBuilderPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 46\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/drag-visual\",\n      element: /*#__PURE__*/_jsxDEV(DragVisualPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 41\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/mes-visuels\",\n      element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 694,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "useNavigate", "axios", "Plot", "BrAInBIHeader", "<PERSON><PERSON><PERSON><PERSON>", "PrimaryButton", "SecondaryButton", "DarkCard", "KPICard", "ChatBubble", "DarkInput", "DarkBadge", "Dark<PERSON><PERSON>ner", "SectionHeader", "TablesOverview", "TablesListPage", "TableDetailPage", "VisualBuilderPage", "DragVisualPage", "DashboardPage", "jsxDEV", "_jsxDEV", "API_BASE_URL", "HomePage", "_s", "_dataInfo$total_rows", "_dataInfo$columns", "_dataInfo$columns2", "_dataInfo$columns3", "_dataInfo$sample_data", "_fullSchemaData$datab", "_fullSchemaData$datab2", "_fullSchemaData$datab3", "_fullSchemaData$datab4", "navigate", "dataInfo", "setDataInfo", "loading", "setLoading", "error", "setError", "question", "setQuestion", "chatHistory", "setChatHistory", "dataQuality", "setDataQuality", "kpiExplanation", "setKpiExplanation", "showKpiModal", "setShowKpiModal", "dataLoaded", "setDataLoaded", "showTablesOverview", "setShowTablesOverview", "fullSchemaData", "setFullSchemaData", "loadDataInfo", "response", "get", "data", "data_quality", "err", "_err$response", "_err$response$data", "detail", "message", "loadFullSchema", "_err$response2", "_err$response2$data", "useEffect", "handleAskQuestion", "trim", "newHistory", "type", "content", "timestamp", "Date", "toLocaleTimeString", "post", "analysis", "chart", "chart_data", "chartJson", "chart_json", "_err$response3", "_err$response3$data", "handleExplainKPI", "kpiName", "kpi_name", "context", "table_name", "prev", "_err$response4", "_err$response4$data", "clearHistory", "delete", "console", "renderDynamicChart", "chartData", "plotData", "layout", "title", "autosize", "height", "margin", "l", "r", "t", "b", "push", "x", "y", "marker", "color", "xaxis", "xlabel", "yaxis", "ylabel", "labels", "values", "colors", "mode", "line", "size", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "config", "responsive", "style", "width", "className", "hasData", "subtitle", "src", "alt", "onClick", "icon", "value", "total_rows", "toLocaleString", "trend", "change", "columns", "length", "tables_count", "disabled", "tables", "Object", "keys", "entries", "slice", "map", "tableName", "tableData", "_tableData$columns", "col", "index", "variant", "sample_data", "row", "rowIndex", "_dataInfo$columns4", "colIndex", "database_info", "name", "action", "paper_bgcolor", "plot_bgcolor", "font", "JSON", "parse", "suggestion", "placeholder", "onChange", "e", "target", "onKeyPress", "key", "_c", "App", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport './App.css';\nimport {\n  BrA<PERSON>nBIHeader,\n  HeroTitle,\n  PrimaryButton,\n  SecondaryButton,\n  DarkCard,\n  KPICard,\n  ChatBubble,\n  DarkInput,\n  DarkBadge,\n  DarkSpinner,\n  SectionHeader\n} from './components/YellowMindUI';\nimport TablesOverview from './components/TablesOverview';\nimport TablesListPage from './components/TablesListPage';\nimport TableDetailPage from './components/TableDetailPage';\nimport VisualBuilderPage from './components/VisualBuilderPage';\nimport DragVisualPage from './components/DragVisualPage';\nimport DashboardPage from './components/DashboardPage';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\n// Composant principal de la page d'accueil\nfunction HomePage() {\n  const navigate = useNavigate();\n  const [dataInfo, setDataInfo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [question, setQuestion] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [dataQuality, setDataQuality] = useState(null);\n  const [kpiExplanation, setKpiExplanation] = useState('');\n  const [showKpiModal, setShowKpiModal] = useState(false);\n  const [dataLoaded, setDataLoaded] = useState(false);\n  const [showTablesOverview, setShowTablesOverview] = useState(false);\n  const [fullSchemaData, setFullSchemaData] = useState(null);\n\n  // Fonction pour charger les informations des données SQL Server\n  const loadDataInfo = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      const response = await axios.get(`${API_BASE_URL}/data/info`);\n      setDataInfo(response.data);\n      setDataQuality(response.data.data_quality);\n      setDataLoaded(true);\n\n    } catch (err) {\n      setError('Erreur lors du chargement des données SQL Server: ' + (err.response?.data?.detail || err.message));\n      setDataLoaded(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction pour charger l'aperçu complet des tables\n  const loadFullSchema = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/schema/full`);\n      setFullSchemaData(response.data);\n      setShowTablesOverview(true);\n    } catch (err) {\n      setError('Erreur lors du chargement de l\\'aperçu des tables: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger automatiquement les données au démarrage\n  React.useEffect(() => {\n    loadDataInfo();\n  }, []);\n\n  // Poser une question personnalisée\n  const handleAskQuestion = async () => {\n    if (!question.trim()) {\n      setError('Veuillez saisir une question');\n      return;\n    }\n\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    // Ajouter la question de l'utilisateur au chat\n    const newHistory = [...chatHistory, {\n      type: 'user',\n      content: question,\n      timestamp: new Date().toLocaleTimeString()\n    }];\n    setChatHistory(newHistory);\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/ask`, {\n        question: question\n      });\n\n      // Ajouter la réponse de l'IA au chat\n      setChatHistory([...newHistory, {\n        type: 'ai',\n        content: response.data.analysis,\n        chart: response.data.chart_data,\n        chartJson: response.data.chart_json,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n\n      setQuestion('');\n\n    } catch (err) {\n      setError(err.response?.data?.detail || 'Erreur lors de l\\'analyse');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mode pédagogique - Expliquer un KPI\n  const handleExplainKPI = async (kpiName) => {\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/explain-kpi`, {\n        kpi_name: kpiName,\n        context: `Données de la table SQL Server ${dataInfo?.table_name || 'Sales'}`\n      });\n\n      setKpiExplanation(response.data.analysis);\n      setShowKpiModal(true);\n\n      // Ajouter à l'historique du chat\n      setChatHistory(prev => [...prev, {\n        type: 'pedagogical',\n        content: response.data.analysis,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n\n    } catch (err) {\n      setError(err.response?.data?.detail || 'Erreur lors de l\\'explication');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effacer l'historique de conversation\n  const clearHistory = async () => {\n    try {\n      await axios.delete(`${API_BASE_URL}/conversation/history`);\n      setChatHistory([]);\n    } catch (err) {\n      console.error('Erreur lors de l\\'effacement de l\\'historique:', err);\n    }\n  };\n\n  // Rendu d'un graphique dynamique\n  const renderDynamicChart = (chartData) => {\n    if (!chartData) return null;\n\n    const plotData = [];\n    const layout = {\n      title: chartData.title || 'Graphique',\n      autosize: true,\n      height: 400,\n      margin: { l: 50, r: 50, t: 50, b: 50 }\n    };\n\n    switch (chartData.type) {\n      case 'bar':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'pie':\n        plotData.push({\n          labels: chartData.labels,\n          values: chartData.values,\n          type: 'pie',\n          marker: { colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'] }\n        });\n        break;\n\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: { color: '#667eea', size: 8 }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'histogram':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || 'Fréquence' };\n        break;\n\n      default:\n        return <p>Type de graphique non supporté: {chartData.type}</p>;\n    }\n\n    return (\n      <Plot\n        data={plotData}\n        layout={layout}\n        config={{ responsive: true }}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gray-950 text-gray-100 fade-in\">\n      {/* Header BrAInBI */}\n      <BrAInBIHeader\n        hasData={dataLoaded}\n      />\n\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Hero Section */}\n        <div className=\"slide-in-top\">\n          <HeroTitle\n            title=\"BrAInBI\"\n            subtitle=\"Explore your Data. Amplified by AI.\"\n          />\n          <div className=\"text-center mb-8\">\n            <img\n              src=\"/brAInBI-logo.png\"\n              alt=\"BrAInBI Logo\"\n              className=\"w-24 h-24 mx-auto mb-4 rounded-2xl shadow-lg\"\n            />\n            <p className=\"text-lg text-gray-300 max-w-3xl mx-auto\">\n              Plateforme de Business Intelligence augmentée par l'IA pour explorer, comprendre et visualiser vos données simplement via le langage naturel.\n            </p>\n          </div>\n        </div>\n\n        {/* SQL Server Status Section */}\n        {!dataLoaded && (\n          <div className=\"mb-12\">\n            <DarkCard className=\"text-center\">\n              <div className=\"py-12\">\n                <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center\">\n                  <span className=\"text-2xl\">🗄️</span>\n                </div>\n\n                <h3 className=\"text-xl font-semibold text-gray-100 mb-4\">\n                  Connexion SQL Server\n                </h3>\n                <p className=\"text-gray-400 mb-8 max-w-md mx-auto\">\n                  BrAInBI se connecte à votre base de données SQL Server en mode live\n                </p>\n\n                {loading ? (\n                  <div className=\"flex items-center justify-center space-x-3 mb-6\">\n                    <DarkSpinner />\n                    <span className=\"text-gray-400\">Connexion en cours...</span>\n                  </div>\n                ) : error ? (\n                  <div className=\"mb-6\">\n                    <div className=\"text-red-400 mb-4\">\n                      <p>❌ Erreur de connexion</p>\n                      <p className=\"text-sm mt-2 max-w-md mx-auto\">{error}</p>\n                    </div>\n                    <PrimaryButton\n                      onClick={loadDataInfo}\n                      icon=\"🔄\"\n                      className=\"text-lg px-8 py-4\"\n                    >\n                      Réessayer la connexion\n                    </PrimaryButton>\n                  </div>\n                ) : null}\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n\n\n        {/* KPIs Section */}\n        {dataLoaded && dataInfo && (\n          <div className=\"mb-12\">\n            <SectionHeader\n              title=\"Aperçu des données SQL Server\"\n              subtitle=\"Statistiques principales de votre base de données\"\n              icon=\"📈\"\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n              <KPICard\n                title=\"Lignes totales\"\n                value={dataInfo.total_rows?.toLocaleString() || '0'}\n                icon=\"📊\"\n                trend=\"up\"\n                change=\"Live\"\n              />\n              <KPICard\n                title=\"Colonnes\"\n                value={dataInfo.columns?.length || '0'}\n                icon=\"📋\"\n                trend=\"neutral\"\n              />\n              <KPICard\n                title=\"Tables\"\n                value={`${dataInfo.tables_count || 0} tables`}\n                icon=\"🗄️\"\n                trend=\"neutral\"\n                onClick={() => navigate('/tables')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Visualisations\"\n                value=\"Drag & Drop\"\n                icon=\"🎯\"\n                trend=\"up\"\n                change=\"Nouveau\"\n                onClick={() => navigate('/drag-visual')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Statut\"\n                value=\"Connecté\"\n                icon=\"✅\"\n                trend=\"up\"\n                change=\"Live\"\n              />\n            </div>\n\n            {/* Structure des données et exploration des tables */}\n            <DarkCard className=\"mb-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <SectionHeader\n                  title=\"Structure des données\"\n                  subtitle=\"Explorez les colonnes et la structure complète de la base\"\n                  icon=\"🏷️\"\n                />\n                <div className=\"flex space-x-3\">\n                  <SecondaryButton\n                    onClick={() => navigate('/drag-visual')}\n                    disabled={loading}\n                    icon=\"🎯\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Drag & Drop\n                  </SecondaryButton>\n                  <SecondaryButton\n                    onClick={() => navigate('/visual-builder')}\n                    disabled={loading}\n                    icon=\"📊\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Visual Builder\n                  </SecondaryButton>\n                  <PrimaryButton\n                    onClick={() => navigate('/tables')}\n                    disabled={loading}\n                    icon=\"🔍\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Explorer les tables\n                  </PrimaryButton>\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-3\">\n                {dataInfo.tables && Object.keys(dataInfo.tables).length > 0 ? (\n                  Object.entries(dataInfo.tables).slice(0, 1).map(([tableName, tableData]) =>\n                    tableData.columns?.map((col, index) => (\n                      <div key={`${tableName}-${index}`} className=\"flex items-center space-x-2\">\n                        <DarkBadge\n                          variant=\"info\"\n                          className=\"cursor-pointer hover:bg-blue-800/70 transition-colors\"\n                          onClick={() => handleExplainKPI(col)}\n                        >\n                          {col}\n                        </DarkBadge>\n                        <SecondaryButton\n                          onClick={() => handleExplainKPI(col)}\n                          className=\"!p-1 !min-w-0 w-6 h-6 text-xs\"\n                          icon=\"?\"\n                        />\n                      </div>\n                    ))\n                  )\n                ) : (\n                  dataInfo.columns?.map((col, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <DarkBadge\n                        variant=\"info\"\n                        className=\"cursor-pointer hover:bg-blue-800/70 transition-colors\"\n                        onClick={() => handleExplainKPI(col)}\n                      >\n                        {col}\n                      </DarkBadge>\n                      <SecondaryButton\n                        onClick={() => handleExplainKPI(col)}\n                        className=\"!p-1 !min-w-0 w-6 h-6 text-xs\"\n                        icon=\"?\"\n                      />\n                    </div>\n                  ))\n                )}\n              </div>\n            </DarkCard>\n\n            {/* Qualité des données */}\n            {dataQuality && (\n              <DarkCard className=\"mb-8 border-yellow-500/30 bg-yellow-900/10\">\n                <SectionHeader\n                  title=\"Qualité des données\"\n                  subtitle=\"Analyse automatique de la qualité\"\n                  icon=\"🔍\"\n                />\n                <div className=\"text-gray-300 whitespace-pre-wrap leading-relaxed\">\n                  {dataQuality}\n                </div>\n              </DarkCard>\n            )}\n\n            {/* Aperçu du tableau */}\n            <DarkCard>\n              <SectionHeader\n                title=\"Aperçu des données SQL Server\"\n                subtitle=\"5 premières lignes de votre table\"\n                icon=\"👁️\"\n              />\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full text-sm\">\n                  <thead>\n                    <tr className=\"border-b border-gray-700\">\n                      {dataInfo.columns?.map((col, index) => (\n                        <th key={index} className=\"text-left py-3 px-4 font-semibold text-gray-300 bg-gray-800/50\">\n                          {col}\n                        </th>\n                      ))}\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {dataInfo.sample_data?.map((row, rowIndex) => (\n                      <tr key={rowIndex} className=\"border-b border-gray-800 hover:bg-gray-800/30\">\n                        {dataInfo.columns?.map((col, colIndex) => (\n                          <td key={colIndex} className=\"py-3 px-4 text-gray-400\">\n                            {row[col] || 'N/A'}\n                          </td>\n                        ))}\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Tables Overview Modal/Section */}\n        {showTablesOverview && fullSchemaData && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center p-4 overflow-y-auto\">\n            <div className=\"bg-gray-900 rounded-xl max-w-6xl w-full min-h-[90vh] my-4\">\n              {/* Header fixe */}\n              <div className=\"sticky top-0 bg-gray-900 rounded-t-xl border-b border-gray-800 z-10\">\n                <div className=\"flex items-center justify-between p-6\">\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-gray-100\">Aperçu des Tables</h2>\n                    <p className=\"text-gray-400 mt-1\">\n                      Base de données: {fullSchemaData.database_info?.name} • {fullSchemaData.database_info?.tables_count} tables • {fullSchemaData.database_info?.total_rows?.toLocaleString()} lignes totales\n                    </p>\n                  </div>\n                  <SecondaryButton\n                    onClick={() => setShowTablesOverview(false)}\n                    icon=\"✕\"\n                    className=\"!p-2 hover:bg-gray-800 transition-colors\"\n                  />\n                </div>\n              </div>\n\n              {/* Contenu scrollable */}\n              <div className=\"p-6\">\n                <TablesOverview data={fullSchemaData} />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Section - Aligné en bas */}\n        {dataLoaded && (\n          <div className=\"mb-8\">\n            <DarkCard>\n              <SectionHeader\n                title=\"Assistant IA BrAInBI\"\n                subtitle=\"Posez vos questions sur vos données en langage naturel\"\n                icon=\"💬\"\n                action={\n                  <SecondaryButton\n                    onClick={clearHistory}\n                    disabled={loading || chatHistory.length === 0}\n                    icon=\"🗑️\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Effacer\n                  </SecondaryButton>\n                }\n              />\n\n              {/* Chat History */}\n              <div className=\"h-96 overflow-y-auto mb-6 p-4 bg-gray-900/30 rounded-lg border border-gray-800\">\n                {chatHistory.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full text-center\">\n                    <div className=\"w-16 h-16 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-2xl mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl\">🤖</span>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-gray-300 mb-2\">\n                      Prêt à analyser vos données avec BrAInBI\n                    </h3>\n                    <p className=\"text-gray-500 max-w-md\">\n                      Posez votre première question pour commencer l'analyse intelligente de vos données\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {chatHistory.map((message, index) => (\n                      <ChatBubble\n                        key={index}\n                        type={message.type}\n                        timestamp={message.timestamp}\n                      >\n                        {message.content}\n\n                        {/* Graphiques */}\n                        {message.chart && (\n                          <div className=\"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\">\n                            <Plot\n                              data={message.chart.data}\n                              layout={{\n                                ...message.chart.layout,\n                                autosize: true,\n                                height: 400,\n                                paper_bgcolor: 'rgba(0,0,0,0)',\n                                plot_bgcolor: 'rgba(0,0,0,0)',\n                                font: { color: '#F3F4F6' }\n                              }}\n                              config={{ responsive: true }}\n                              style={{ width: '100%' }}\n                            />\n                          </div>\n                        )}\n\n                        {message.chartJson && (\n                          <div className=\"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\">\n                            {renderDynamicChart(JSON.parse(message.chartJson))}\n                          </div>\n                        )}\n                      </ChatBubble>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              {/* Suggestions */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-sm font-medium text-gray-400 mb-3\">💡 Suggestions</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {[\n                    'Montre-moi un graphique en barres',\n                    'Analyse les tendances',\n                    'Y a-t-il des anomalies ?',\n                    'Crée un diagramme circulaire',\n                    'Résume les données'\n                  ].map((suggestion, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setQuestion(suggestion)}\n                      disabled={loading || !dataLoaded}\n                      className=\"px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full border border-gray-700 hover:border-gray-600 transition-colors disabled:opacity-50\"\n                    >\n                      {suggestion}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Input Zone */}\n              <div className=\"flex space-x-4\">\n                <div className=\"flex-1\">\n                  <DarkInput\n                    placeholder=\"Posez votre question sur les données...\"\n                    value={question}\n                    onChange={(e) => setQuestion(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && !loading && dataLoaded && question.trim() && handleAskQuestion()}\n                    disabled={loading || !dataLoaded}\n                    icon=\"💬\"\n                  />\n                </div>\n                <PrimaryButton\n                  onClick={handleAskQuestion}\n                  disabled={loading || !dataLoaded || !question.trim()}\n                  loading={loading}\n                  icon={!loading ? \"🚀\" : null}\n                  className=\"!py-3\"\n                >\n                  Envoyer\n                </PrimaryButton>\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Modal KPI */}\n        {showKpiModal && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n            <DarkCard className=\"max-w-2xl w-full max-h-[80vh] overflow-y-auto\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-100\">\n                  📚 Explication pédagogique\n                </h3>\n                <button\n                  onClick={() => setShowKpiModal(false)}\n                  className=\"w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors\"\n                >\n                  ×\n                </button>\n              </div>\n              <div className=\"text-gray-300 whitespace-pre-wrap leading-relaxed\">\n                {kpiExplanation}\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Loading Indicator */}\n        {loading && (\n          <div className=\"fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-lg p-4 shadow-xl z-50\">\n            <div className=\"flex items-center space-x-3\">\n              <DarkSpinner />\n              <span className=\"text-gray-300 font-medium\">\n                Traitement en cours...\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Composant App principal avec routing\nfunction App() {\n  return (\n    <Routes>\n      <Route path=\"/\" element={<HomePage />} />\n      <Route path=\"/tables\" element={<TablesListPage />} />\n      <Route path=\"/table/:tableName\" element={<TableDetailPage />} />\n      <Route path=\"/visual-builder\" element={<VisualBuilderPage />} />\n      <Route path=\"/drag-visual\" element={<DragVisualPage />} />\n      <Route path=\"/dashboard\" element={<DashboardPage />} />\n      <Route path=\"/mes-visuels\" element={<DashboardPage />} />\n    </Routes>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,WAAW;AAClB,SACEC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,WAAW,EACXC,aAAa,QACR,2BAA2B;AAClC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,YAAY,GAAG,uBAAuB;;AAE5C;AACA,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClB,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM4D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMkB,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,GAAG,CAAC,GAAGrC,YAAY,YAAY,CAAC;MAC7Dc,WAAW,CAACsB,QAAQ,CAACE,IAAI,CAAC;MAC1Bd,cAAc,CAACY,QAAQ,CAACE,IAAI,CAACC,YAAY,CAAC;MAC1CT,aAAa,CAAC,IAAI,CAAC;IAErB,CAAC,CAAC,OAAOU,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZxB,QAAQ,CAAC,oDAAoD,IAAI,EAAAuB,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBC,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;MAC5Gd,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF7B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,GAAG,CAAC,GAAGrC,YAAY,cAAc,CAAC;MAC/DkC,iBAAiB,CAACE,QAAQ,CAACE,IAAI,CAAC;MAChCN,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOQ,GAAG,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACZ7B,QAAQ,CAAC,qDAAqD,IAAI,EAAA4B,cAAA,GAAAN,GAAG,CAACJ,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcR,IAAI,cAAAS,mBAAA,uBAAlBA,mBAAA,CAAoBJ,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;IAC/G,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA1C,KAAK,CAAC0E,SAAS,CAAC,MAAM;IACpBb,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,CAAC,CAAC,EAAE;MACpBhC,QAAQ,CAAC,8BAA8B,CAAC;MACxC;IACF;IAEA,IAAI,CAACW,UAAU,EAAE;MACfX,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,MAAMiC,UAAU,GAAG,CAAC,GAAG9B,WAAW,EAAE;MAClC+B,IAAI,EAAE,MAAM;MACZC,OAAO,EAAElC,QAAQ;MACjBmC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC;IACFlC,cAAc,CAAC6B,UAAU,CAAC;IAE1B,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMzD,KAAK,CAAC8E,IAAI,CAAC,GAAGzD,YAAY,MAAM,EAAE;QACvDmB,QAAQ,EAAEA;MACZ,CAAC,CAAC;;MAEF;MACAG,cAAc,CAAC,CAAC,GAAG6B,UAAU,EAAE;QAC7BC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAEjB,QAAQ,CAACE,IAAI,CAACoB,QAAQ;QAC/BC,KAAK,EAAEvB,QAAQ,CAACE,IAAI,CAACsB,UAAU;QAC/BC,SAAS,EAAEzB,QAAQ,CAACE,IAAI,CAACwB,UAAU;QACnCR,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MAC3C,CAAC,CAAC,CAAC;MAEHpC,WAAW,CAAC,EAAE,CAAC;IAEjB,CAAC,CAAC,OAAOoB,GAAG,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACZ9C,QAAQ,CAAC,EAAA6C,cAAA,GAAAvB,GAAG,CAACJ,QAAQ,cAAA2B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,uBAAlBA,mBAAA,CAAoBrB,MAAM,KAAI,2BAA2B,CAAC;IACrE,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI,CAACrC,UAAU,EAAE;MACfX,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMzD,KAAK,CAAC8E,IAAI,CAAC,GAAGzD,YAAY,cAAc,EAAE;QAC/DmE,QAAQ,EAAED,OAAO;QACjBE,OAAO,EAAE,kCAAkC,CAAAvD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,UAAU,KAAI,OAAO;MAC5E,CAAC,CAAC;MAEF3C,iBAAiB,CAACU,QAAQ,CAACE,IAAI,CAACoB,QAAQ,CAAC;MACzC9B,eAAe,CAAC,IAAI,CAAC;;MAErB;MACAN,cAAc,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC/BlB,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAEjB,QAAQ,CAACE,IAAI,CAACoB,QAAQ;QAC/BJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MAC3C,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOhB,GAAG,EAAE;MAAA,IAAA+B,cAAA,EAAAC,mBAAA;MACZtD,QAAQ,CAAC,EAAAqD,cAAA,GAAA/B,GAAG,CAACJ,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoB7B,MAAM,KAAI,+BAA+B,CAAC;IACzE,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM9F,KAAK,CAAC+F,MAAM,CAAC,GAAG1E,YAAY,uBAAuB,CAAC;MAC1DsB,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZmC,OAAO,CAAC1D,KAAK,CAAC,gDAAgD,EAAEuB,GAAG,CAAC;IACtE;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,MAAM,GAAG;MACbC,KAAK,EAAEH,SAAS,CAACG,KAAK,IAAI,WAAW;MACrCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAG;IACvC,CAAC;IAED,QAAQV,SAAS,CAACzB,IAAI;MACpB,KAAK,KAAK;QACR0B,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdtC,IAAI,EAAE,KAAK;UACXuC,MAAM,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC7B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,KAAK;QACRlB,QAAQ,CAACU,IAAI,CAAC;UACZS,MAAM,EAAEpB,SAAS,CAACoB,MAAM;UACxBC,MAAM,EAAErB,SAAS,CAACqB,MAAM;UACxB9C,IAAI,EAAE,KAAK;UACXuC,MAAM,EAAE;YAAEQ,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;UAAE;QAC5E,CAAC,CAAC;QACF;MAEF,KAAK,MAAM;QACTrB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdtC,IAAI,EAAE,SAAS;UACfgD,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAET,KAAK,EAAE;UAAU;QAC3B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,SAAS;QACZlB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdtC,IAAI,EAAE,SAAS;UACfgD,IAAI,EAAE,SAAS;UACfT,MAAM,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEU,IAAI,EAAE;UAAE;QACtC,CAAC,CAAC;QACFvB,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,WAAW;QACdlB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdtC,IAAI,EAAE,KAAK;UACXuC,MAAM,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC7B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAY,CAAC;QACzD;MAEF;QACE,oBAAOjG,OAAA;UAAAwG,QAAA,GAAG,qCAAgC,EAAC1B,SAAS,CAACzB,IAAI;QAAA;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;IAClE;IAEA,oBACE5G,OAAA,CAACnB,IAAI;MACH0D,IAAI,EAAEwC,QAAS;MACfC,MAAM,EAAEA,MAAO;MACf6B,MAAM,EAAE;QAAEC,UAAU,EAAE;MAAK,CAAE;MAC7BC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAMD,oBACE5G,OAAA;IAAKiH,SAAS,EAAC,gDAAgD;IAAAT,QAAA,gBAE7DxG,OAAA,CAAClB,aAAa;MACZoI,OAAO,EAAEpF;IAAW;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAEF5G,OAAA;MAAKiH,SAAS,EAAC,wBAAwB;MAAAT,QAAA,gBAErCxG,OAAA;QAAKiH,SAAS,EAAC,cAAc;QAAAT,QAAA,gBAC3BxG,OAAA,CAACjB,SAAS;UACRkG,KAAK,EAAC,SAAS;UACfkC,QAAQ,EAAC;QAAqC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACF5G,OAAA;UAAKiH,SAAS,EAAC,kBAAkB;UAAAT,QAAA,gBAC/BxG,OAAA;YACEoH,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,cAAc;YAClBJ,SAAS,EAAC;UAA8C;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACF5G,OAAA;YAAGiH,SAAS,EAAC,yCAAyC;YAAAT,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAAC9E,UAAU,iBACV9B,OAAA;QAAKiH,SAAS,EAAC,OAAO;QAAAT,QAAA,eACpBxG,OAAA,CAACd,QAAQ;UAAC+H,SAAS,EAAC,aAAa;UAAAT,QAAA,eAC/BxG,OAAA;YAAKiH,SAAS,EAAC,OAAO;YAAAT,QAAA,gBACpBxG,OAAA;cAAKiH,SAAS,EAAC,kHAAkH;cAAAT,QAAA,eAC/HxG,OAAA;gBAAMiH,SAAS,EAAC,UAAU;gBAAAT,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eAEN5G,OAAA;cAAIiH,SAAS,EAAC,0CAA0C;cAAAT,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5G,OAAA;cAAGiH,SAAS,EAAC,qCAAqC;cAAAT,QAAA,EAAC;YAEnD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAEH5F,OAAO,gBACNhB,OAAA;cAAKiH,SAAS,EAAC,iDAAiD;cAAAT,QAAA,gBAC9DxG,OAAA,CAACT,WAAW;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACf5G,OAAA;gBAAMiH,SAAS,EAAC,eAAe;gBAAAT,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,GACJ1F,KAAK,gBACPlB,OAAA;cAAKiH,SAAS,EAAC,MAAM;cAAAT,QAAA,gBACnBxG,OAAA;gBAAKiH,SAAS,EAAC,mBAAmB;gBAAAT,QAAA,gBAChCxG,OAAA;kBAAAwG,QAAA,EAAG;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5B5G,OAAA;kBAAGiH,SAAS,EAAC,+BAA+B;kBAAAT,QAAA,EAAEtF;gBAAK;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN5G,OAAA,CAAChB,aAAa;gBACZsI,OAAO,EAAElF,YAAa;gBACtBmF,IAAI,EAAC,cAAI;gBACTN,SAAS,EAAC,mBAAmB;gBAAAT,QAAA,EAC9B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAKA9E,UAAU,IAAIhB,QAAQ,iBACrBd,OAAA;QAAKiH,SAAS,EAAC,OAAO;QAAAT,QAAA,gBACpBxG,OAAA,CAACR,aAAa;UACZyF,KAAK,EAAC,qCAA+B;UACrCkC,QAAQ,EAAC,sDAAmD;UAC5DI,IAAI,EAAC;QAAI;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEF5G,OAAA;UAAKiH,SAAS,EAAC,2DAA2D;UAAAT,QAAA,gBACxExG,OAAA,CAACb,OAAO;YACN8F,KAAK,EAAC,gBAAgB;YACtBuC,KAAK,EAAE,EAAApH,oBAAA,GAAAU,QAAQ,CAAC2G,UAAU,cAAArH,oBAAA,uBAAnBA,oBAAA,CAAqBsH,cAAc,CAAC,CAAC,KAAI,GAAI;YACpDH,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACF5G,OAAA,CAACb,OAAO;YACN8F,KAAK,EAAC,UAAU;YAChBuC,KAAK,EAAE,EAAAnH,iBAAA,GAAAS,QAAQ,CAAC+G,OAAO,cAAAxH,iBAAA,uBAAhBA,iBAAA,CAAkByH,MAAM,KAAI,GAAI;YACvCP,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC;UAAS;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF5G,OAAA,CAACb,OAAO;YACN8F,KAAK,EAAC,QAAQ;YACduC,KAAK,EAAE,GAAG1G,QAAQ,CAACiH,YAAY,IAAI,CAAC,SAAU;YAC9CR,IAAI,EAAC,oBAAK;YACVI,KAAK,EAAC,SAAS;YACfL,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,SAAS,CAAE;YACnCoG,SAAS,EAAC;UAAuD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACF5G,OAAA,CAACb,OAAO;YACN8F,KAAK,EAAC,gBAAgB;YACtBuC,KAAK,EAAC,aAAa;YACnBD,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,SAAS;YAChBN,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,cAAc,CAAE;YACxCoG,SAAS,EAAC;UAAuD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACF5G,OAAA,CAACb,OAAO;YACN8F,KAAK,EAAC,QAAQ;YACduC,KAAK,EAAC,aAAU;YAChBD,IAAI,EAAC,QAAG;YACRI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAM;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN5G,OAAA,CAACd,QAAQ;UAAC+H,SAAS,EAAC,MAAM;UAAAT,QAAA,gBACxBxG,OAAA;YAAKiH,SAAS,EAAC,wCAAwC;YAAAT,QAAA,gBACrDxG,OAAA,CAACR,aAAa;cACZyF,KAAK,EAAC,0BAAuB;cAC7BkC,QAAQ,EAAC,8DAA2D;cACpEI,IAAI,EAAC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACF5G,OAAA;cAAKiH,SAAS,EAAC,gBAAgB;cAAAT,QAAA,gBAC7BxG,OAAA,CAACf,eAAe;gBACdqI,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,cAAc,CAAE;gBACxCmH,QAAQ,EAAEhH,OAAQ;gBAClBuG,IAAI,EAAC,cAAI;gBACTN,SAAS,EAAC,aAAa;gBAAAT,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClB5G,OAAA,CAACf,eAAe;gBACdqI,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,iBAAiB,CAAE;gBAC3CmH,QAAQ,EAAEhH,OAAQ;gBAClBuG,IAAI,EAAC,cAAI;gBACTN,SAAS,EAAC,aAAa;gBAAAT,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClB5G,OAAA,CAAChB,aAAa;gBACZsI,OAAO,EAAEA,CAAA,KAAMzG,QAAQ,CAAC,SAAS,CAAE;gBACnCmH,QAAQ,EAAEhH,OAAQ;gBAClBuG,IAAI,EAAC,cAAI;gBACTN,SAAS,EAAC,aAAa;gBAAAT,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKiH,SAAS,EAAC,sBAAsB;YAAAT,QAAA,EAClC1F,QAAQ,CAACmH,MAAM,IAAIC,MAAM,CAACC,IAAI,CAACrH,QAAQ,CAACmH,MAAM,CAAC,CAACH,MAAM,GAAG,CAAC,GACzDI,MAAM,CAACE,OAAO,CAACtH,QAAQ,CAACmH,MAAM,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,SAAS,EAAEC,SAAS,CAAC;cAAA,IAAAC,kBAAA;cAAA,QAAAA,kBAAA,GACrED,SAAS,CAACX,OAAO,cAAAY,kBAAA,uBAAjBA,kBAAA,CAAmBH,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAChC3I,OAAA;gBAAmCiH,SAAS,EAAC,6BAA6B;gBAAAT,QAAA,gBACxExG,OAAA,CAACV,SAAS;kBACRsJ,OAAO,EAAC,MAAM;kBACd3B,SAAS,EAAC,uDAAuD;kBACjEK,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAACwE,GAAG,CAAE;kBAAAlC,QAAA,EAEpCkC;gBAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZ5G,OAAA,CAACf,eAAe;kBACdqI,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAACwE,GAAG,CAAE;kBACrCzB,SAAS,EAAC,+BAA+B;kBACzCM,IAAI,EAAC;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAZM,GAAG2B,SAAS,IAAII,KAAK,EAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAa5B,CACN,CAAC;YAAA,CACJ,CAAC,IAAAtG,kBAAA,GAEDQ,QAAQ,CAAC+G,OAAO,cAAAvH,kBAAA,uBAAhBA,kBAAA,CAAkBgI,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC/B3I,OAAA;cAAiBiH,SAAS,EAAC,6BAA6B;cAAAT,QAAA,gBACtDxG,OAAA,CAACV,SAAS;gBACRsJ,OAAO,EAAC,MAAM;gBACd3B,SAAS,EAAC,uDAAuD;gBACjEK,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAACwE,GAAG,CAAE;gBAAAlC,QAAA,EAEpCkC;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACZ5G,OAAA,CAACf,eAAe;gBACdqI,OAAO,EAAEA,CAAA,KAAMpD,gBAAgB,CAACwE,GAAG,CAAE;gBACrCzB,SAAS,EAAC,+BAA+B;gBACzCM,IAAI,EAAC;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GAZM+B,KAAK;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGVpF,WAAW,iBACVxB,OAAA,CAACd,QAAQ;UAAC+H,SAAS,EAAC,4CAA4C;UAAAT,QAAA,gBAC9DxG,OAAA,CAACR,aAAa;YACZyF,KAAK,EAAC,2BAAqB;YAC3BkC,QAAQ,EAAC,sCAAmC;YAC5CI,IAAI,EAAC;UAAI;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACF5G,OAAA;YAAKiH,SAAS,EAAC,mDAAmD;YAAAT,QAAA,EAC/DhF;UAAW;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACX,eAGD5G,OAAA,CAACd,QAAQ;UAAAsH,QAAA,gBACPxG,OAAA,CAACR,aAAa;YACZyF,KAAK,EAAC,qCAA+B;YACrCkC,QAAQ,EAAC,sCAAmC;YAC5CI,IAAI,EAAC;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEF5G,OAAA;YAAKiH,SAAS,EAAC,iBAAiB;YAAAT,QAAA,eAC9BxG,OAAA;cAAOiH,SAAS,EAAC,gBAAgB;cAAAT,QAAA,gBAC/BxG,OAAA;gBAAAwG,QAAA,eACExG,OAAA;kBAAIiH,SAAS,EAAC,0BAA0B;kBAAAT,QAAA,GAAAjG,kBAAA,GACrCO,QAAQ,CAAC+G,OAAO,cAAAtH,kBAAA,uBAAhBA,kBAAA,CAAkB+H,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAChC3I,OAAA;oBAAgBiH,SAAS,EAAC,gEAAgE;oBAAAT,QAAA,EACvFkC;kBAAG,GADGC,KAAK;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5G,OAAA;gBAAAwG,QAAA,GAAAhG,qBAAA,GACGM,QAAQ,CAAC+H,WAAW,cAAArI,qBAAA,uBAApBA,qBAAA,CAAsB8H,GAAG,CAAC,CAACQ,GAAG,EAAEC,QAAQ;kBAAA,IAAAC,kBAAA;kBAAA,oBACvChJ,OAAA;oBAAmBiH,SAAS,EAAC,+CAA+C;oBAAAT,QAAA,GAAAwC,kBAAA,GACzElI,QAAQ,CAAC+G,OAAO,cAAAmB,kBAAA,uBAAhBA,kBAAA,CAAkBV,GAAG,CAAC,CAACI,GAAG,EAAEO,QAAQ,kBACnCjJ,OAAA;sBAAmBiH,SAAS,EAAC,yBAAyB;sBAAAT,QAAA,EACnDsC,GAAG,CAACJ,GAAG,CAAC,IAAI;oBAAK,GADXO,QAAQ;sBAAAxC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACL;kBAAC,GALKmC,QAAQ;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMb,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGA5E,kBAAkB,IAAIE,cAAc,iBACnClC,OAAA;QAAKiH,SAAS,EAAC,qGAAqG;QAAAT,QAAA,eAClHxG,OAAA;UAAKiH,SAAS,EAAC,2DAA2D;UAAAT,QAAA,gBAExExG,OAAA;YAAKiH,SAAS,EAAC,qEAAqE;YAAAT,QAAA,eAClFxG,OAAA;cAAKiH,SAAS,EAAC,uCAAuC;cAAAT,QAAA,gBACpDxG,OAAA;gBAAAwG,QAAA,gBACExG,OAAA;kBAAIiH,SAAS,EAAC,kCAAkC;kBAAAT,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE5G,OAAA;kBAAGiH,SAAS,EAAC,oBAAoB;kBAAAT,QAAA,GAAC,sBACf,GAAA/F,qBAAA,GAACyB,cAAc,CAACgH,aAAa,cAAAzI,qBAAA,uBAA5BA,qBAAA,CAA8B0I,IAAI,EAAC,UAAG,GAAAzI,sBAAA,GAACwB,cAAc,CAACgH,aAAa,cAAAxI,sBAAA,uBAA5BA,sBAAA,CAA8BqH,YAAY,EAAC,iBAAU,GAAApH,sBAAA,GAACuB,cAAc,CAACgH,aAAa,cAAAvI,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8B8G,UAAU,cAAA7G,sBAAA,uBAAxCA,sBAAA,CAA0C8G,cAAc,CAAC,CAAC,EAAC,iBAC5K;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN5G,OAAA,CAACf,eAAe;gBACdqI,OAAO,EAAEA,CAAA,KAAMrF,qBAAqB,CAAC,KAAK,CAAE;gBAC5CsF,IAAI,EAAC,QAAG;gBACRN,SAAS,EAAC;cAA0C;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5G,OAAA;YAAKiH,SAAS,EAAC,KAAK;YAAAT,QAAA,eAClBxG,OAAA,CAACP,cAAc;cAAC8C,IAAI,EAAEL;YAAe;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA9E,UAAU,iBACT9B,OAAA;QAAKiH,SAAS,EAAC,MAAM;QAAAT,QAAA,eACnBxG,OAAA,CAACd,QAAQ;UAAAsH,QAAA,gBACPxG,OAAA,CAACR,aAAa;YACZyF,KAAK,EAAC,sBAAsB;YAC5BkC,QAAQ,EAAC,2DAAwD;YACjEI,IAAI,EAAC,cAAI;YACT6B,MAAM,eACJpJ,OAAA,CAACf,eAAe;cACdqI,OAAO,EAAE5C,YAAa;cACtBsD,QAAQ,EAAEhH,OAAO,IAAIM,WAAW,CAACwG,MAAM,KAAK,CAAE;cAC9CP,IAAI,EAAC,oBAAK;cACVN,SAAS,EAAC,aAAa;cAAAT,QAAA,EACxB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB;UAClB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF5G,OAAA;YAAKiH,SAAS,EAAC,gFAAgF;YAAAT,QAAA,EAC5FlF,WAAW,CAACwG,MAAM,KAAK,CAAC,gBACvB9H,OAAA;cAAKiH,SAAS,EAAC,8DAA8D;cAAAT,QAAA,gBAC3ExG,OAAA;gBAAKiH,SAAS,EAAC,0GAA0G;gBAAAT,QAAA,eACvHxG,OAAA;kBAAMiH,SAAS,EAAC,UAAU;kBAAAT,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACN5G,OAAA;gBAAIiH,SAAS,EAAC,0CAA0C;gBAAAT,QAAA,EAAC;cAEzD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5G,OAAA;gBAAGiH,SAAS,EAAC,wBAAwB;gBAAAT,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAEN5G,OAAA;cAAKiH,SAAS,EAAC,WAAW;cAAAT,QAAA,EACvBlF,WAAW,CAACgH,GAAG,CAAC,CAACzF,OAAO,EAAE8F,KAAK,kBAC9B3I,OAAA,CAACZ,UAAU;gBAETiE,IAAI,EAAER,OAAO,CAACQ,IAAK;gBACnBE,SAAS,EAAEV,OAAO,CAACU,SAAU;gBAAAiD,QAAA,GAE5B3D,OAAO,CAACS,OAAO,EAGfT,OAAO,CAACe,KAAK,iBACZ5D,OAAA;kBAAKiH,SAAS,EAAC,wDAAwD;kBAAAT,QAAA,eACrExG,OAAA,CAACnB,IAAI;oBACH0D,IAAI,EAAEM,OAAO,CAACe,KAAK,CAACrB,IAAK;oBACzByC,MAAM,EAAE;sBACN,GAAGnC,OAAO,CAACe,KAAK,CAACoB,MAAM;sBACvBE,QAAQ,EAAE,IAAI;sBACdC,MAAM,EAAE,GAAG;sBACXkE,aAAa,EAAE,eAAe;sBAC9BC,YAAY,EAAE,eAAe;sBAC7BC,IAAI,EAAE;wBAAE1D,KAAK,EAAE;sBAAU;oBAC3B,CAAE;oBACFgB,MAAM,EAAE;sBAAEC,UAAU,EAAE;oBAAK,CAAE;oBAC7BC,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO;kBAAE;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,EAEA/D,OAAO,CAACiB,SAAS,iBAChB9D,OAAA;kBAAKiH,SAAS,EAAC,wDAAwD;kBAAAT,QAAA,EACpE3B,kBAAkB,CAAC2E,IAAI,CAACC,KAAK,CAAC5G,OAAO,CAACiB,SAAS,CAAC;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CACN;cAAA,GA7BI+B,KAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN5G,OAAA;YAAKiH,SAAS,EAAC,MAAM;YAAAT,QAAA,gBACnBxG,OAAA;cAAIiH,SAAS,EAAC,wCAAwC;cAAAT,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E5G,OAAA;cAAKiH,SAAS,EAAC,sBAAsB;cAAAT,QAAA,EAClC,CACC,mCAAmC,EACnC,uBAAuB,EACvB,0BAA0B,EAC1B,8BAA8B,EAC9B,oBAAoB,CACrB,CAAC8B,GAAG,CAAC,CAACoB,UAAU,EAAEf,KAAK,kBACtB3I,OAAA;gBAEEsH,OAAO,EAAEA,CAAA,KAAMjG,WAAW,CAACqI,UAAU,CAAE;gBACvC1B,QAAQ,EAAEhH,OAAO,IAAI,CAACc,UAAW;gBACjCmF,SAAS,EAAC,+JAA+J;gBAAAT,QAAA,EAExKkD;cAAU,GALNf,KAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5G,OAAA;YAAKiH,SAAS,EAAC,gBAAgB;YAAAT,QAAA,gBAC7BxG,OAAA;cAAKiH,SAAS,EAAC,QAAQ;cAAAT,QAAA,eACrBxG,OAAA,CAACX,SAAS;gBACRsK,WAAW,EAAC,4CAAyC;gBACrDnC,KAAK,EAAEpG,QAAS;gBAChBwI,QAAQ,EAAGC,CAAC,IAAKxI,WAAW,CAACwI,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;gBAC7CuC,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI,CAAChJ,OAAO,IAAIc,UAAU,IAAIV,QAAQ,CAAC+B,IAAI,CAAC,CAAC,IAAID,iBAAiB,CAAC,CAAE;gBACzG8E,QAAQ,EAAEhH,OAAO,IAAI,CAACc,UAAW;gBACjCyF,IAAI,EAAC;cAAI;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5G,OAAA,CAAChB,aAAa;cACZsI,OAAO,EAAEpE,iBAAkB;cAC3B8E,QAAQ,EAAEhH,OAAO,IAAI,CAACc,UAAU,IAAI,CAACV,QAAQ,CAAC+B,IAAI,CAAC,CAAE;cACrDnC,OAAO,EAAEA,OAAQ;cACjBuG,IAAI,EAAE,CAACvG,OAAO,GAAG,IAAI,GAAG,IAAK;cAC7BiG,SAAS,EAAC,OAAO;cAAAT,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGAhF,YAAY,iBACX5B,OAAA;QAAKiH,SAAS,EAAC,sFAAsF;QAAAT,QAAA,eACnGxG,OAAA,CAACd,QAAQ;UAAC+H,SAAS,EAAC,+CAA+C;UAAAT,QAAA,gBACjExG,OAAA;YAAKiH,SAAS,EAAC,wCAAwC;YAAAT,QAAA,gBACrDxG,OAAA;cAAIiH,SAAS,EAAC,qCAAqC;cAAAT,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5G,OAAA;cACEsH,OAAO,EAAEA,CAAA,KAAMzF,eAAe,CAAC,KAAK,CAAE;cACtCoF,SAAS,EAAC,uIAAuI;cAAAT,QAAA,EAClJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN5G,OAAA;YAAKiH,SAAS,EAAC,mDAAmD;YAAAT,QAAA,EAC/D9E;UAAc;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGA5F,OAAO,iBACNhB,OAAA;QAAKiH,SAAS,EAAC,yFAAyF;QAAAT,QAAA,eACtGxG,OAAA;UAAKiH,SAAS,EAAC,6BAA6B;UAAAT,QAAA,gBAC1CxG,OAAA,CAACT,WAAW;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACf5G,OAAA;YAAMiH,SAAS,EAAC,2BAA2B;YAAAT,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAzG,EAAA,CAtpBSD,QAAQ;EAAA,QACEvB,WAAW;AAAA;AAAAsL,EAAA,GADrB/J,QAAQ;AAupBjB,SAASgK,GAAGA,CAAA,EAAG;EACb,oBACElK,OAAA,CAACvB,MAAM;IAAA+H,QAAA,gBACLxG,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEpK,OAAA,CAACE,QAAQ;QAAAuG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzC5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,SAAS;MAACC,OAAO,eAAEpK,OAAA,CAACN,cAAc;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrD5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,mBAAmB;MAACC,OAAO,eAAEpK,OAAA,CAACL,eAAe;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChE5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,iBAAiB;MAACC,OAAO,eAAEpK,OAAA,CAACJ,iBAAiB;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChE5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,cAAc;MAACC,OAAO,eAAEpK,OAAA,CAACH,cAAc;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1D5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,YAAY;MAACC,OAAO,eAAEpK,OAAA,CAACF,aAAa;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvD5G,OAAA,CAACtB,KAAK;MAACyL,IAAI,EAAC,cAAc;MAACC,OAAO,eAAEpK,OAAA,CAACF,aAAa;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnD,CAAC;AAEb;AAACyD,GAAA,GAZQH,GAAG;AAcZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}