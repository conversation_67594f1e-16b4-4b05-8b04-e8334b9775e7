{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\YellowMindUI.js\",\n  _s = $RefreshSig$();\n/**\n * BrAInBI UI Components - Dark Theme\n * Composants avec le branding BrAInBI et thème violet-bleu\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\n// Header BrAInBI\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const BrAInBIHeader = ({\n  hasData,\n  sqlConfigured = false,\n  sqlConfig = null\n}) => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/brAInBI-logo.png\",\n            alt: \"BrAInBI Logo\",\n            className: \"w-8 h-8 rounded-lg cursor-pointer hover:opacity-80 transition-opacity\",\n            onClick: () => navigate('/')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-semibold text-gray-100 cursor-pointer hover:opacity-80 transition-opacity\",\n            onClick: () => navigate('/'),\n            children: [\"Br\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 17\n            }, this), \"nBI\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex items-center space-x-4\",\n            children: [hasData && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/dashboard'),\n                className: \"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCCA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 43,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/drag-visual'),\n                className: \"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Cr\\xE9er\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/tables'),\n                className: \"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCCB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Tables\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/config/sql'),\n              className: \"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Config\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: !sqlConfigured ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Configuration requise\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this) : hasData ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-400\",\n                children: sqlConfig ? `${sqlConfig.server}/${sqlConfig.database}` : 'SQL Server connecté'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-red-400 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Connexion SQL Server...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n\n// Titre principal avec dégradé\n_s(BrAInBIHeader, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = BrAInBIHeader;\nexport const HeroTitle = ({\n  title,\n  subtitle\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 to-pink-500 bg-clip-text text-transparent mb-4\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n\n// Bouton principal avec glow BrAInBI\n_c2 = HeroTitle;\nexport const PrimaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  loading = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `\n        bg-gradient-to-r from-purple-600 to-blue-600\n        hover:from-purple-700 hover:to-blue-700\n        disabled:bg-gray-700\n        text-white font-medium px-6 py-3 rounded-lg\n        transition-all duration-200\n        shadow-lg hover:shadow-purple-500/25 hover:shadow-xl\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `,\n    onClick: onClick,\n    disabled: disabled || loading,\n    ...props,\n    children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 9\n    }, this) : icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 5\n  }, this);\n};\n\n// Bouton secondaire transparent\n_c3 = PrimaryButton;\nexport const SecondaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `\n        bg-transparent hover:bg-gray-800/50 \n        border border-gray-600 hover:border-gray-500\n        text-gray-300 hover:text-gray-100\n        font-medium px-6 py-3 rounded-lg \n        transition-all duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `,\n    onClick: onClick,\n    disabled: disabled,\n    ...props,\n    children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 16\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n\n// Carte sombre avec ombres douces\n_c4 = SecondaryButton;\nexport const DarkCard = ({\n  children,\n  className = '',\n  hover = true,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        bg-gray-900/50 backdrop-blur-sm \n        border border-gray-800 \n        rounded-xl p-6 \n        shadow-xl shadow-black/20\n        ${hover ? 'hover:bg-gray-900/70 hover:border-gray-700 transition-all duration-200' : ''}\n        ${className}\n      `,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n\n// Carte KPI\n_c5 = DarkCard;\nexport const KPICard = ({\n  title,\n  value,\n  change,\n  icon,\n  trend = 'up',\n  onClick = null,\n  className = ''\n}) => {\n  const trendColor = trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400';\n  const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';\n  return /*#__PURE__*/_jsxDEV(DarkCard, {\n    className: `relative overflow-hidden ${onClick ? 'cursor-pointer hover:bg-gray-800/50 transition-colors' : ''} ${className}`,\n    onClick: onClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm font-medium mb-1\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-gray-100 mb-2\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), change && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center space-x-1 ${trendColor}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: trendIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), icon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl opacity-60\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-500/10 to-pink-500/10 rounded-full blur-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n\n// Chat Bubble\n_c6 = KPICard;\nexport const ChatBubble = ({\n  type = 'user',\n  // 'user' | 'ai' | 'system'\n  children,\n  timestamp,\n  className = ''\n}) => {\n  const isUser = type === 'user';\n  const isSystem = type === 'system';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n        max-w-[80%] rounded-xl px-4 py-3\n        ${isUser ? 'bg-indigo-600 text-white ml-12' : isSystem ? 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-200' : 'bg-gray-800 text-gray-100 mr-12'}\n        ${className}\n      `,\n      children: [!isUser && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-xs font-bold\",\n            children: \"AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-400\",\n          children: \"YellowMind Assistant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), timestamp && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs opacity-60 mt-2\",\n        children: timestamp\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 5\n  }, this);\n};\n\n// Input avec style sombre\n_c7 = ChatBubble;\nexport const DarkInput = ({\n  placeholder = '',\n  value = '',\n  onChange = () => {},\n  onKeyPress = () => {},\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [icon && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      className: `\n          w-full bg-gray-900/50 border border-gray-700 \n          focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500\n          text-gray-100 placeholder-gray-400\n          rounded-lg px-4 py-3 ${icon ? 'pl-10' : ''}\n          transition-all duration-200\n          disabled:opacity-50 disabled:cursor-not-allowed\n          ${className}\n        `,\n      placeholder: placeholder,\n      value: value,\n      onChange: onChange,\n      onKeyPress: onKeyPress,\n      disabled: disabled,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 297,\n    columnNumber: 5\n  }, this);\n};\n\n// Badge/Tag sombre\n_c8 = DarkInput;\nexport const DarkBadge = ({\n  children,\n  variant = 'default',\n  // 'default' | 'success' | 'warning' | 'error' | 'info'\n  size = 'sm',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-gray-800 text-gray-300 border-gray-700',\n    success: 'bg-green-900/50 text-green-300 border-green-700',\n    warning: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',\n    error: 'bg-red-900/50 text-red-300 border-red-700',\n    info: 'bg-blue-900/50 text-blue-300 border-blue-700'\n  };\n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: `\n        inline-flex items-center rounded-full border font-medium\n        ${variants[variant]}\n        ${sizes[size]}\n        ${className}\n      `,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n};\n\n// Loading Spinner sombre\n_c9 = DarkBadge;\nexport const DarkSpinner = ({\n  size = 'md',\n  className = ''\n}) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        border-2 border-gray-600 border-t-indigo-500 rounded-full animate-spin\n        ${sizes[size]}\n        ${className}\n      `\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 368,\n    columnNumber: 5\n  }, this);\n};\n\n// Section Header\n_c0 = DarkSpinner;\nexport const SectionHeader = ({\n  title,\n  subtitle = null,\n  action = null,\n  icon = null,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center justify-between mb-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3\",\n      children: [icon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-100\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm mt-1\",\n          children: subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this), action && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: action\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 18\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 387,\n    columnNumber: 5\n  }, this);\n};\n_c1 = SectionHeader;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"BrAInBIHeader\");\n$RefreshReg$(_c2, \"HeroTitle\");\n$RefreshReg$(_c3, \"PrimaryButton\");\n$RefreshReg$(_c4, \"SecondaryButton\");\n$RefreshReg$(_c5, \"DarkCard\");\n$RefreshReg$(_c6, \"KPICard\");\n$RefreshReg$(_c7, \"ChatBubble\");\n$RefreshReg$(_c8, \"DarkInput\");\n$RefreshReg$(_c9, \"DarkBadge\");\n$RefreshReg$(_c0, \"DarkSpinner\");\n$RefreshReg$(_c1, \"SectionHeader\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BrAInBIHeader", "hasData", "sqlConfigured", "sqlConfig", "_s", "navigate", "className", "children", "src", "alt", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "server", "database", "_c", "<PERSON><PERSON><PERSON><PERSON>", "title", "subtitle", "_c2", "PrimaryButton", "disabled", "loading", "icon", "props", "_c3", "SecondaryButton", "_c4", "DarkCard", "hover", "_c5", "KPICard", "value", "change", "trend", "trendColor", "trendIcon", "_c6", "ChatBubble", "type", "timestamp", "isUser", "isSystem", "_c7", "DarkInput", "placeholder", "onChange", "onKeyPress", "_c8", "DarkBadge", "variant", "size", "variants", "default", "success", "warning", "error", "info", "sizes", "sm", "md", "lg", "_c9", "Dark<PERSON><PERSON>ner", "_c0", "SectionHeader", "action", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/YellowMindUI.js"], "sourcesContent": ["/**\n * BrAInBI UI Components - Dark Theme\n * Composants avec le branding BrAInBI et thème violet-bleu\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\n// Header BrAInBI\nexport const BrAInBIHeader = ({ hasData, sqlConfigured = false, sqlConfig = null }) => {\n  const navigate = useNavigate();\n\n  return (\n    <header className=\"bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <img\n              src=\"/brAInBI-logo.png\"\n              alt=\"BrAInBI Logo\"\n              className=\"w-8 h-8 rounded-lg cursor-pointer hover:opacity-80 transition-opacity\"\n              onClick={() => navigate('/')}\n            />\n            <h1\n              className=\"text-xl font-semibold text-gray-100 cursor-pointer hover:opacity-80 transition-opacity\"\n              onClick={() => navigate('/')}\n            >\n              Br<span className=\"bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">AI</span>nBI\n            </h1>\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            <nav className=\"flex items-center space-x-4\">\n              {hasData && (\n                <>\n                  <button\n                    onClick={() => navigate('/dashboard')}\n                    className=\"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\"\n                  >\n                    <span>📊</span>\n                    <span>Dashboard</span>\n                  </button>\n                  <button\n                    onClick={() => navigate('/drag-visual')}\n                    className=\"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\"\n                  >\n                    <span>🎯</span>\n                    <span>Créer</span>\n                  </button>\n                  <button\n                    onClick={() => navigate('/tables')}\n                    className=\"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\"\n                  >\n                    <span>📋</span>\n                    <span>Tables</span>\n                  </button>\n                </>\n              )}\n              <button\n                onClick={() => navigate('/config/sql')}\n                className=\"text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1\"\n              >\n                <span>⚙️</span>\n                <span>Config</span>\n              </button>\n            </nav>\n\n            {/* Status */}\n            <div className=\"flex items-center space-x-4\">\n              {!sqlConfigured ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\n                  <span className=\"text-sm text-gray-400\">Configuration requise</span>\n                </div>\n              ) : hasData ? (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                  <span className=\"text-sm text-gray-400\">\n                    {sqlConfig ? `${sqlConfig.server}/${sqlConfig.database}` : 'SQL Server connecté'}\n                  </span>\n                </div>\n              ) : (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-red-400 rounded-full\"></div>\n                  <span className=\"text-sm text-gray-400\">Connexion SQL Server...</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// Titre principal avec dégradé\nexport const HeroTitle = ({ title, subtitle }) => {\n  return (\n    <div className=\"text-center py-12\">\n      <h1 className=\"text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 to-pink-500 bg-clip-text text-transparent mb-4\">\n        {title}\n      </h1>\n      {subtitle && (\n        <p className=\"text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed\">\n          {subtitle}\n        </p>\n      )}\n    </div>\n  );\n};\n\n// Bouton principal avec glow BrAInBI\nexport const PrimaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  loading = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return (\n    <button\n      className={`\n        bg-gradient-to-r from-purple-600 to-blue-600\n        hover:from-purple-700 hover:to-blue-700\n        disabled:bg-gray-700\n        text-white font-medium px-6 py-3 rounded-lg\n        transition-all duration-200\n        shadow-lg hover:shadow-purple-500/25 hover:shadow-xl\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `}\n      onClick={onClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading ? (\n        <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n      ) : icon && (\n        <span>{icon}</span>\n      )}\n      <span>{children}</span>\n    </button>\n  );\n};\n\n// Bouton secondaire transparent\nexport const SecondaryButton = ({ \n  children, \n  onClick, \n  disabled = false,\n  icon = null,\n  className = '',\n  ...props \n}) => {\n  return (\n    <button\n      className={`\n        bg-transparent hover:bg-gray-800/50 \n        border border-gray-600 hover:border-gray-500\n        text-gray-300 hover:text-gray-100\n        font-medium px-6 py-3 rounded-lg \n        transition-all duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `}\n      onClick={onClick}\n      disabled={disabled}\n      {...props}\n    >\n      {icon && <span>{icon}</span>}\n      <span>{children}</span>\n    </button>\n  );\n};\n\n// Carte sombre avec ombres douces\nexport const DarkCard = ({ \n  children, \n  className = '',\n  hover = true,\n  ...props \n}) => {\n  return (\n    <div\n      className={`\n        bg-gray-900/50 backdrop-blur-sm \n        border border-gray-800 \n        rounded-xl p-6 \n        shadow-xl shadow-black/20\n        ${hover ? 'hover:bg-gray-900/70 hover:border-gray-700 transition-all duration-200' : ''}\n        ${className}\n      `}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Carte KPI\nexport const KPICard = ({ title, value, change, icon, trend = 'up', onClick = null, className = '' }) => {\n  const trendColor = trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400';\n  const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';\n\n  return (\n    <DarkCard\n      className={`relative overflow-hidden ${onClick ? 'cursor-pointer hover:bg-gray-800/50 transition-colors' : ''} ${className}`}\n      onClick={onClick}\n    >\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-gray-400 text-sm font-medium mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-100 mb-2\">{value}</p>\n          {change && (\n            <div className={`flex items-center space-x-1 ${trendColor}`}>\n              <span className=\"text-xs\">{trendIcon}</span>\n              <span className=\"text-sm font-medium\">{change}</span>\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div className=\"text-2xl opacity-60\">\n            {icon}\n          </div>\n        )}\n      </div>\n      \n      {/* Gradient overlay */}\n      <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-500/10 to-pink-500/10 rounded-full blur-xl\"></div>\n    </DarkCard>\n  );\n};\n\n// Chat Bubble\nexport const ChatBubble = ({ \n  type = 'user', // 'user' | 'ai' | 'system'\n  children,\n  timestamp,\n  className = ''\n}) => {\n  const isUser = type === 'user';\n  const isSystem = type === 'system';\n  \n  return (\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>\n      <div className={`\n        max-w-[80%] rounded-xl px-4 py-3\n        ${isUser \n          ? 'bg-indigo-600 text-white ml-12' \n          : isSystem\n          ? 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-200'\n          : 'bg-gray-800 text-gray-100 mr-12'\n        }\n        ${className}\n      `}>\n        {!isUser && (\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <div className=\"w-6 h-6 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-xs font-bold\">AI</span>\n            </div>\n            <span className=\"text-xs text-gray-400\">YellowMind Assistant</span>\n          </div>\n        )}\n        \n        <div className=\"text-sm leading-relaxed whitespace-pre-wrap\">\n          {children}\n        </div>\n        \n        {timestamp && (\n          <div className=\"text-xs opacity-60 mt-2\">\n            {timestamp}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Input avec style sombre\nexport const DarkInput = ({ \n  placeholder = '',\n  value = '',\n  onChange = () => {},\n  onKeyPress = () => {},\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return (\n    <div className=\"relative\">\n      {icon && (\n        <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\">\n          {icon}\n        </div>\n      )}\n      <input\n        className={`\n          w-full bg-gray-900/50 border border-gray-700 \n          focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500\n          text-gray-100 placeholder-gray-400\n          rounded-lg px-4 py-3 ${icon ? 'pl-10' : ''}\n          transition-all duration-200\n          disabled:opacity-50 disabled:cursor-not-allowed\n          ${className}\n        `}\n        placeholder={placeholder}\n        value={value}\n        onChange={onChange}\n        onKeyPress={onKeyPress}\n        disabled={disabled}\n        {...props}\n      />\n    </div>\n  );\n};\n\n// Badge/Tag sombre\nexport const DarkBadge = ({ \n  children, \n  variant = 'default', // 'default' | 'success' | 'warning' | 'error' | 'info'\n  size = 'sm',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-gray-800 text-gray-300 border-gray-700',\n    success: 'bg-green-900/50 text-green-300 border-green-700',\n    warning: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',\n    error: 'bg-red-900/50 text-red-300 border-red-700',\n    info: 'bg-blue-900/50 text-blue-300 border-blue-700'\n  };\n  \n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n  \n  return (\n    <span\n      className={`\n        inline-flex items-center rounded-full border font-medium\n        ${variants[variant]}\n        ${sizes[size]}\n        ${className}\n      `}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Loading Spinner sombre\nexport const DarkSpinner = ({ size = 'md', className = '' }) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n  \n  return (\n    <div\n      className={`\n        border-2 border-gray-600 border-t-indigo-500 rounded-full animate-spin\n        ${sizes[size]}\n        ${className}\n      `}\n    />\n  );\n};\n\n// Section Header\nexport const SectionHeader = ({ \n  title, \n  subtitle = null, \n  action = null,\n  icon = null,\n  className = ''\n}) => {\n  return (\n    <div className={`flex items-center justify-between mb-6 ${className}`}>\n      <div className=\"flex items-center space-x-3\">\n        {icon && (\n          <div className=\"text-2xl\">\n            {icon}\n          </div>\n        )}\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-100\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-gray-400 text-sm mt-1\">\n              {subtitle}\n            </p>\n          )}\n        </div>\n      </div>\n      {action && <div>{action}</div>}\n    </div>\n  );\n};\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC,OAAO;EAAEC,aAAa,GAAG,KAAK;EAAEC,SAAS,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA;IAAQS,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eAC5FV,OAAA;MAAKS,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CV,OAAA;QAAKS,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAEhDV,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CV,OAAA;YACEW,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,cAAc;YAClBH,SAAS,EAAC,uEAAuE;YACjFI,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,GAAG;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFjB,OAAA;YACES,SAAS,EAAC,wFAAwF;YAClGI,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,GAAG,CAAE;YAAAE,QAAA,GAC9B,IACG,eAAAV,OAAA;cAAMS,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,OAC1G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNjB,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CV,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCN,OAAO,iBACNJ,OAAA,CAAAE,SAAA;cAAAQ,QAAA,gBACEV,OAAA;gBACEa,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,YAAY,CAAE;gBACtCC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,gBAErGV,OAAA;kBAAAU,QAAA,EAAM;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfjB,OAAA;kBAAAU,QAAA,EAAM;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACTjB,OAAA;gBACEa,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,cAAc,CAAE;gBACxCC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,gBAErGV,OAAA;kBAAAU,QAAA,EAAM;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfjB,OAAA;kBAAAU,QAAA,EAAM;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACTjB,OAAA;gBACEa,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,SAAS,CAAE;gBACnCC,SAAS,EAAC,2FAA2F;gBAAAC,QAAA,gBAErGV,OAAA;kBAAAU,QAAA,EAAM;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfjB,OAAA;kBAAAU,QAAA,EAAM;gBAAM;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA,eACT,CACH,eACDjB,OAAA;cACEa,OAAO,EAAEA,CAAA,KAAML,QAAQ,CAAC,aAAa,CAAE;cACvCC,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBAErGV,OAAA;gBAAAU,QAAA,EAAM;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACfjB,OAAA;gBAAAU,QAAA,EAAM;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjB,OAAA;YAAKS,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACzC,CAACL,aAAa,gBACbL,OAAA;cAAKS,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CV,OAAA;gBAAKS,SAAS,EAAC;cAAoC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1DjB,OAAA;gBAAMS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAqB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,GACJb,OAAO,gBACTJ,OAAA;cAAKS,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CV,OAAA;gBAAKS,SAAS,EAAC;cAAiD;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvEjB,OAAA;gBAAMS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpCJ,SAAS,GAAG,GAAGA,SAAS,CAACY,MAAM,IAAIZ,SAAS,CAACa,QAAQ,EAAE,GAAG;cAAqB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,gBAENjB,OAAA;cAAKS,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CV,OAAA;gBAAKS,SAAS,EAAC;cAAiC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDjB,OAAA;gBAAMS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;;AAED;AAAAV,EAAA,CAxFaJ,aAAa;EAAA,QACPL,WAAW;AAAA;AAAAsB,EAAA,GADjBjB,aAAa;AAyF1B,OAAO,MAAMkB,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAChD,oBACEvB,OAAA;IAAKS,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCV,OAAA;MAAIS,SAAS,EAAC,gHAAgH;MAAAC,QAAA,EAC3HY;IAAK;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EACJM,QAAQ,iBACPvB,OAAA;MAAGS,SAAS,EAAC,yDAAyD;MAAAC,QAAA,EACnEa;IAAQ;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAO,GAAA,GAfaH,SAAS;AAgBtB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAC5Bf,QAAQ;EACRG,OAAO;EACPa,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,IAAI,GAAG,IAAI;EACXnB,SAAS,GAAG,EAAE;EACd,GAAGoB;AACL,CAAC,KAAK;EACJ,oBACE7B,OAAA;IACES,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUA,SAAS;AACnB,OAAQ;IACFI,OAAO,EAAEA,OAAQ;IACjBa,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAAA,GAC1BE,KAAK;IAAAnB,QAAA,GAERiB,OAAO,gBACN3B,OAAA;MAAKS,SAAS,EAAC;IAA2E;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAC/FW,IAAI,iBACN5B,OAAA;MAAAU,QAAA,EAAOkB;IAAI;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACnB,eACDjB,OAAA;MAAAU,QAAA,EAAOA;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEb,CAAC;;AAED;AAAAa,GAAA,GApCaL,aAAa;AAqC1B,OAAO,MAAMM,eAAe,GAAGA,CAAC;EAC9BrB,QAAQ;EACRG,OAAO;EACPa,QAAQ,GAAG,KAAK;EAChBE,IAAI,GAAG,IAAI;EACXnB,SAAS,GAAG,EAAE;EACd,GAAGoB;AACL,CAAC,KAAK;EACJ,oBACE7B,OAAA;IACES,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUA,SAAS;AACnB,OAAQ;IACFI,OAAO,EAAEA,OAAQ;IACjBa,QAAQ,EAAEA,QAAS;IAAA,GACfG,KAAK;IAAAnB,QAAA,GAERkB,IAAI,iBAAI5B,OAAA;MAAAU,QAAA,EAAOkB;IAAI;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC5BjB,OAAA;MAAAU,QAAA,EAAOA;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEb,CAAC;;AAED;AAAAe,GAAA,GA9BaD,eAAe;AA+B5B,OAAO,MAAME,QAAQ,GAAGA,CAAC;EACvBvB,QAAQ;EACRD,SAAS,GAAG,EAAE;EACdyB,KAAK,GAAG,IAAI;EACZ,GAAGL;AACL,CAAC,KAAK;EACJ,oBACE7B,OAAA;IACES,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA,UAAUyB,KAAK,GAAG,wEAAwE,GAAG,EAAE;AAC/F,UAAUzB,SAAS;AACnB,OAAQ;IAAA,GACEoB,KAAK;IAAAnB,QAAA,EAERA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAkB,GAAA,GAvBaF,QAAQ;AAwBrB,OAAO,MAAMG,OAAO,GAAGA,CAAC;EAAEd,KAAK;EAAEe,KAAK;EAAEC,MAAM;EAAEV,IAAI;EAAEW,KAAK,GAAG,IAAI;EAAE1B,OAAO,GAAG,IAAI;EAAEJ,SAAS,GAAG;AAAG,CAAC,KAAK;EACvG,MAAM+B,UAAU,GAAGD,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAGA,KAAK,KAAK,MAAM,GAAG,cAAc,GAAG,eAAe;EAC1G,MAAME,SAAS,GAAGF,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGA,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;EAExE,oBACEvC,OAAA,CAACiC,QAAQ;IACPxB,SAAS,EAAE,4BAA4BI,OAAO,GAAG,uDAAuD,GAAG,EAAE,IAAIJ,SAAS,EAAG;IAC7HI,OAAO,EAAEA,OAAQ;IAAAH,QAAA,gBAEjBV,OAAA;MAAKS,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CV,OAAA;QAAKS,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBV,OAAA;UAAGS,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAEY;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEjB,OAAA;UAAGS,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAE2B;QAAK;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/DqB,MAAM,iBACLtC,OAAA;UAAKS,SAAS,EAAE,+BAA+B+B,UAAU,EAAG;UAAA9B,QAAA,gBAC1DV,OAAA;YAAMS,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAE+B;UAAS;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CjB,OAAA;YAAMS,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAE4B;UAAM;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACLW,IAAI,iBACH5B,OAAA;QAAKS,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EACjCkB;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNjB,OAAA;MAAKS,SAAS,EAAC;IAA2G;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzH,CAAC;AAEf,CAAC;;AAED;AAAAyB,GAAA,GAjCaN,OAAO;AAkCpB,OAAO,MAAMO,UAAU,GAAGA,CAAC;EACzBC,IAAI,GAAG,MAAM;EAAE;EACflC,QAAQ;EACRmC,SAAS;EACTpC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMqC,MAAM,GAAGF,IAAI,KAAK,MAAM;EAC9B,MAAMG,QAAQ,GAAGH,IAAI,KAAK,QAAQ;EAElC,oBACE5C,OAAA;IAAKS,SAAS,EAAE,QAAQqC,MAAM,GAAG,aAAa,GAAG,eAAe,OAAQ;IAAApC,QAAA,eACtEV,OAAA;MAAKS,SAAS,EAAE;AACtB;AACA,UAAUqC,MAAM,GACJ,gCAAgC,GAChCC,QAAQ,GACR,8DAA8D,GAC9D,iCAAiC;AAC7C,UACUtC,SAAS;AACnB,OAAQ;MAAAC,QAAA,GACC,CAACoC,MAAM,iBACN9C,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CV,OAAA;UAAKS,SAAS,EAAC,oGAAoG;UAAAC,QAAA,eACjHV,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNjB,OAAA;UAAMS,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN,eAEDjB,OAAA;QAAKS,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EACzDA;MAAQ;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL4B,SAAS,iBACR7C,OAAA;QAAKS,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACrCmC;MAAS;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA+B,GAAA,GA5CaL,UAAU;AA6CvB,OAAO,MAAMM,SAAS,GAAGA,CAAC;EACxBC,WAAW,GAAG,EAAE;EAChBb,KAAK,GAAG,EAAE;EACVc,QAAQ,GAAGA,CAAA,KAAM,CAAC,CAAC;EACnBC,UAAU,GAAGA,CAAA,KAAM,CAAC,CAAC;EACrB1B,QAAQ,GAAG,KAAK;EAChBE,IAAI,GAAG,IAAI;EACXnB,SAAS,GAAG,EAAE;EACd,GAAGoB;AACL,CAAC,KAAK;EACJ,oBACE7B,OAAA;IAAKS,SAAS,EAAC,UAAU;IAAAC,QAAA,GACtBkB,IAAI,iBACH5B,OAAA;MAAKS,SAAS,EAAC,kEAAkE;MAAAC,QAAA,EAC9EkB;IAAI;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eACDjB,OAAA;MACES,SAAS,EAAE;AACnB;AACA;AACA;AACA,iCAAiCmB,IAAI,GAAG,OAAO,GAAG,EAAE;AACpD;AACA;AACA,YAAYnB,SAAS;AACrB,SAAU;MACFyC,WAAW,EAAEA,WAAY;MACzBb,KAAK,EAAEA,KAAM;MACbc,QAAQ,EAAEA,QAAS;MACnBC,UAAU,EAAEA,UAAW;MACvB1B,QAAQ,EAAEA,QAAS;MAAA,GACfG;IAAK;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAoC,GAAA,GAtCaJ,SAAS;AAuCtB,OAAO,MAAMK,SAAS,GAAGA,CAAC;EACxB5C,QAAQ;EACR6C,OAAO,GAAG,SAAS;EAAE;EACrBC,IAAI,GAAG,IAAI;EACX/C,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMgD,QAAQ,GAAG;IACfC,OAAO,EAAE,2CAA2C;IACpDC,OAAO,EAAE,iDAAiD;IAC1DC,OAAO,EAAE,oDAAoD;IAC7DC,KAAK,EAAE,2CAA2C;IAClDC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,KAAK,GAAG;IACZC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;EAED,oBACElE,OAAA;IACES,SAAS,EAAE;AACjB;AACA,UAAUgD,QAAQ,CAACF,OAAO,CAAC;AAC3B,UAAUQ,KAAK,CAACP,IAAI,CAAC;AACrB,UAAU/C,SAAS;AACnB,OAAQ;IAAAC,QAAA,EAEDA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;;AAED;AAAAkD,GAAA,GAlCab,SAAS;AAmCtB,OAAO,MAAMc,WAAW,GAAGA,CAAC;EAAEZ,IAAI,GAAG,IAAI;EAAE/C,SAAS,GAAG;AAAG,CAAC,KAAK;EAC9D,MAAMsD,KAAK,GAAG;IACZC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,oBACElE,OAAA;IACES,SAAS,EAAE;AACjB;AACA,UAAUsD,KAAK,CAACP,IAAI,CAAC;AACrB,UAAU/C,SAAS;AACnB;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;;AAED;AAAAoD,GAAA,GAlBaD,WAAW;AAmBxB,OAAO,MAAME,aAAa,GAAGA,CAAC;EAC5BhD,KAAK;EACLC,QAAQ,GAAG,IAAI;EACfgD,MAAM,GAAG,IAAI;EACb3C,IAAI,GAAG,IAAI;EACXnB,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,oBACET,OAAA;IAAKS,SAAS,EAAE,0CAA0CA,SAAS,EAAG;IAAAC,QAAA,gBACpEV,OAAA;MAAKS,SAAS,EAAC,6BAA6B;MAAAC,QAAA,GACzCkB,IAAI,iBACH5B,OAAA;QAAKS,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtBkB;MAAI;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eACDjB,OAAA;QAAAU,QAAA,gBACEV,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChDY;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJM,QAAQ,iBACPvB,OAAA;UAAGS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtCa;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLsD,MAAM,iBAAIvE,OAAA;MAAAU,QAAA,EAAM6D;IAAM;MAAAzD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3B,CAAC;AAEV,CAAC;AAACuD,GAAA,GA7BWF,aAAa;AAAA,IAAAlD,EAAA,EAAAI,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAc,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAArD,EAAA;AAAAqD,YAAA,CAAAjD,GAAA;AAAAiD,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}