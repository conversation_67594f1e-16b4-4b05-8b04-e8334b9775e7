{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\YellowMindUI.js\";\n/**\n * BrAInBI UI Components - Dark Theme\n * Composants avec le branding BrAInBI et thème violet-bleu\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\n// Header BrAInBI\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const BrAInBIHeader = ({\n  hasData\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6 py-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/brAInBI-logo.png\",\n            alt: \"BrAInBI Logo\",\n            className: \"w-8 h-8 rounded-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-semibold text-gray-100\",\n            children: [\"Br\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 17\n            }, this), \"nBI\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: hasData ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-400\",\n              children: \"SQL Server connect\\xE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-red-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-400\",\n              children: \"Connexion SQL Server...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n\n// Titre principal avec dégradé\n_c = BrAInBIHeader;\nexport const HeroTitle = ({\n  title,\n  subtitle\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 to-pink-500 bg-clip-text text-transparent mb-4\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed\",\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n\n// Bouton principal avec glow BrAInBI\n_c2 = HeroTitle;\nexport const PrimaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  loading = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `\n        bg-gradient-to-r from-purple-600 to-blue-600\n        hover:from-purple-700 hover:to-blue-700\n        disabled:bg-gray-700\n        text-white font-medium px-6 py-3 rounded-lg\n        transition-all duration-200\n        shadow-lg hover:shadow-purple-500/25 hover:shadow-xl\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `,\n    onClick: onClick,\n    disabled: disabled || loading,\n    ...props,\n    children: [loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 9\n    }, this) : icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n\n// Bouton secondaire transparent\n_c3 = PrimaryButton;\nexport const SecondaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: `\n        bg-transparent hover:bg-gray-800/50 \n        border border-gray-600 hover:border-gray-500\n        text-gray-300 hover:text-gray-100\n        font-medium px-6 py-3 rounded-lg \n        transition-all duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `,\n    onClick: onClick,\n    disabled: disabled,\n    ...props,\n    children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 16\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n\n// Carte sombre avec ombres douces\n_c4 = SecondaryButton;\nexport const DarkCard = ({\n  children,\n  className = '',\n  hover = true,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        bg-gray-900/50 backdrop-blur-sm \n        border border-gray-800 \n        rounded-xl p-6 \n        shadow-xl shadow-black/20\n        ${hover ? 'hover:bg-gray-900/70 hover:border-gray-700 transition-all duration-200' : ''}\n        ${className}\n      `,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n};\n\n// Carte KPI\n_c5 = DarkCard;\nexport const KPICard = ({\n  title,\n  value,\n  change,\n  icon,\n  trend = 'up',\n  onClick = null,\n  className = ''\n}) => {\n  const trendColor = trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400';\n  const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';\n  return /*#__PURE__*/_jsxDEV(DarkCard, {\n    className: `relative overflow-hidden ${onClick ? 'cursor-pointer hover:bg-gray-800/50 transition-colors' : ''} ${className}`,\n    onClick: onClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm font-medium mb-1\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-2xl font-bold text-gray-100 mb-2\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), change && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center space-x-1 ${trendColor}`,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: trendIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: change\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), icon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl opacity-60\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-500/10 to-pink-500/10 rounded-full blur-xl\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n\n// Chat Bubble\n_c6 = KPICard;\nexport const ChatBubble = ({\n  type = 'user',\n  // 'user' | 'ai' | 'system'\n  children,\n  timestamp,\n  className = ''\n}) => {\n  const isUser = type === 'user';\n  const isSystem = type === 'system';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n        max-w-[80%] rounded-xl px-4 py-3\n        ${isUser ? 'bg-indigo-600 text-white ml-12' : isSystem ? 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-200' : 'bg-gray-800 text-gray-100 mr-12'}\n        ${className}\n      `,\n      children: [!isUser && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-xs font-bold\",\n            children: \"AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-400\",\n          children: \"YellowMind Assistant\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm leading-relaxed whitespace-pre-wrap\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), timestamp && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs opacity-60 mt-2\",\n        children: timestamp\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n\n// Input avec style sombre\n_c7 = ChatBubble;\nexport const DarkInput = ({\n  placeholder = '',\n  value = '',\n  onChange = () => {},\n  onKeyPress = () => {},\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [icon && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      className: `\n          w-full bg-gray-900/50 border border-gray-700 \n          focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500\n          text-gray-100 placeholder-gray-400\n          rounded-lg px-4 py-3 ${icon ? 'pl-10' : ''}\n          transition-all duration-200\n          disabled:opacity-50 disabled:cursor-not-allowed\n          ${className}\n        `,\n      placeholder: placeholder,\n      value: value,\n      onChange: onChange,\n      onKeyPress: onKeyPress,\n      disabled: disabled,\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n\n// Badge/Tag sombre\n_c8 = DarkInput;\nexport const DarkBadge = ({\n  children,\n  variant = 'default',\n  // 'default' | 'success' | 'warning' | 'error' | 'info'\n  size = 'sm',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-gray-800 text-gray-300 border-gray-700',\n    success: 'bg-green-900/50 text-green-300 border-green-700',\n    warning: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',\n    error: 'bg-red-900/50 text-red-300 border-red-700',\n    info: 'bg-blue-900/50 text-blue-300 border-blue-700'\n  };\n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: `\n        inline-flex items-center rounded-full border font-medium\n        ${variants[variant]}\n        ${sizes[size]}\n        ${className}\n      `,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n\n// Loading Spinner sombre\n_c9 = DarkBadge;\nexport const DarkSpinner = ({\n  size = 'md',\n  className = ''\n}) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        border-2 border-gray-600 border-t-indigo-500 rounded-full animate-spin\n        ${sizes[size]}\n        ${className}\n      `\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n};\n\n// Section Header\n_c0 = DarkSpinner;\nexport const SectionHeader = ({\n  title,\n  subtitle = null,\n  action = null,\n  icon = null,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center justify-between mb-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3\",\n      children: [icon && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl\",\n        children: icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-100\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm mt-1\",\n          children: subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), action && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: action\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 18\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 5\n  }, this);\n};\n_c1 = SectionHeader;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"BrAInBIHeader\");\n$RefreshReg$(_c2, \"HeroTitle\");\n$RefreshReg$(_c3, \"PrimaryButton\");\n$RefreshReg$(_c4, \"SecondaryButton\");\n$RefreshReg$(_c5, \"DarkCard\");\n$RefreshReg$(_c6, \"KPICard\");\n$RefreshReg$(_c7, \"ChatBubble\");\n$RefreshReg$(_c8, \"DarkInput\");\n$RefreshReg$(_c9, \"DarkBadge\");\n$RefreshReg$(_c0, \"DarkSpinner\");\n$RefreshReg$(_c1, \"SectionHeader\");", "map": {"version": 3, "names": ["React", "useNavigate", "jsxDEV", "_jsxDEV", "BrAInBIHeader", "hasData", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "<PERSON><PERSON><PERSON><PERSON>", "title", "subtitle", "_c2", "PrimaryButton", "onClick", "disabled", "loading", "icon", "props", "_c3", "SecondaryButton", "_c4", "DarkCard", "hover", "_c5", "KPICard", "value", "change", "trend", "trendColor", "trendIcon", "_c6", "ChatBubble", "type", "timestamp", "isUser", "isSystem", "_c7", "DarkInput", "placeholder", "onChange", "onKeyPress", "_c8", "DarkBadge", "variant", "size", "variants", "default", "success", "warning", "error", "info", "sizes", "sm", "md", "lg", "_c9", "Dark<PERSON><PERSON>ner", "_c0", "SectionHeader", "action", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/YellowMindUI.js"], "sourcesContent": ["/**\n * BrAInBI UI Components - Dark Theme\n * Composants avec le branding BrAInBI et thème violet-bleu\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\n\n// Header BrAInBI\nexport const BrAInBIHeader = ({ hasData }) => {\n  return (\n    <header className=\"bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <img\n              src=\"/brAInBI-logo.png\"\n              alt=\"BrAInBI Logo\"\n              className=\"w-8 h-8 rounded-lg\"\n            />\n            <h1 className=\"text-xl font-semibold text-gray-100\">\n              Br<span className=\"bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">AI</span>nBI\n            </h1>\n          </div>\n\n          {/* Status */}\n          <div className=\"flex items-center space-x-4\">\n            {hasData ? (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-gray-400\">SQL Server connecté</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-red-400 rounded-full\"></div>\n                <span className=\"text-sm text-gray-400\">Connexion SQL Server...</span>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// Titre principal avec dégradé\nexport const HeroTitle = ({ title, subtitle }) => {\n  return (\n    <div className=\"text-center py-12\">\n      <h1 className=\"text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 to-pink-500 bg-clip-text text-transparent mb-4\">\n        {title}\n      </h1>\n      {subtitle && (\n        <p className=\"text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed\">\n          {subtitle}\n        </p>\n      )}\n    </div>\n  );\n};\n\n// Bouton principal avec glow BrAInBI\nexport const PrimaryButton = ({\n  children,\n  onClick,\n  disabled = false,\n  loading = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return (\n    <button\n      className={`\n        bg-gradient-to-r from-purple-600 to-blue-600\n        hover:from-purple-700 hover:to-blue-700\n        disabled:bg-gray-700\n        text-white font-medium px-6 py-3 rounded-lg\n        transition-all duration-200\n        shadow-lg hover:shadow-purple-500/25 hover:shadow-xl\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `}\n      onClick={onClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading ? (\n        <div className=\"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n      ) : icon && (\n        <span>{icon}</span>\n      )}\n      <span>{children}</span>\n    </button>\n  );\n};\n\n// Bouton secondaire transparent\nexport const SecondaryButton = ({ \n  children, \n  onClick, \n  disabled = false,\n  icon = null,\n  className = '',\n  ...props \n}) => {\n  return (\n    <button\n      className={`\n        bg-transparent hover:bg-gray-800/50 \n        border border-gray-600 hover:border-gray-500\n        text-gray-300 hover:text-gray-100\n        font-medium px-6 py-3 rounded-lg \n        transition-all duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n        flex items-center space-x-2\n        ${className}\n      `}\n      onClick={onClick}\n      disabled={disabled}\n      {...props}\n    >\n      {icon && <span>{icon}</span>}\n      <span>{children}</span>\n    </button>\n  );\n};\n\n// Carte sombre avec ombres douces\nexport const DarkCard = ({ \n  children, \n  className = '',\n  hover = true,\n  ...props \n}) => {\n  return (\n    <div\n      className={`\n        bg-gray-900/50 backdrop-blur-sm \n        border border-gray-800 \n        rounded-xl p-6 \n        shadow-xl shadow-black/20\n        ${hover ? 'hover:bg-gray-900/70 hover:border-gray-700 transition-all duration-200' : ''}\n        ${className}\n      `}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Carte KPI\nexport const KPICard = ({ title, value, change, icon, trend = 'up', onClick = null, className = '' }) => {\n  const trendColor = trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400';\n  const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';\n\n  return (\n    <DarkCard\n      className={`relative overflow-hidden ${onClick ? 'cursor-pointer hover:bg-gray-800/50 transition-colors' : ''} ${className}`}\n      onClick={onClick}\n    >\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-gray-400 text-sm font-medium mb-1\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-100 mb-2\">{value}</p>\n          {change && (\n            <div className={`flex items-center space-x-1 ${trendColor}`}>\n              <span className=\"text-xs\">{trendIcon}</span>\n              <span className=\"text-sm font-medium\">{change}</span>\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div className=\"text-2xl opacity-60\">\n            {icon}\n          </div>\n        )}\n      </div>\n      \n      {/* Gradient overlay */}\n      <div className=\"absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-500/10 to-pink-500/10 rounded-full blur-xl\"></div>\n    </DarkCard>\n  );\n};\n\n// Chat Bubble\nexport const ChatBubble = ({ \n  type = 'user', // 'user' | 'ai' | 'system'\n  children,\n  timestamp,\n  className = ''\n}) => {\n  const isUser = type === 'user';\n  const isSystem = type === 'system';\n  \n  return (\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>\n      <div className={`\n        max-w-[80%] rounded-xl px-4 py-3\n        ${isUser \n          ? 'bg-indigo-600 text-white ml-12' \n          : isSystem\n          ? 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-200'\n          : 'bg-gray-800 text-gray-100 mr-12'\n        }\n        ${className}\n      `}>\n        {!isUser && (\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <div className=\"w-6 h-6 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-white text-xs font-bold\">AI</span>\n            </div>\n            <span className=\"text-xs text-gray-400\">YellowMind Assistant</span>\n          </div>\n        )}\n        \n        <div className=\"text-sm leading-relaxed whitespace-pre-wrap\">\n          {children}\n        </div>\n        \n        {timestamp && (\n          <div className=\"text-xs opacity-60 mt-2\">\n            {timestamp}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Input avec style sombre\nexport const DarkInput = ({ \n  placeholder = '',\n  value = '',\n  onChange = () => {},\n  onKeyPress = () => {},\n  disabled = false,\n  icon = null,\n  className = '',\n  ...props\n}) => {\n  return (\n    <div className=\"relative\">\n      {icon && (\n        <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\">\n          {icon}\n        </div>\n      )}\n      <input\n        className={`\n          w-full bg-gray-900/50 border border-gray-700 \n          focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500\n          text-gray-100 placeholder-gray-400\n          rounded-lg px-4 py-3 ${icon ? 'pl-10' : ''}\n          transition-all duration-200\n          disabled:opacity-50 disabled:cursor-not-allowed\n          ${className}\n        `}\n        placeholder={placeholder}\n        value={value}\n        onChange={onChange}\n        onKeyPress={onKeyPress}\n        disabled={disabled}\n        {...props}\n      />\n    </div>\n  );\n};\n\n// Badge/Tag sombre\nexport const DarkBadge = ({ \n  children, \n  variant = 'default', // 'default' | 'success' | 'warning' | 'error' | 'info'\n  size = 'sm',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-gray-800 text-gray-300 border-gray-700',\n    success: 'bg-green-900/50 text-green-300 border-green-700',\n    warning: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',\n    error: 'bg-red-900/50 text-red-300 border-red-700',\n    info: 'bg-blue-900/50 text-blue-300 border-blue-700'\n  };\n  \n  const sizes = {\n    sm: 'px-2 py-1 text-xs',\n    md: 'px-3 py-1 text-sm',\n    lg: 'px-4 py-2 text-base'\n  };\n  \n  return (\n    <span\n      className={`\n        inline-flex items-center rounded-full border font-medium\n        ${variants[variant]}\n        ${sizes[size]}\n        ${className}\n      `}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Loading Spinner sombre\nexport const DarkSpinner = ({ size = 'md', className = '' }) => {\n  const sizes = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n  \n  return (\n    <div\n      className={`\n        border-2 border-gray-600 border-t-indigo-500 rounded-full animate-spin\n        ${sizes[size]}\n        ${className}\n      `}\n    />\n  );\n};\n\n// Section Header\nexport const SectionHeader = ({ \n  title, \n  subtitle = null, \n  action = null,\n  icon = null,\n  className = ''\n}) => {\n  return (\n    <div className={`flex items-center justify-between mb-6 ${className}`}>\n      <div className=\"flex items-center space-x-3\">\n        {icon && (\n          <div className=\"text-2xl\">\n            {icon}\n          </div>\n        )}\n        <div>\n          <h2 className=\"text-xl font-semibold text-gray-100\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-gray-400 text-sm mt-1\">\n              {subtitle}\n            </p>\n          )}\n        </div>\n      </div>\n      {action && <div>{action}</div>}\n    </div>\n  );\n};\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC5C,oBACEF,OAAA;IAAQG,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eAC5FJ,OAAA;MAAKG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CJ,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAEhDJ,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CJ,OAAA;YACEK,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,cAAc;YAClBH,SAAS,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACFV,OAAA;YAAIG,SAAS,EAAC,qCAAqC;YAAAC,QAAA,GAAC,IAChD,eAAAJ,OAAA;cAAMG,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,OAC1G;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNV,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzCF,OAAO,gBACNF,OAAA;YAAKG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CJ,OAAA;cAAKG,SAAS,EAAC;YAAiD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEV,OAAA;cAAMG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,gBAENV,OAAA;YAAKG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CJ,OAAA;cAAKG,SAAS,EAAC;YAAiC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvDV,OAAA;cAAMG,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;;AAED;AAAAC,EAAA,GArCaV,aAAa;AAsC1B,OAAO,MAAMW,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAChD,oBACEd,OAAA;IAAKG,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAChCJ,OAAA;MAAIG,SAAS,EAAC,gHAAgH;MAAAC,QAAA,EAC3HS;IAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EACJI,QAAQ,iBACPd,OAAA;MAAGG,SAAS,EAAC,yDAAyD;MAAAC,QAAA,EACnEU;IAAQ;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAK,GAAA,GAfaH,SAAS;AAgBtB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAC5BZ,QAAQ;EACRa,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,IAAI,GAAG,IAAI;EACXjB,SAAS,GAAG,EAAE;EACd,GAAGkB;AACL,CAAC,KAAK;EACJ,oBACErB,OAAA;IACEG,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUA,SAAS;AACnB,OAAQ;IACFc,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAAA,GAC1BE,KAAK;IAAAjB,QAAA,GAERe,OAAO,gBACNnB,OAAA;MAAKG,SAAS,EAAC;IAA2E;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,GAC/FU,IAAI,iBACNpB,OAAA;MAAAI,QAAA,EAAOgB;IAAI;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACnB,eACDV,OAAA;MAAAI,QAAA,EAAOA;IAAQ;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEb,CAAC;;AAED;AAAAY,GAAA,GApCaN,aAAa;AAqC1B,OAAO,MAAMO,eAAe,GAAGA,CAAC;EAC9BnB,QAAQ;EACRa,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBE,IAAI,GAAG,IAAI;EACXjB,SAAS,GAAG,EAAE;EACd,GAAGkB;AACL,CAAC,KAAK;EACJ,oBACErB,OAAA;IACEG,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUA,SAAS;AACnB,OAAQ;IACFc,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IAAA,GACfG,KAAK;IAAAjB,QAAA,GAERgB,IAAI,iBAAIpB,OAAA;MAAAI,QAAA,EAAOgB;IAAI;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC5BV,OAAA;MAAAI,QAAA,EAAOA;IAAQ;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEb,CAAC;;AAED;AAAAc,GAAA,GA9BaD,eAAe;AA+B5B,OAAO,MAAME,QAAQ,GAAGA,CAAC;EACvBrB,QAAQ;EACRD,SAAS,GAAG,EAAE;EACduB,KAAK,GAAG,IAAI;EACZ,GAAGL;AACL,CAAC,KAAK;EACJ,oBACErB,OAAA;IACEG,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA,UAAUuB,KAAK,GAAG,wEAAwE,GAAG,EAAE;AAC/F,UAAUvB,SAAS;AACnB,OAAQ;IAAA,GACEkB,KAAK;IAAAjB,QAAA,EAERA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAiB,GAAA,GAvBaF,QAAQ;AAwBrB,OAAO,MAAMG,OAAO,GAAGA,CAAC;EAAEf,KAAK;EAAEgB,KAAK;EAAEC,MAAM;EAAEV,IAAI;EAAEW,KAAK,GAAG,IAAI;EAAEd,OAAO,GAAG,IAAI;EAAEd,SAAS,GAAG;AAAG,CAAC,KAAK;EACvG,MAAM6B,UAAU,GAAGD,KAAK,KAAK,IAAI,GAAG,gBAAgB,GAAGA,KAAK,KAAK,MAAM,GAAG,cAAc,GAAG,eAAe;EAC1G,MAAME,SAAS,GAAGF,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGA,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;EAExE,oBACE/B,OAAA,CAACyB,QAAQ;IACPtB,SAAS,EAAE,4BAA4Bc,OAAO,GAAG,uDAAuD,GAAG,EAAE,IAAId,SAAS,EAAG;IAC7Hc,OAAO,EAAEA,OAAQ;IAAAb,QAAA,gBAEjBJ,OAAA;MAAKG,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CJ,OAAA;QAAKG,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBJ,OAAA;UAAGG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAES;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEV,OAAA;UAAGG,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAEyB;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC/DoB,MAAM,iBACL9B,OAAA;UAAKG,SAAS,EAAE,+BAA+B6B,UAAU,EAAG;UAAA5B,QAAA,gBAC1DJ,OAAA;YAAMG,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAE6B;UAAS;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CV,OAAA;YAAMG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAE0B;UAAM;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACLU,IAAI,iBACHpB,OAAA;QAAKG,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EACjCgB;MAAI;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNV,OAAA;MAAKG,SAAS,EAAC;IAA2G;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzH,CAAC;AAEf,CAAC;;AAED;AAAAwB,GAAA,GAjCaN,OAAO;AAkCpB,OAAO,MAAMO,UAAU,GAAGA,CAAC;EACzBC,IAAI,GAAG,MAAM;EAAE;EACfhC,QAAQ;EACRiC,SAAS;EACTlC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMmC,MAAM,GAAGF,IAAI,KAAK,MAAM;EAC9B,MAAMG,QAAQ,GAAGH,IAAI,KAAK,QAAQ;EAElC,oBACEpC,OAAA;IAAKG,SAAS,EAAE,QAAQmC,MAAM,GAAG,aAAa,GAAG,eAAe,OAAQ;IAAAlC,QAAA,eACtEJ,OAAA;MAAKG,SAAS,EAAE;AACtB;AACA,UAAUmC,MAAM,GACJ,gCAAgC,GAChCC,QAAQ,GACR,8DAA8D,GAC9D,iCAAiC;AAC7C,UACUpC,SAAS;AACnB,OAAQ;MAAAC,QAAA,GACC,CAACkC,MAAM,iBACNtC,OAAA;QAAKG,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CJ,OAAA;UAAKG,SAAS,EAAC,oGAAoG;UAAAC,QAAA,eACjHJ,OAAA;YAAMG,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNV,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAoB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CACN,eAEDV,OAAA;QAAKG,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EACzDA;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL2B,SAAS,iBACRrC,OAAA;QAAKG,SAAS,EAAC,yBAAyB;QAAAC,QAAA,EACrCiC;MAAS;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA8B,GAAA,GA5CaL,UAAU;AA6CvB,OAAO,MAAMM,SAAS,GAAGA,CAAC;EACxBC,WAAW,GAAG,EAAE;EAChBb,KAAK,GAAG,EAAE;EACVc,QAAQ,GAAGA,CAAA,KAAM,CAAC,CAAC;EACnBC,UAAU,GAAGA,CAAA,KAAM,CAAC,CAAC;EACrB1B,QAAQ,GAAG,KAAK;EAChBE,IAAI,GAAG,IAAI;EACXjB,SAAS,GAAG,EAAE;EACd,GAAGkB;AACL,CAAC,KAAK;EACJ,oBACErB,OAAA;IAAKG,SAAS,EAAC,UAAU;IAAAC,QAAA,GACtBgB,IAAI,iBACHpB,OAAA;MAAKG,SAAS,EAAC,kEAAkE;MAAAC,QAAA,EAC9EgB;IAAI;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN,eACDV,OAAA;MACEG,SAAS,EAAE;AACnB;AACA;AACA;AACA,iCAAiCiB,IAAI,GAAG,OAAO,GAAG,EAAE;AACpD;AACA;AACA,YAAYjB,SAAS;AACrB,SAAU;MACFuC,WAAW,EAAEA,WAAY;MACzBb,KAAK,EAAEA,KAAM;MACbc,QAAQ,EAAEA,QAAS;MACnBC,UAAU,EAAEA,UAAW;MACvB1B,QAAQ,EAAEA,QAAS;MAAA,GACfG;IAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAmC,GAAA,GAtCaJ,SAAS;AAuCtB,OAAO,MAAMK,SAAS,GAAGA,CAAC;EACxB1C,QAAQ;EACR2C,OAAO,GAAG,SAAS;EAAE;EACrBC,IAAI,GAAG,IAAI;EACX7C,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAM8C,QAAQ,GAAG;IACfC,OAAO,EAAE,2CAA2C;IACpDC,OAAO,EAAE,iDAAiD;IAC1DC,OAAO,EAAE,oDAAoD;IAC7DC,KAAK,EAAE,2CAA2C;IAClDC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,KAAK,GAAG;IACZC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;EAED,oBACE1D,OAAA;IACEG,SAAS,EAAE;AACjB;AACA,UAAU8C,QAAQ,CAACF,OAAO,CAAC;AAC3B,UAAUQ,KAAK,CAACP,IAAI,CAAC;AACrB,UAAU7C,SAAS;AACnB,OAAQ;IAAAC,QAAA,EAEDA;EAAQ;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;;AAED;AAAAiD,GAAA,GAlCab,SAAS;AAmCtB,OAAO,MAAMc,WAAW,GAAGA,CAAC;EAAEZ,IAAI,GAAG,IAAI;EAAE7C,SAAS,GAAG;AAAG,CAAC,KAAK;EAC9D,MAAMoD,KAAK,GAAG;IACZC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,oBACE1D,OAAA;IACEG,SAAS,EAAE;AACjB;AACA,UAAUoD,KAAK,CAACP,IAAI,CAAC;AACrB,UAAU7C,SAAS;AACnB;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;;AAED;AAAAmD,GAAA,GAlBaD,WAAW;AAmBxB,OAAO,MAAME,aAAa,GAAGA,CAAC;EAC5BjD,KAAK;EACLC,QAAQ,GAAG,IAAI;EACfiD,MAAM,GAAG,IAAI;EACb3C,IAAI,GAAG,IAAI;EACXjB,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,oBACEH,OAAA;IAAKG,SAAS,EAAE,0CAA0CA,SAAS,EAAG;IAAAC,QAAA,gBACpEJ,OAAA;MAAKG,SAAS,EAAC,6BAA6B;MAAAC,QAAA,GACzCgB,IAAI,iBACHpB,OAAA;QAAKG,SAAS,EAAC,UAAU;QAAAC,QAAA,EACtBgB;MAAI;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eACDV,OAAA;QAAAI,QAAA,gBACEJ,OAAA;UAAIG,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAChDS;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACJI,QAAQ,iBACPd,OAAA;UAAGG,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EACtCU;QAAQ;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACLqD,MAAM,iBAAI/D,OAAA;MAAAI,QAAA,EAAM2D;IAAM;MAAAxD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3B,CAAC;AAEV,CAAC;AAACsD,GAAA,GA7BWF,aAAa;AAAA,IAAAnD,EAAA,EAAAI,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAc,GAAA,EAAAE,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAtD,EAAA;AAAAsD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}