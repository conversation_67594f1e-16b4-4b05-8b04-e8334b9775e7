/**
 * BrAInBI UI Components - Dark Theme
 * Composants avec le branding BrAInBI et thème violet-bleu
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';

// Header BrAInBI
export const BrAInBIHeader = ({ hasData }) => {
  const navigate = useNavigate();

  return (
    <header className="bg-gray-900/50 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img
              src="/brAInBI-logo.png"
              alt="BrAInBI Logo"
              className="w-8 h-8 rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => navigate('/')}
            />
            <h1
              className="text-xl font-semibold text-gray-100 cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => navigate('/')}
            >
              Br<span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">AI</span>nBI
            </h1>
          </div>

          {/* Navigation */}
          <div className="flex items-center space-x-6">
            <nav className="flex items-center space-x-4">
              {hasData && (
                <>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1"
                  >
                    <span>📊</span>
                    <span>Dashboard</span>
                  </button>
                  <button
                    onClick={() => navigate('/drag-visual')}
                    className="text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1"
                  >
                    <span>🎯</span>
                    <span>Créer</span>
                  </button>
                  <button
                    onClick={() => navigate('/tables')}
                    className="text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1"
                  >
                    <span>📋</span>
                    <span>Tables</span>
                  </button>
                </>
              )}
              <button
                onClick={() => navigate('/config/sql')}
                className="text-sm text-gray-400 hover:text-purple-400 transition-colors flex items-center space-x-1"
              >
                <span>⚙️</span>
                <span>Config</span>
              </button>
            </nav>

            {/* Status */}
            <div className="flex items-center space-x-4">
              {hasData ? (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-400">SQL Server connecté</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  <span className="text-sm text-gray-400">Connexion SQL Server...</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Titre principal avec dégradé
export const HeroTitle = ({ title, subtitle }) => {
  return (
    <div className="text-center py-12">
      <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-indigo-400 to-pink-500 bg-clip-text text-transparent mb-4">
        {title}
      </h1>
      {subtitle && (
        <p className="text-xl text-gray-400 max-w-2xl mx-auto leading-relaxed">
          {subtitle}
        </p>
      )}
    </div>
  );
};

// Bouton principal avec glow BrAInBI
export const PrimaryButton = ({
  children,
  onClick,
  disabled = false,
  loading = false,
  icon = null,
  className = '',
  ...props
}) => {
  return (
    <button
      className={`
        bg-gradient-to-r from-purple-600 to-blue-600
        hover:from-purple-700 hover:to-blue-700
        disabled:bg-gray-700
        text-white font-medium px-6 py-3 rounded-lg
        transition-all duration-200
        shadow-lg hover:shadow-purple-500/25 hover:shadow-xl
        disabled:opacity-50 disabled:cursor-not-allowed
        flex items-center space-x-2
        ${className}
      `}
      onClick={onClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
      ) : icon && (
        <span>{icon}</span>
      )}
      <span>{children}</span>
    </button>
  );
};

// Bouton secondaire transparent
export const SecondaryButton = ({ 
  children, 
  onClick, 
  disabled = false,
  icon = null,
  className = '',
  ...props 
}) => {
  return (
    <button
      className={`
        bg-transparent hover:bg-gray-800/50 
        border border-gray-600 hover:border-gray-500
        text-gray-300 hover:text-gray-100
        font-medium px-6 py-3 rounded-lg 
        transition-all duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        flex items-center space-x-2
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {icon && <span>{icon}</span>}
      <span>{children}</span>
    </button>
  );
};

// Carte sombre avec ombres douces
export const DarkCard = ({ 
  children, 
  className = '',
  hover = true,
  ...props 
}) => {
  return (
    <div
      className={`
        bg-gray-900/50 backdrop-blur-sm 
        border border-gray-800 
        rounded-xl p-6 
        shadow-xl shadow-black/20
        ${hover ? 'hover:bg-gray-900/70 hover:border-gray-700 transition-all duration-200' : ''}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

// Carte KPI
export const KPICard = ({ title, value, change, icon, trend = 'up', onClick = null, className = '' }) => {
  const trendColor = trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400';
  const trendIcon = trend === 'up' ? '↗️' : trend === 'down' ? '↘️' : '➡️';

  return (
    <DarkCard
      className={`relative overflow-hidden ${onClick ? 'cursor-pointer hover:bg-gray-800/50 transition-colors' : ''} ${className}`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-gray-400 text-sm font-medium mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-100 mb-2">{value}</p>
          {change && (
            <div className={`flex items-center space-x-1 ${trendColor}`}>
              <span className="text-xs">{trendIcon}</span>
              <span className="text-sm font-medium">{change}</span>
            </div>
          )}
        </div>
        {icon && (
          <div className="text-2xl opacity-60">
            {icon}
          </div>
        )}
      </div>
      
      {/* Gradient overlay */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-indigo-500/10 to-pink-500/10 rounded-full blur-xl"></div>
    </DarkCard>
  );
};

// Chat Bubble
export const ChatBubble = ({ 
  type = 'user', // 'user' | 'ai' | 'system'
  children,
  timestamp,
  className = ''
}) => {
  const isUser = type === 'user';
  const isSystem = type === 'system';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`
        max-w-[80%] rounded-xl px-4 py-3
        ${isUser 
          ? 'bg-indigo-600 text-white ml-12' 
          : isSystem
          ? 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-200'
          : 'bg-gray-800 text-gray-100 mr-12'
        }
        ${className}
      `}>
        {!isUser && (
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-6 h-6 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">AI</span>
            </div>
            <span className="text-xs text-gray-400">YellowMind Assistant</span>
          </div>
        )}
        
        <div className="text-sm leading-relaxed whitespace-pre-wrap">
          {children}
        </div>
        
        {timestamp && (
          <div className="text-xs opacity-60 mt-2">
            {timestamp}
          </div>
        )}
      </div>
    </div>
  );
};

// Input avec style sombre
export const DarkInput = ({ 
  placeholder = '',
  value = '',
  onChange = () => {},
  onKeyPress = () => {},
  disabled = false,
  icon = null,
  className = '',
  ...props
}) => {
  return (
    <div className="relative">
      {icon && (
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}
      <input
        className={`
          w-full bg-gray-900/50 border border-gray-700 
          focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500
          text-gray-100 placeholder-gray-400
          rounded-lg px-4 py-3 ${icon ? 'pl-10' : ''}
          transition-all duration-200
          disabled:opacity-50 disabled:cursor-not-allowed
          ${className}
        `}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyPress={onKeyPress}
        disabled={disabled}
        {...props}
      />
    </div>
  );
};

// Badge/Tag sombre
export const DarkBadge = ({ 
  children, 
  variant = 'default', // 'default' | 'success' | 'warning' | 'error' | 'info'
  size = 'sm',
  className = ''
}) => {
  const variants = {
    default: 'bg-gray-800 text-gray-300 border-gray-700',
    success: 'bg-green-900/50 text-green-300 border-green-700',
    warning: 'bg-yellow-900/50 text-yellow-300 border-yellow-700',
    error: 'bg-red-900/50 text-red-300 border-red-700',
    info: 'bg-blue-900/50 text-blue-300 border-blue-700'
  };
  
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };
  
  return (
    <span
      className={`
        inline-flex items-center rounded-full border font-medium
        ${variants[variant]}
        ${sizes[size]}
        ${className}
      `}
    >
      {children}
    </span>
  );
};

// Loading Spinner sombre
export const DarkSpinner = ({ size = 'md', className = '' }) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };
  
  return (
    <div
      className={`
        border-2 border-gray-600 border-t-indigo-500 rounded-full animate-spin
        ${sizes[size]}
        ${className}
      `}
    />
  );
};

// Section Header
export const SectionHeader = ({ 
  title, 
  subtitle = null, 
  action = null,
  icon = null,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between mb-6 ${className}`}>
      <div className="flex items-center space-x-3">
        {icon && (
          <div className="text-2xl">
            {icon}
          </div>
        )}
        <div>
          <h2 className="text-xl font-semibold text-gray-100">
            {title}
          </h2>
          {subtitle && (
            <p className="text-gray-400 text-sm mt-1">
              {subtitle}
            </p>
          )}
        </div>
      </div>
      {action && <div>{action}</div>}
    </div>
  );
};
