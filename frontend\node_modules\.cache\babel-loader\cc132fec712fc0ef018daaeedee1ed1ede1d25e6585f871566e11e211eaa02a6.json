{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\SQLConfigPage.js\",\n  _s = $RefreshSig$();\n/**\n * SQLConfigPage - Page de configuration de la base de données SQL Server\n * Permet de configurer dynamiquement la connexion à SQL Server\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { BrAInBIHeader, DarkCard, SectionHeader, PrimaryButton, SecondaryButton, DarkInput, DarkSpinner } from './YellowMindUI';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\nconst SQLConfigPage = () => {\n  _s();\n  var _testResult$server_in, _testResult$connectio;\n  const navigate = useNavigate();\n\n  // États du formulaire\n  const [config, setConfig] = useState({\n    server: 'localhost\\\\SQLSERVER',\n    database: '',\n    driver: 'ODBC Driver 17 for SQL Server',\n    username: '',\n    password: '',\n    use_windows_auth: true,\n    port: '',\n    timeout: 30\n  });\n\n  // États de l'interface\n  const [loading, setLoading] = useState(false);\n  const [testing, setTesting] = useState(false);\n  const [testResult, setTestResult] = useState(null);\n  const [currentConfig, setCurrentConfig] = useState(null);\n  const [toast, setToast] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 5000);\n  };\n\n  // Fonction utilitaire pour extraire les messages d'erreur\n  const extractErrorMessage = error => {\n    var _error$response, _error$response$data;\n    let errorMsg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n\n    // Si l'erreur est un objet (erreur de validation Pydantic), la convertir en chaîne\n    if (typeof errorMsg === 'object') {\n      if (Array.isArray(errorMsg)) {\n        errorMsg = errorMsg.map(err => {\n          if (typeof err === 'object') {\n            return err.msg || `${err.type}: ${err.input}` || JSON.stringify(err);\n          }\n          return err;\n        }).join(', ');\n      } else {\n        errorMsg = errorMsg.msg || `${errorMsg.type}: ${errorMsg.input}` || JSON.stringify(errorMsg);\n      }\n    }\n    return String(errorMsg);\n  };\n\n  // Charger la configuration actuelle au démarrage\n  useEffect(() => {\n    loadCurrentConfig();\n  }, []);\n  const loadCurrentConfig = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      if (response.data.configured) {\n        setCurrentConfig(response.data.config);\n        // Pré-remplir le formulaire avec la config actuelle (sans le mot de passe)\n        setConfig(prev => ({\n          ...prev,\n          ...response.data.config,\n          password: '' // Ne pas pré-remplir le mot de passe pour la sécurité\n        }));\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement de la configuration:', error);\n    }\n  };\n\n  // Gérer les changements dans le formulaire\n  const handleInputChange = (field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Réinitialiser le résultat du test quand on modifie la config\n    setTestResult(null);\n  };\n\n  // Tester la connexion\n  const testConnection = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n    try {\n      setTesting(true);\n      setTestResult(null);\n      const response = await axios.post(`${API_BASE_URL}/config/sql/test`, config);\n      setTestResult(response.data);\n      if (response.data.success) {\n        var _response$data$connec;\n        showToast(`Connexion réussie ! (${(_response$data$connec = response.data.connection_time) === null || _response$data$connec === void 0 ? void 0 : _response$data$connec.toFixed(2)}s)`);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = extractErrorMessage(error);\n      setTestResult({\n        success: false,\n        message: errorMsg,\n        error_code: 'REQUEST_ERROR'\n      });\n      showToast('Erreur lors du test: ' + errorMsg, 'error');\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  // Sauvegarder la configuration\n  const saveConfiguration = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await axios.post(`${API_BASE_URL}/config/sql`, config);\n      if (response.data.success) {\n        showToast('Configuration sauvegardée avec succès !');\n        setCurrentConfig(response.data.config);\n\n        // Rediriger vers la page principale après un délai\n        setTimeout(() => {\n          navigate('/');\n        }, 2000);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = extractErrorMessage(error);\n      showToast('Erreur lors de la sauvegarde: ' + errorMsg, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-950 text-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n      hasData: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n              children: \"Configuration SQL Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Configurez votre connexion \\xE0 la base de donn\\xE9es SQL Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this), currentConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-400\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-green-300\",\n              children: \"Configuration actuelle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Serveur: \", currentConfig.server]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Base: \", currentConfig.database]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Authentification: \", currentConfig.use_windows_auth ? 'Windows' : 'SQL Server']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), toast.show && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fixed top-4 right-4 z-50 p-4 rounded-lg border ${toast.type === 'success' ? 'bg-green-900/90 border-green-700 text-green-100' : 'bg-red-900/90 border-red-700 text-red-100'} backdrop-blur-sm`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: toast.type === 'success' ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: toast.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Param\\xE8tres de connexion\",\n          subtitle: \"Configurez les d\\xE9tails de votre serveur SQL Server\",\n          icon: \"\\uD83D\\uDD27\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Serveur SQL Server *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.server,\n                onChange: e => handleInputChange('server', e.target.value),\n                placeholder: \"localhost\\\\\\\\SQLSERVER\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Exemple: localhost\\\\\\\\SQLSERVER, *************, myserver.domain.com\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Base de donn\\xE9es *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.database,\n                onChange: e => handleInputChange('database', e.target.value),\n                placeholder: \"BrAInBIDemo\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Driver ODBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.driver,\n                onChange: e => handleInputChange('driver', e.target.value),\n                placeholder: \"ODBC Driver 17 for SQL Server\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Port (optionnel)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"number\",\n                value: config.port,\n                onChange: e => handleInputChange('port', e.target.value),\n                placeholder: \"1433\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-3\",\n              children: \"Type d'authentification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center space-x-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  checked: config.use_windows_auth,\n                  onChange: () => handleInputChange('use_windows_auth', true),\n                  className: \"text-purple-500 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Authentification Windows\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center space-x-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  checked: !config.use_windows_auth,\n                  onChange: () => handleInputChange('use_windows_auth', false),\n                  className: \"text-purple-500 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Authentification SQL Server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), !config.use_windows_auth && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Nom d'utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.username,\n                onChange: e => handleInputChange('username', e.target.value),\n                placeholder: \"sa\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"password\",\n                value: config.password,\n                onChange: e => handleInputChange('password', e.target.value),\n                placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full md:w-1/2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Timeout de connexion (secondes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n              type: \"number\",\n              value: config.timeout,\n              onChange: e => handleInputChange('timeout', parseInt(e.target.value) || 30),\n              placeholder: \"30\",\n              className: \"w-full\",\n              min: \"5\",\n              max: \"300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mt-6 p-4 rounded-lg border ${testResult.success ? 'bg-green-900/20 border-green-700' : 'bg-red-900/20 border-red-700'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: testResult.success ? '✅' : '❌'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `font-medium ${testResult.success ? 'text-green-300' : 'text-red-300'}`,\n              children: testResult.success ? 'Test réussi' : 'Test échoué'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-300 mb-2\",\n            children: testResult.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), testResult.success && testResult.server_info && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Version: \", (_testResult$server_in = testResult.server_info.version) === null || _testResult$server_in === void 0 ? void 0 : _testResult$server_in.substring(0, 50), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Base actuelle: \", testResult.server_info.current_database]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Temps de connexion: \", (_testResult$connectio = testResult.connection_time) === null || _testResult$connectio === void 0 ? void 0 : _testResult$connectio.toFixed(3), \"s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 17\n          }, this), !testResult.success && testResult.error_code && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-red-400\",\n            children: [\"Code d'erreur: \", testResult.error_code]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4 mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n            onClick: () => navigate('/'),\n            icon: \"\\uD83C\\uDFE0\",\n            disabled: loading || testing,\n            children: \"Retour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n            onClick: testConnection,\n            icon: testing ? null : \"🔍\",\n            disabled: loading || testing || !config.server || !config.database,\n            loading: testing,\n            children: testing ? 'Test en cours...' : 'Tester la connexion'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n            onClick: saveConfiguration,\n            icon: loading ? null : \"💾\",\n            disabled: loading || testing || !config.server || !config.database,\n            loading: loading,\n            className: \"flex-1\",\n            children: loading ? 'Sauvegarde...' : 'Valider la configuration'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n        className: \"mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Aide \\xE0 la configuration\",\n          subtitle: \"Conseils pour configurer votre connexion SQL Server\",\n          icon: \"\\uD83D\\uDCA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 space-y-4 text-sm text-gray-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDD27 Formats de serveur courants :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"localhost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 23\n                }, this), \" - Instance par d\\xE9faut locale\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"localhost\\\\\\\\SQLEXPRESS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this), \" - Instance nomm\\xE9e locale\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"*************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 23\n                }, this), \" - Serveur distant par IP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"server.domain.com\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 23\n                }, this), \" - Serveur distant par nom\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDD10 Authentification :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Windows\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this), \" : Utilise votre compte Windows actuel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SQL Server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 23\n                }, this), \" : N\\xE9cessite un nom d'utilisateur et mot de passe SQL\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\u26A0\\uFE0F D\\xE9pannage :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 V\\xE9rifiez que SQL Server est d\\xE9marr\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Assurez-vous que TCP/IP est activ\\xE9 dans SQL Server Configuration Manager\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 V\\xE9rifiez les param\\xE8tres de pare-feu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Testez d'abord avec SQL Server Management Studio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(SQLConfigPage, \"Cv1qlQ0i95Murl/syuqCbzUmc9Y=\", false, function () {\n  return [useNavigate];\n});\n_c = SQLConfigPage;\nexport default SQLConfigPage;\nvar _c;\n$RefreshReg$(_c, \"SQLConfigPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "BrAInBIHeader", "DarkCard", "SectionHeader", "PrimaryButton", "SecondaryButton", "DarkInput", "Dark<PERSON><PERSON>ner", "jsxDEV", "_jsxDEV", "API_BASE_URL", "SQLConfigPage", "_s", "_testResult$server_in", "_testResult$connectio", "navigate", "config", "setConfig", "server", "database", "driver", "username", "password", "use_windows_auth", "port", "timeout", "loading", "setLoading", "testing", "setTesting", "testResult", "setTestResult", "currentConfig", "setCurrentConfig", "toast", "setToast", "show", "message", "type", "showToast", "setTimeout", "extractErrorMessage", "error", "_error$response", "_error$response$data", "errorMsg", "response", "data", "detail", "Array", "isArray", "map", "err", "msg", "input", "JSON", "stringify", "join", "String", "loadCurrentConfig", "get", "configured", "prev", "console", "handleInputChange", "field", "value", "testConnection", "post", "success", "_response$data$connec", "connection_time", "toFixed", "error_code", "saveConfiguration", "className", "children", "hasData", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subtitle", "icon", "onChange", "e", "target", "placeholder", "checked", "parseInt", "min", "max", "server_info", "version", "substring", "current_database", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/SQLConfigPage.js"], "sourcesContent": ["/**\n * SQLConfigPage - Page de configuration de la base de données SQL Server\n * Permet de configurer dynamiquement la connexion à SQL Server\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  BrAInBIHeader,\n  DarkCard,\n  SectionHeader,\n  PrimaryButton,\n  SecondaryButton,\n  DarkInput,\n  DarkSpinner\n} from './YellowMindUI';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\nconst SQLConfigPage = () => {\n  const navigate = useNavigate();\n  \n  // États du formulaire\n  const [config, setConfig] = useState({\n    server: 'localhost\\\\SQLSERVER',\n    database: '',\n    driver: 'ODBC Driver 17 for SQL Server',\n    username: '',\n    password: '',\n    use_windows_auth: true,\n    port: '',\n    timeout: 30\n  });\n\n  // États de l'interface\n  const [loading, setLoading] = useState(false);\n  const [testing, setTesting] = useState(false);\n  const [testResult, setTestResult] = useState(null);\n  const [currentConfig, setCurrentConfig] = useState(null);\n  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({ show: true, message, type });\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 5000);\n  };\n\n  // Fonction utilitaire pour extraire les messages d'erreur\n  const extractErrorMessage = (error) => {\n    let errorMsg = error.response?.data?.detail || error.message;\n\n    // Si l'erreur est un objet (erreur de validation Pydantic), la convertir en chaîne\n    if (typeof errorMsg === 'object') {\n      if (Array.isArray(errorMsg)) {\n        errorMsg = errorMsg.map(err => {\n          if (typeof err === 'object') {\n            return err.msg || `${err.type}: ${err.input}` || JSON.stringify(err);\n          }\n          return err;\n        }).join(', ');\n      } else {\n        errorMsg = errorMsg.msg || `${errorMsg.type}: ${errorMsg.input}` || JSON.stringify(errorMsg);\n      }\n    }\n\n    return String(errorMsg);\n  };\n\n  // Charger la configuration actuelle au démarrage\n  useEffect(() => {\n    loadCurrentConfig();\n  }, []);\n\n  const loadCurrentConfig = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      if (response.data.configured) {\n        setCurrentConfig(response.data.config);\n        // Pré-remplir le formulaire avec la config actuelle (sans le mot de passe)\n        setConfig(prev => ({\n          ...prev,\n          ...response.data.config,\n          password: '' // Ne pas pré-remplir le mot de passe pour la sécurité\n        }));\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement de la configuration:', error);\n    }\n  };\n\n  // Gérer les changements dans le formulaire\n  const handleInputChange = (field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Réinitialiser le résultat du test quand on modifie la config\n    setTestResult(null);\n  };\n\n  // Tester la connexion\n  const testConnection = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n\n    try {\n      setTesting(true);\n      setTestResult(null);\n\n      const response = await axios.post(`${API_BASE_URL}/config/sql/test`, config);\n      setTestResult(response.data);\n\n      if (response.data.success) {\n        showToast(`Connexion réussie ! (${response.data.connection_time?.toFixed(2)}s)`);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = extractErrorMessage(error);\n\n      setTestResult({\n        success: false,\n        message: errorMsg,\n        error_code: 'REQUEST_ERROR'\n      });\n      showToast('Erreur lors du test: ' + errorMsg, 'error');\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  // Sauvegarder la configuration\n  const saveConfiguration = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const response = await axios.post(`${API_BASE_URL}/config/sql`, config);\n\n      if (response.data.success) {\n        showToast('Configuration sauvegardée avec succès !');\n        setCurrentConfig(response.data.config);\n        \n        // Rediriger vers la page principale après un délai\n        setTimeout(() => {\n          navigate('/');\n        }, 2000);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = extractErrorMessage(error);\n      showToast('Erreur lors de la sauvegarde: ' + errorMsg, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-950 text-gray-100\">\n      <BrAInBIHeader hasData={false} />\n      \n      <div className=\"max-w-4xl mx-auto px-6 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-3 mb-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\">\n              <span className=\"text-xl\">⚙️</span>\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                Configuration SQL Server\n              </h1>\n              <p className=\"text-gray-400\">\n                Configurez votre connexion à la base de données SQL Server\n              </p>\n            </div>\n          </div>\n\n          {/* État de la configuration actuelle */}\n          {currentConfig && (\n            <div className=\"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-green-400\">✅</span>\n                <span className=\"font-medium text-green-300\">Configuration actuelle</span>\n              </div>\n              <div className=\"text-sm text-gray-300\">\n                <div>Serveur: {currentConfig.server}</div>\n                <div>Base: {currentConfig.database}</div>\n                <div>Authentification: {currentConfig.use_windows_auth ? 'Windows' : 'SQL Server'}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Toast de notification */}\n        {toast.show && (\n          <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${\n            toast.type === 'success' \n              ? 'bg-green-900/90 border-green-700 text-green-100' \n              : 'bg-red-900/90 border-red-700 text-red-100'\n          } backdrop-blur-sm`}>\n            <div className=\"flex items-center space-x-2\">\n              <span>{toast.type === 'success' ? '✅' : '❌'}</span>\n              <span>{toast.message}</span>\n            </div>\n          </div>\n        )}\n\n        {/* Formulaire de configuration */}\n        <DarkCard>\n          <SectionHeader\n            title=\"Paramètres de connexion\"\n            subtitle=\"Configurez les détails de votre serveur SQL Server\"\n            icon=\"🔧\"\n          />\n\n          <div className=\"mt-6 space-y-6\">\n            {/* Serveur */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Serveur SQL Server *\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.server}\n                  onChange={(e) => handleInputChange('server', e.target.value)}\n                  placeholder=\"localhost\\\\SQLSERVER\"\n                  className=\"w-full\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Exemple: localhost\\\\SQLSERVER, *************, myserver.domain.com\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Base de données *\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.database}\n                  onChange={(e) => handleInputChange('database', e.target.value)}\n                  placeholder=\"BrAInBIDemo\"\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n\n            {/* Driver et Port */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Driver ODBC\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.driver}\n                  onChange={(e) => handleInputChange('driver', e.target.value)}\n                  placeholder=\"ODBC Driver 17 for SQL Server\"\n                  className=\"w-full\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Port (optionnel)\n                </label>\n                <DarkInput\n                  type=\"number\"\n                  value={config.port}\n                  onChange={(e) => handleInputChange('port', e.target.value)}\n                  placeholder=\"1433\"\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n\n            {/* Type d'authentification */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n                Type d'authentification\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    checked={config.use_windows_auth}\n                    onChange={() => handleInputChange('use_windows_auth', true)}\n                    className=\"text-purple-500 focus:ring-purple-500\"\n                  />\n                  <span className=\"text-gray-300\">Authentification Windows</span>\n                </label>\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    checked={!config.use_windows_auth}\n                    onChange={() => handleInputChange('use_windows_auth', false)}\n                    className=\"text-purple-500 focus:ring-purple-500\"\n                  />\n                  <span className=\"text-gray-300\">Authentification SQL Server</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Credentials SQL Server */}\n            {!config.use_windows_auth && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Nom d'utilisateur\n                  </label>\n                  <DarkInput\n                    type=\"text\"\n                    value={config.username}\n                    onChange={(e) => handleInputChange('username', e.target.value)}\n                    placeholder=\"sa\"\n                    className=\"w-full\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Mot de passe\n                  </label>\n                  <DarkInput\n                    type=\"password\"\n                    value={config.password}\n                    onChange={(e) => handleInputChange('password', e.target.value)}\n                    placeholder=\"••••••••\"\n                    className=\"w-full\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Timeout */}\n            <div className=\"w-full md:w-1/2\">\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Timeout de connexion (secondes)\n              </label>\n              <DarkInput\n                type=\"number\"\n                value={config.timeout}\n                onChange={(e) => handleInputChange('timeout', parseInt(e.target.value) || 30)}\n                placeholder=\"30\"\n                className=\"w-full\"\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n\n          {/* Résultat du test */}\n          {testResult && (\n            <div className={`mt-6 p-4 rounded-lg border ${\n              testResult.success \n                ? 'bg-green-900/20 border-green-700' \n                : 'bg-red-900/20 border-red-700'\n            }`}>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span>{testResult.success ? '✅' : '❌'}</span>\n                <span className={`font-medium ${\n                  testResult.success ? 'text-green-300' : 'text-red-300'\n                }`}>\n                  {testResult.success ? 'Test réussi' : 'Test échoué'}\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-300 mb-2\">{testResult.message}</p>\n              \n              {testResult.success && testResult.server_info && (\n                <div className=\"text-xs text-gray-400 space-y-1\">\n                  <div>Version: {testResult.server_info.version?.substring(0, 50)}...</div>\n                  <div>Base actuelle: {testResult.server_info.current_database}</div>\n                  <div>Temps de connexion: {testResult.connection_time?.toFixed(3)}s</div>\n                </div>\n              )}\n              \n              {!testResult.success && testResult.error_code && (\n                <div className=\"text-xs text-red-400\">\n                  Code d'erreur: {testResult.error_code}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Boutons d'action */}\n          <div className=\"flex space-x-4 mt-8\">\n            <SecondaryButton\n              onClick={() => navigate('/')}\n              icon=\"🏠\"\n              disabled={loading || testing}\n            >\n              Retour\n            </SecondaryButton>\n            \n            <SecondaryButton\n              onClick={testConnection}\n              icon={testing ? null : \"🔍\"}\n              disabled={loading || testing || !config.server || !config.database}\n              loading={testing}\n            >\n              {testing ? 'Test en cours...' : 'Tester la connexion'}\n            </SecondaryButton>\n            \n            <PrimaryButton\n              onClick={saveConfiguration}\n              icon={loading ? null : \"💾\"}\n              disabled={loading || testing || !config.server || !config.database}\n              loading={loading}\n              className=\"flex-1\"\n            >\n              {loading ? 'Sauvegarde...' : 'Valider la configuration'}\n            </PrimaryButton>\n          </div>\n        </DarkCard>\n\n        {/* Aide */}\n        <DarkCard className=\"mt-8\">\n          <SectionHeader\n            title=\"Aide à la configuration\"\n            subtitle=\"Conseils pour configurer votre connexion SQL Server\"\n            icon=\"💡\"\n          />\n          \n          <div className=\"mt-6 space-y-4 text-sm text-gray-400\">\n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">🔧 Formats de serveur courants :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• <code className=\"text-purple-400\">localhost</code> - Instance par défaut locale</li>\n                <li>• <code className=\"text-purple-400\">localhost\\\\SQLEXPRESS</code> - Instance nommée locale</li>\n                <li>• <code className=\"text-purple-400\">*************</code> - Serveur distant par IP</li>\n                <li>• <code className=\"text-purple-400\">server.domain.com</code> - Serveur distant par nom</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">🔐 Authentification :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• <strong>Windows</strong> : Utilise votre compte Windows actuel</li>\n                <li>• <strong>SQL Server</strong> : Nécessite un nom d'utilisateur et mot de passe SQL</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">⚠️ Dépannage :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• Vérifiez que SQL Server est démarré</li>\n                <li>• Assurez-vous que TCP/IP est activé dans SQL Server Configuration Manager</li>\n                <li>• Vérifiez les paramètres de pare-feu</li>\n                <li>• Testez d'abord avec SQL Server Management Studio</li>\n              </ul>\n            </div>\n          </div>\n        </DarkCard>\n      </div>\n    </div>\n  );\n};\n\nexport default SQLConfigPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,SAAS,EACTC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAY,GAAG,uBAAuB;AAE5C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC;IACnCqB,MAAM,EAAE,sBAAsB;IAC9BC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,+BAA+B;IACvCC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,IAAI;IACtBC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC;IAAEuC,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;;EAEjF;EACA,MAAMC,SAAS,GAAGA,CAACF,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvCE,UAAU,CAAC,MAAM;MACfL,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,KAAK,IAAK;IAAA,IAAAC,eAAA,EAAAC,oBAAA;IACrC,IAAIC,QAAQ,GAAG,EAAAF,eAAA,GAAAD,KAAK,CAACI,QAAQ,cAAAH,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBI,IAAI,cAAAH,oBAAA,uBAApBA,oBAAA,CAAsBI,MAAM,KAAIN,KAAK,CAACL,OAAO;;IAE5D;IACA,IAAI,OAAOQ,QAAQ,KAAK,QAAQ,EAAE;MAChC,IAAII,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,EAAE;QAC3BA,QAAQ,GAAGA,QAAQ,CAACM,GAAG,CAACC,GAAG,IAAI;UAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAOA,GAAG,CAACC,GAAG,IAAI,GAAGD,GAAG,CAACd,IAAI,KAAKc,GAAG,CAACE,KAAK,EAAE,IAAIC,IAAI,CAACC,SAAS,CAACJ,GAAG,CAAC;UACtE;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC;MACf,CAAC,MAAM;QACLZ,QAAQ,GAAGA,QAAQ,CAACQ,GAAG,IAAI,GAAGR,QAAQ,CAACP,IAAI,KAAKO,QAAQ,CAACS,KAAK,EAAE,IAAIC,IAAI,CAACC,SAAS,CAACX,QAAQ,CAAC;MAC9F;IACF;IAEA,OAAOa,MAAM,CAACb,QAAQ,CAAC;EACzB,CAAC;;EAED;EACA/C,SAAS,CAAC,MAAM;IACd6D,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM9C,KAAK,CAAC4D,GAAG,CAAC,GAAGlD,YAAY,qBAAqB,CAAC;MACtE,IAAIoC,QAAQ,CAACC,IAAI,CAACc,UAAU,EAAE;QAC5B5B,gBAAgB,CAACa,QAAQ,CAACC,IAAI,CAAC/B,MAAM,CAAC;QACtC;QACAC,SAAS,CAAC6C,IAAI,KAAK;UACjB,GAAGA,IAAI;UACP,GAAGhB,QAAQ,CAACC,IAAI,CAAC/B,MAAM;UACvBM,QAAQ,EAAE,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CjD,SAAS,CAAC6C,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACG,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;IACH;IACAnC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMoC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnD,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE;MACtCoB,SAAS,CAAC,iEAAiE,EAAE,OAAO,CAAC;MACrF;IACF;IAEA,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChBE,aAAa,CAAC,IAAI,CAAC;MAEnB,MAAMe,QAAQ,GAAG,MAAM9C,KAAK,CAACoE,IAAI,CAAC,GAAG1D,YAAY,kBAAkB,EAAEM,MAAM,CAAC;MAC5Ee,aAAa,CAACe,QAAQ,CAACC,IAAI,CAAC;MAE5B,IAAID,QAAQ,CAACC,IAAI,CAACsB,OAAO,EAAE;QAAA,IAAAC,qBAAA;QACzB/B,SAAS,CAAC,yBAAA+B,qBAAA,GAAwBxB,QAAQ,CAACC,IAAI,CAACwB,eAAe,cAAAD,qBAAA,uBAA7BA,qBAAA,CAA+BE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAClF,CAAC,MAAM;QACLjC,SAAS,CAACO,QAAQ,CAACC,IAAI,CAACV,OAAO,EAAE,OAAO,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,MAAMG,QAAQ,GAAGJ,mBAAmB,CAACC,KAAK,CAAC;MAE3CX,aAAa,CAAC;QACZsC,OAAO,EAAE,KAAK;QACdhC,OAAO,EAAEQ,QAAQ;QACjB4B,UAAU,EAAE;MACd,CAAC,CAAC;MACFlC,SAAS,CAAC,uBAAuB,GAAGM,QAAQ,EAAE,OAAO,CAAC;IACxD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC1D,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE;MACtCoB,SAAS,CAAC,iEAAiE,EAAE,OAAO,CAAC;MACrF;IACF;IAEA,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMmB,QAAQ,GAAG,MAAM9C,KAAK,CAACoE,IAAI,CAAC,GAAG1D,YAAY,aAAa,EAAEM,MAAM,CAAC;MAEvE,IAAI8B,QAAQ,CAACC,IAAI,CAACsB,OAAO,EAAE;QACzB9B,SAAS,CAAC,yCAAyC,CAAC;QACpDN,gBAAgB,CAACa,QAAQ,CAACC,IAAI,CAAC/B,MAAM,CAAC;;QAEtC;QACAwB,UAAU,CAAC,MAAM;UACfzB,QAAQ,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLwB,SAAS,CAACO,QAAQ,CAACC,IAAI,CAACV,OAAO,EAAE,OAAO,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,MAAMG,QAAQ,GAAGJ,mBAAmB,CAACC,KAAK,CAAC;MAC3CH,SAAS,CAAC,gCAAgC,GAAGM,QAAQ,EAAE,OAAO,CAAC;IACjE,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElB,OAAA;IAAKkE,SAAS,EAAC,wCAAwC;IAAAC,QAAA,gBACrDnE,OAAA,CAACR,aAAa;MAAC4E,OAAO,EAAE;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjCxE,OAAA;MAAKkE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CnE,OAAA;QAAKkE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnE,OAAA;UAAKkE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CnE,OAAA;YAAKkE,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjHnE,OAAA;cAAMkE,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNxE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAAC;YAE9G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxE,OAAA;cAAGkE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLjD,aAAa,iBACZvB,OAAA;UAAKkE,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1EnE,OAAA;YAAKkE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CnE,OAAA;cAAMkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCxE,OAAA;cAAMkE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxE,OAAA;YAAKkE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCnE,OAAA;cAAAmE,QAAA,GAAK,WAAS,EAAC5C,aAAa,CAACd,MAAM;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CxE,OAAA;cAAAmE,QAAA,GAAK,QAAM,EAAC5C,aAAa,CAACb,QAAQ;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCxE,OAAA;cAAAmE,QAAA,GAAK,oBAAkB,EAAC5C,aAAa,CAACT,gBAAgB,GAAG,SAAS,GAAG,YAAY;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL/C,KAAK,CAACE,IAAI,iBACT3B,OAAA;QAAKkE,SAAS,EAAE,kDACdzC,KAAK,CAACI,IAAI,KAAK,SAAS,GACpB,iDAAiD,GACjD,2CAA2C,mBAC7B;QAAAsC,QAAA,eAClBnE,OAAA;UAAKkE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CnE,OAAA;YAAAmE,QAAA,EAAO1C,KAAK,CAACI,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDxE,OAAA;YAAAmE,QAAA,EAAO1C,KAAK,CAACG;UAAO;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxE,OAAA,CAACP,QAAQ;QAAA0E,QAAA,gBACPnE,OAAA,CAACN,aAAa;UACZ+E,KAAK,EAAC,4BAAyB;UAC/BC,QAAQ,EAAC,uDAAoD;UAC7DC,IAAI,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEFxE,OAAA;UAAKkE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7BnE,OAAA;YAAKkE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAElD,MAAM,CAACE,MAAO;gBACrBmE,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,QAAQ,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC7DsB,WAAW,EAAC,wBAAsB;gBAClCb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACFxE,OAAA;gBAAGkE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAElD,MAAM,CAACG,QAAS;gBACvBkE,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC/DsB,WAAW,EAAC,aAAa;gBACzBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxE,OAAA;YAAKkE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAElD,MAAM,CAACI,MAAO;gBACrBiE,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,QAAQ,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC7DsB,WAAW,EAAC,+BAA+B;gBAC3Cb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,QAAQ;gBACb4B,KAAK,EAAElD,MAAM,CAACQ,IAAK;gBACnB6D,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,MAAM,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC3DsB,WAAW,EAAC,MAAM;gBAClBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAOkE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA;cAAKkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BnE,OAAA;gBAAOkE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAC3DnE,OAAA;kBACE6B,IAAI,EAAC,OAAO;kBACZmD,OAAO,EAAEzE,MAAM,CAACO,gBAAiB;kBACjC8D,QAAQ,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,kBAAkB,EAAE,IAAI,CAAE;kBAC5DW,SAAS,EAAC;gBAAuC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACFxE,OAAA;kBAAMkE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACRxE,OAAA;gBAAOkE,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAC3DnE,OAAA;kBACE6B,IAAI,EAAC,OAAO;kBACZmD,OAAO,EAAE,CAACzE,MAAM,CAACO,gBAAiB;kBAClC8D,QAAQ,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,CAAE;kBAC7DW,SAAS,EAAC;gBAAuC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACFxE,OAAA;kBAAMkE,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAACjE,MAAM,CAACO,gBAAgB,iBACvBd,OAAA;YAAKkE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDnE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACX4B,KAAK,EAAElD,MAAM,CAACK,QAAS;gBACvBgE,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC/DsB,WAAW,EAAC,IAAI;gBAChBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENxE,OAAA;cAAAmE,QAAA,gBACEnE,OAAA;gBAAOkE,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,UAAU;gBACf4B,KAAK,EAAElD,MAAM,CAACM,QAAS;gBACvB+D,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,UAAU,EAAEsB,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAE;gBAC/DsB,WAAW,EAAC,kDAAU;gBACtBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDxE,OAAA;YAAKkE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnE,OAAA;cAAOkE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxE,OAAA,CAACH,SAAS;cACRgC,IAAI,EAAC,QAAQ;cACb4B,KAAK,EAAElD,MAAM,CAACS,OAAQ;cACtB4D,QAAQ,EAAGC,CAAC,IAAKtB,iBAAiB,CAAC,SAAS,EAAE0B,QAAQ,CAACJ,CAAC,CAACC,MAAM,CAACrB,KAAK,CAAC,IAAI,EAAE,CAAE;cAC9EsB,WAAW,EAAC,IAAI;cAChBb,SAAS,EAAC,QAAQ;cAClBgB,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLnD,UAAU,iBACTrB,OAAA;UAAKkE,SAAS,EAAE,8BACd7C,UAAU,CAACuC,OAAO,GACd,kCAAkC,GAClC,8BAA8B,EACjC;UAAAO,QAAA,gBACDnE,OAAA;YAAKkE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CnE,OAAA;cAAAmE,QAAA,EAAO9C,UAAU,CAACuC,OAAO,GAAG,GAAG,GAAG;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CxE,OAAA;cAAMkE,SAAS,EAAE,eACf7C,UAAU,CAACuC,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACrD;cAAAO,QAAA,EACA9C,UAAU,CAACuC,OAAO,GAAG,aAAa,GAAG;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNxE,OAAA;YAAGkE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAE9C,UAAU,CAACO;UAAO;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEjEnD,UAAU,CAACuC,OAAO,IAAIvC,UAAU,CAAC+D,WAAW,iBAC3CpF,OAAA;YAAKkE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CnE,OAAA;cAAAmE,QAAA,GAAK,WAAS,GAAA/D,qBAAA,GAACiB,UAAU,CAAC+D,WAAW,CAACC,OAAO,cAAAjF,qBAAA,uBAA9BA,qBAAA,CAAgCkF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzExE,OAAA;cAAAmE,QAAA,GAAK,iBAAe,EAAC9C,UAAU,CAAC+D,WAAW,CAACG,gBAAgB;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnExE,OAAA;cAAAmE,QAAA,GAAK,sBAAoB,GAAA9D,qBAAA,GAACgB,UAAU,CAACyC,eAAe,cAAAzD,qBAAA,uBAA1BA,qBAAA,CAA4B0D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CACN,EAEA,CAACnD,UAAU,CAACuC,OAAO,IAAIvC,UAAU,CAAC2C,UAAU,iBAC3ChE,OAAA;YAAKkE,SAAS,EAAC,sBAAsB;YAAAC,QAAA,GAAC,iBACrB,EAAC9C,UAAU,CAAC2C,UAAU;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDxE,OAAA;UAAKkE,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCnE,OAAA,CAACJ,eAAe;YACd4F,OAAO,EAAEA,CAAA,KAAMlF,QAAQ,CAAC,GAAG,CAAE;YAC7BqE,IAAI,EAAC,cAAI;YACTc,QAAQ,EAAExE,OAAO,IAAIE,OAAQ;YAAAgD,QAAA,EAC9B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAElBxE,OAAA,CAACJ,eAAe;YACd4F,OAAO,EAAE9B,cAAe;YACxBiB,IAAI,EAAExD,OAAO,GAAG,IAAI,GAAG,IAAK;YAC5BsE,QAAQ,EAAExE,OAAO,IAAIE,OAAO,IAAI,CAACZ,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAS;YACnEO,OAAO,EAAEE,OAAQ;YAAAgD,QAAA,EAEhBhD,OAAO,GAAG,kBAAkB,GAAG;UAAqB;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAElBxE,OAAA,CAACL,aAAa;YACZ6F,OAAO,EAAEvB,iBAAkB;YAC3BU,IAAI,EAAE1D,OAAO,GAAG,IAAI,GAAG,IAAK;YAC5BwE,QAAQ,EAAExE,OAAO,IAAIE,OAAO,IAAI,CAACZ,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAS;YACnEO,OAAO,EAAEA,OAAQ;YACjBiD,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAEjBlD,OAAO,GAAG,eAAe,GAAG;UAA0B;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGXxE,OAAA,CAACP,QAAQ;QAACyE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACxBnE,OAAA,CAACN,aAAa;UACZ+E,KAAK,EAAC,4BAAyB;UAC/BC,QAAQ,EAAC,qDAAqD;UAC9DC,IAAI,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEFxE,OAAA;UAAKkE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDnE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpFxE,OAAA;cAAIkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5BnE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAMkE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oCAA6B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1FxE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAMkE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gCAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClGxE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAMkE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,6BAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1FxE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAMkE,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8BAA0B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENxE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzExE,OAAA;cAAIkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5BnE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAAmE,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,0CAAsC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzExE,OAAA;gBAAAmE,QAAA,GAAI,SAAE,eAAAnE,OAAA;kBAAAmE,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4DAAqD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENxE,OAAA;YAAAmE,QAAA,gBACEnE,OAAA;cAAIkE,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClExE,OAAA;cAAIkE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5BnE,OAAA;gBAAAmE,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CxE,OAAA;gBAAAmE,QAAA,EAAI;cAA0E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFxE,OAAA;gBAAAmE,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CxE,OAAA;gBAAAmE,QAAA,EAAI;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrE,EAAA,CAhcID,aAAa;EAAA,QACAZ,WAAW;AAAA;AAAAoG,EAAA,GADxBxF,aAAa;AAkcnB,eAAeA,aAAa;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}