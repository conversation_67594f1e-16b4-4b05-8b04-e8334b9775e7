import React, { useState } from 'react';
import { Routes, Route, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Plot from 'react-plotly.js';
import './App.css';
import {
  BrA<PERSON>nBIHeader,
  HeroTitle,
  PrimaryButton,
  SecondaryButton,
  DarkCard,
  KPICard,
  ChatBubble,
  DarkInput,
  DarkBadge,
  DarkSpinner,
  SectionHeader
} from './components/YellowMindUI';
import TablesOverview from './components/TablesOverview';
import TablesListPage from './components/TablesListPage';
import TableDetailPage from './components/TableDetailPage';
import VisualBuilderPage from './components/VisualBuilderPage';
import DragVisualPage from './components/DragVisualPage';
import DashboardPage from './components/DashboardPage';
import SQLConfigPage from './components/SQLConfigPage';

const API_BASE_URL = 'http://localhost:8000';

// Composant principal de la page d'accueil
function HomePage() {
  const navigate = useNavigate();
  const [dataInfo, setDataInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [question, setQuestion] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [dataQuality, setDataQuality] = useState(null);
  const [kpiExplanation, setKpiExplanation] = useState('');
  const [showKpiModal, setShowKpiModal] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [showTablesOverview, setShowTablesOverview] = useState(false);
  const [fullSchemaData, setFullSchemaData] = useState(null);
  const [sqlConfigured, setSqlConfigured] = useState(false);
  const [sqlConfigStatus, setSqlConfigStatus] = useState(null);

  // Fonction pour vérifier l'état de la configuration SQL
  const checkSqlConfiguration = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);
      setSqlConfigured(response.data.configured);
      setSqlConfigStatus(response.data.config);
      return response.data.configured;
    } catch (error) {
      console.error('Erreur lors de la vérification de la configuration SQL:', error);
      setSqlConfigured(false);
      setSqlConfigStatus(null);
      return false;
    }
  };

  // Fonction pour charger les informations des données SQL Server
  const loadDataInfo = async () => {
    try {
      setLoading(true);
      setError('');

      // Vérifier d'abord la configuration SQL
      const isConfigured = await checkSqlConfiguration();
      if (!isConfigured) {
        setError('Configuration SQL non définie. Veuillez configurer votre base de données.');
        setLoading(false);
        return;
      }

      const response = await axios.get(`${API_BASE_URL}/data/info`);
      setDataInfo(response.data);
      setDataQuality(response.data.data_quality);
      setDataLoaded(true);

    } catch (err) {
      setError('Erreur lors du chargement des données SQL Server: ' + (err.response?.data?.detail || err.message));
      setDataLoaded(false);
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour charger l'aperçu complet des tables
  const loadFullSchema = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/schema/full`);
      setFullSchemaData(response.data);
      setShowTablesOverview(true);
    } catch (err) {
      setError('Erreur lors du chargement de l\'aperçu des tables: ' + (err.response?.data?.detail || err.message));
    } finally {
      setLoading(false);
    }
  };

  // Charger automatiquement les données au démarrage
  React.useEffect(() => {
    // D'abord vérifier la configuration, puis charger les données si configuré
    checkSqlConfiguration().then(isConfigured => {
      if (isConfigured) {
        loadDataInfo();
      } else {
        setLoading(false);
      }
    });
  }, []);

  // Poser une question personnalisée
  const handleAskQuestion = async () => {
    if (!question.trim()) {
      setError('Veuillez saisir une question');
      return;
    }

    if (!dataLoaded) {
      setError('Données SQL Server non disponibles');
      return;
    }

    setLoading(true);
    setError('');

    // Ajouter la question de l'utilisateur au chat
    const newHistory = [...chatHistory, {
      type: 'user',
      content: question,
      timestamp: new Date().toLocaleTimeString()
    }];
    setChatHistory(newHistory);

    try {
      const response = await axios.post(`${API_BASE_URL}/ask`, {
        question: question
      });

      // Ajouter la réponse de l'IA au chat
      setChatHistory([...newHistory, {
        type: 'ai',
        content: response.data.analysis,
        chart: response.data.chart_data,
        chartJson: response.data.chart_json,
        timestamp: new Date().toLocaleTimeString()
      }]);

      setQuestion('');

    } catch (err) {
      setError(err.response?.data?.detail || 'Erreur lors de l\'analyse');
    } finally {
      setLoading(false);
    }
  };

  // Mode pédagogique - Expliquer un KPI
  const handleExplainKPI = async (kpiName) => {
    if (!dataLoaded) {
      setError('Données SQL Server non disponibles');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post(`${API_BASE_URL}/explain-kpi`, {
        kpi_name: kpiName,
        context: `Données de la table SQL Server ${dataInfo?.table_name || 'Sales'}`
      });

      setKpiExplanation(response.data.analysis);
      setShowKpiModal(true);

      // Ajouter à l'historique du chat
      setChatHistory(prev => [...prev, {
        type: 'pedagogical',
        content: response.data.analysis,
        timestamp: new Date().toLocaleTimeString()
      }]);

    } catch (err) {
      setError(err.response?.data?.detail || 'Erreur lors de l\'explication');
    } finally {
      setLoading(false);
    }
  };

  // Effacer l'historique de conversation
  const clearHistory = async () => {
    try {
      await axios.delete(`${API_BASE_URL}/conversation/history`);
      setChatHistory([]);
    } catch (err) {
      console.error('Erreur lors de l\'effacement de l\'historique:', err);
    }
  };

  // Rendu d'un graphique dynamique
  const renderDynamicChart = (chartData) => {
    if (!chartData) return null;

    const plotData = [];
    const layout = {
      title: chartData.title || 'Graphique',
      autosize: true,
      height: 400,
      margin: { l: 50, r: 50, t: 50, b: 50 }
    };

    switch (chartData.type) {
      case 'bar':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'bar',
          marker: { color: '#667eea' }
        });
        layout.xaxis = { title: chartData.xlabel || '' };
        layout.yaxis = { title: chartData.ylabel || '' };
        break;

      case 'pie':
        plotData.push({
          labels: chartData.labels,
          values: chartData.values,
          type: 'pie',
          marker: { colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'] }
        });
        break;

      case 'line':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'scatter',
          mode: 'lines+markers',
          line: { color: '#667eea' }
        });
        layout.xaxis = { title: chartData.xlabel || '' };
        layout.yaxis = { title: chartData.ylabel || '' };
        break;

      case 'scatter':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'scatter',
          mode: 'markers',
          marker: { color: '#667eea', size: 8 }
        });
        layout.xaxis = { title: chartData.xlabel || '' };
        layout.yaxis = { title: chartData.ylabel || '' };
        break;

      case 'histogram':
        plotData.push({
          x: chartData.x,
          y: chartData.y,
          type: 'bar',
          marker: { color: '#667eea' }
        });
        layout.xaxis = { title: chartData.xlabel || '' };
        layout.yaxis = { title: chartData.ylabel || 'Fréquence' };
        break;

      default:
        return <p>Type de graphique non supporté: {chartData.type}</p>;
    }

    return (
      <Plot
        data={plotData}
        layout={layout}
        config={{ responsive: true }}
        style={{ width: '100%' }}
      />
    );
  };





  return (
    <div className="min-h-screen bg-gray-950 text-gray-100 fade-in">
      {/* Header BrAInBI */}
      <BrAInBIHeader
        hasData={dataLoaded}
        sqlConfigured={sqlConfigured}
        sqlConfig={sqlConfigStatus}
      />

      <div className="max-w-7xl mx-auto px-6">
        {/* Hero Section */}
        <div className="slide-in-top">
          <HeroTitle
            title="BrAInBI"
            subtitle="Explore your Data. Amplified by AI."
          />
          <div className="text-center mb-8">
            <img
              src="/brAInBI-logo.png"
              alt="BrAInBI Logo"
              className="w-24 h-24 mx-auto mb-4 rounded-2xl shadow-lg"
            />
            <p className="text-lg text-gray-300 max-w-3xl mx-auto">
              Plateforme de Business Intelligence augmentée par l'IA pour explorer, comprendre et visualiser vos données simplement via le langage naturel.
            </p>
          </div>
        </div>

        {/* SQL Server Status Section */}
        {!dataLoaded && (
          <div className="mb-12">
            <DarkCard className="text-center">
              <div className="py-12">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <span className="text-2xl">🗄️</span>
                </div>

                <h3 className="text-xl font-semibold text-gray-100 mb-4">
                  {sqlConfigured ? 'Connexion SQL Server' : 'Configuration SQL Server'}
                </h3>
                <p className="text-gray-400 mb-8 max-w-md mx-auto">
                  {sqlConfigured
                    ? 'BrAInBI se connecte à votre base de données SQL Server en mode live'
                    : 'Configurez votre connexion à SQL Server pour commencer'
                  }
                </p>

                {/* Affichage de la configuration actuelle */}
                {sqlConfigured && sqlConfigStatus && (
                  <div className="bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6 max-w-md mx-auto">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-green-400">✅</span>
                      <span className="font-medium text-green-300">Configuration active</span>
                    </div>
                    <div className="text-sm text-gray-300">
                      <div>Serveur: {sqlConfigStatus.server}</div>
                      <div>Base: {sqlConfigStatus.database}</div>
                    </div>
                  </div>
                )}

                {loading ? (
                  <div className="flex items-center justify-center space-x-3 mb-6">
                    <DarkSpinner />
                    <span className="text-gray-400">
                      {sqlConfigured ? 'Connexion en cours...' : 'Vérification de la configuration...'}
                    </span>
                  </div>
                ) : error ? (
                  <div className="mb-6">
                    <div className="text-red-400 mb-4">
                      <p>❌ {sqlConfigured ? 'Erreur de connexion' : 'Configuration requise'}</p>
                      <p className="text-sm mt-2 max-w-md mx-auto">{error}</p>
                    </div>
                    {sqlConfigured ? (
                      <div className="flex space-x-4 justify-center">
                        <SecondaryButton
                          onClick={() => navigate('/config/sql')}
                          icon="⚙️"
                          className="text-lg px-6 py-3"
                        >
                          Reconfigurer
                        </SecondaryButton>
                        <PrimaryButton
                          onClick={loadDataInfo}
                          icon="🔄"
                          className="text-lg px-6 py-3"
                        >
                          Réessayer
                        </PrimaryButton>
                      </div>
                    ) : (
                      <PrimaryButton
                        onClick={() => navigate('/config/sql')}
                        icon="⚙️"
                        className="text-lg px-8 py-4"
                      >
                        Configurer SQL Server
                      </PrimaryButton>
                    )}
                  </div>
                ) : (
                  <div className="mb-6">
                    <div className="text-green-400 mb-4">
                      <p>✅ {sqlConfigured ? 'Connexion établie' : 'Prêt à configurer'}</p>
                    </div>
                    <PrimaryButton
                      onClick={loadDataInfo}
                      icon="🚀"
                      className="text-lg px-8 py-4"
                    >
                      Charger les données
                    </PrimaryButton>
                  </div>
                )}
            </DarkCard>
          </div>
        )}



        {/* KPIs Section */}
        {dataLoaded && dataInfo && (
          <div className="mb-12">
            <SectionHeader
              title="Aperçu des données SQL Server"
              subtitle="Statistiques principales de votre base de données"
              icon="📈"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <KPICard
                title="Lignes totales"
                value={dataInfo.total_rows?.toLocaleString() || '0'}
                icon="📊"
                trend="up"
                change="Live"
              />
              <KPICard
                title="Colonnes"
                value={dataInfo.columns?.length || '0'}
                icon="📋"
                trend="neutral"
              />
              <KPICard
                title="Tables"
                value={`${dataInfo.tables_count || 0} tables`}
                icon="🗄️"
                trend="neutral"
                onClick={() => navigate('/tables')}
                className="cursor-pointer hover:bg-gray-800/50 transition-colors"
              />
              <KPICard
                title="Visualisations"
                value="Drag & Drop"
                icon="🎯"
                trend="up"
                change="Nouveau"
                onClick={() => navigate('/drag-visual')}
                className="cursor-pointer hover:bg-gray-800/50 transition-colors"
              />
              <KPICard
                title="Dashboard"
                value="Mes Visuels"
                icon="📊"
                trend="up"
                change="Gérer"
                onClick={() => navigate('/dashboard')}
                className="cursor-pointer hover:bg-gray-800/50 transition-colors"
              />
              <KPICard
                title="Configuration"
                value="SQL Server"
                icon="⚙️"
                trend="neutral"
                change="Configurer"
                onClick={() => navigate('/config/sql')}
                className="cursor-pointer hover:bg-gray-800/50 transition-colors"
              />
              <KPICard
                title="Statut"
                value="Connecté"
                icon="✅"
                trend="up"
                change="Live"
              />
            </div>

            {/* Structure des données et exploration des tables */}
            <DarkCard className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <SectionHeader
                  title="Structure des données"
                  subtitle="Explorez les colonnes et la structure complète de la base"
                  icon="🏷️"
                />
                <div className="flex space-x-3">
                  <SecondaryButton
                    onClick={() => navigate('/drag-visual')}
                    disabled={loading}
                    icon="🎯"
                    className="!py-2 !px-4"
                  >
                    Drag & Drop
                  </SecondaryButton>
                  <SecondaryButton
                    onClick={() => navigate('/visual-builder')}
                    disabled={loading}
                    icon="📊"
                    className="!py-2 !px-4"
                  >
                    Visual Builder
                  </SecondaryButton>
                  <SecondaryButton
                    onClick={() => navigate('/dashboard')}
                    disabled={loading}
                    icon="📋"
                    className="!py-2 !px-4"
                  >
                    Mes Visuels
                  </SecondaryButton>
                  <SecondaryButton
                    onClick={() => navigate('/config/sql')}
                    disabled={loading}
                    icon="⚙️"
                    className="!py-2 !px-4"
                  >
                    Configuration SQL
                  </SecondaryButton>
                  <PrimaryButton
                    onClick={() => navigate('/tables')}
                    disabled={loading}
                    icon="🔍"
                    className="!py-2 !px-4"
                  >
                    Explorer les tables
                  </PrimaryButton>
                </div>
              </div>

              <div className="flex flex-wrap gap-3">
                {dataInfo.tables && Object.keys(dataInfo.tables).length > 0 ? (
                  Object.entries(dataInfo.tables).slice(0, 1).map(([tableName, tableData]) =>
                    tableData.columns?.map((col, index) => (
                      <div key={`${tableName}-${index}`} className="flex items-center space-x-2">
                        <DarkBadge
                          variant="info"
                          className="cursor-pointer hover:bg-blue-800/70 transition-colors"
                          onClick={() => handleExplainKPI(col)}
                        >
                          {col}
                        </DarkBadge>
                        <SecondaryButton
                          onClick={() => handleExplainKPI(col)}
                          className="!p-1 !min-w-0 w-6 h-6 text-xs"
                          icon="?"
                        />
                      </div>
                    ))
                  )
                ) : (
                  dataInfo.columns?.map((col, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <DarkBadge
                        variant="info"
                        className="cursor-pointer hover:bg-blue-800/70 transition-colors"
                        onClick={() => handleExplainKPI(col)}
                      >
                        {col}
                      </DarkBadge>
                      <SecondaryButton
                        onClick={() => handleExplainKPI(col)}
                        className="!p-1 !min-w-0 w-6 h-6 text-xs"
                        icon="?"
                      />
                    </div>
                  ))
                )}
              </div>
            </DarkCard>

            {/* Qualité des données */}
            {dataQuality && (
              <DarkCard className="mb-8 border-yellow-500/30 bg-yellow-900/10">
                <SectionHeader
                  title="Qualité des données"
                  subtitle="Analyse automatique de la qualité"
                  icon="🔍"
                />
                <div className="text-gray-300 whitespace-pre-wrap leading-relaxed">
                  {dataQuality}
                </div>
              </DarkCard>
            )}

            {/* Aperçu du tableau */}
            <DarkCard>
              <SectionHeader
                title="Aperçu des données SQL Server"
                subtitle="5 premières lignes de votre table"
                icon="👁️"
              />

              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      {dataInfo.columns?.map((col, index) => (
                        <th key={index} className="text-left py-3 px-4 font-semibold text-gray-300 bg-gray-800/50">
                          {col}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {dataInfo.sample_data?.map((row, rowIndex) => (
                      <tr key={rowIndex} className="border-b border-gray-800 hover:bg-gray-800/30">
                        {dataInfo.columns?.map((col, colIndex) => (
                          <td key={colIndex} className="py-3 px-4 text-gray-400">
                            {row[col] || 'N/A'}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </DarkCard>
          </div>
        )}

        {/* Tables Overview Modal/Section */}
        {showTablesOverview && fullSchemaData && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center p-4 overflow-y-auto">
            <div className="bg-gray-900 rounded-xl max-w-6xl w-full min-h-[90vh] my-4">
              {/* Header fixe */}
              <div className="sticky top-0 bg-gray-900 rounded-t-xl border-b border-gray-800 z-10">
                <div className="flex items-center justify-between p-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-100">Aperçu des Tables</h2>
                    <p className="text-gray-400 mt-1">
                      Base de données: {fullSchemaData.database_info?.name} • {fullSchemaData.database_info?.tables_count} tables • {fullSchemaData.database_info?.total_rows?.toLocaleString()} lignes totales
                    </p>
                  </div>
                  <SecondaryButton
                    onClick={() => setShowTablesOverview(false)}
                    icon="✕"
                    className="!p-2 hover:bg-gray-800 transition-colors"
                  />
                </div>
              </div>

              {/* Contenu scrollable */}
              <div className="p-6">
                <TablesOverview data={fullSchemaData} />
              </div>
            </div>
          </div>
        )}

        {/* Chat Section - Aligné en bas */}
        {dataLoaded && (
          <div className="mb-8">
            <DarkCard>
              <SectionHeader
                title="Assistant IA BrAInBI"
                subtitle="Posez vos questions sur vos données en langage naturel"
                icon="💬"
                action={
                  <SecondaryButton
                    onClick={clearHistory}
                    disabled={loading || chatHistory.length === 0}
                    icon="🗑️"
                    className="!py-2 !px-4"
                  >
                    Effacer
                  </SecondaryButton>
                }
              />

              {/* Chat History */}
              <div className="h-96 overflow-y-auto mb-6 p-4 bg-gray-900/30 rounded-lg border border-gray-800">
                {chatHistory.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-2xl mb-4 flex items-center justify-center">
                      <span className="text-2xl">🤖</span>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-300 mb-2">
                      Prêt à analyser vos données avec BrAInBI
                    </h3>
                    <p className="text-gray-500 max-w-md">
                      Posez votre première question pour commencer l'analyse intelligente de vos données
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {chatHistory.map((message, index) => (
                      <ChatBubble
                        key={index}
                        type={message.type}
                        timestamp={message.timestamp}
                      >
                        {message.content}

                        {/* Graphiques */}
                        {message.chart && (
                          <div className="mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700">
                            <Plot
                              data={message.chart.data}
                              layout={{
                                ...message.chart.layout,
                                autosize: true,
                                height: 400,
                                paper_bgcolor: 'rgba(0,0,0,0)',
                                plot_bgcolor: 'rgba(0,0,0,0)',
                                font: { color: '#F3F4F6' }
                              }}
                              config={{ responsive: true }}
                              style={{ width: '100%' }}
                            />
                          </div>
                        )}

                        {message.chartJson && (
                          <div className="mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700">
                            {renderDynamicChart(JSON.parse(message.chartJson))}
                          </div>
                        )}
                      </ChatBubble>
                    ))}
                  </div>
                )}
              </div>

              {/* Suggestions */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-400 mb-3">💡 Suggestions</h4>
                <div className="flex flex-wrap gap-2">
                  {[
                    'Montre-moi un graphique en barres',
                    'Analyse les tendances',
                    'Y a-t-il des anomalies ?',
                    'Crée un diagramme circulaire',
                    'Résume les données'
                  ].map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => setQuestion(suggestion)}
                      disabled={loading || !dataLoaded}
                      className="px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full border border-gray-700 hover:border-gray-600 transition-colors disabled:opacity-50"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>

              {/* Input Zone */}
              <div className="flex space-x-4">
                <div className="flex-1">
                  <DarkInput
                    placeholder="Posez votre question sur les données..."
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && !loading && dataLoaded && question.trim() && handleAskQuestion()}
                    disabled={loading || !dataLoaded}
                    icon="💬"
                  />
                </div>
                <PrimaryButton
                  onClick={handleAskQuestion}
                  disabled={loading || !dataLoaded || !question.trim()}
                  loading={loading}
                  icon={!loading ? "🚀" : null}
                  className="!py-3"
                >
                  Envoyer
                </PrimaryButton>
              </div>
            </DarkCard>
          </div>
        )}

        {/* Modal KPI */}
        {showKpiModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <DarkCard className="max-w-2xl w-full max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-100">
                  📚 Explication pédagogique
                </h3>
                <button
                  onClick={() => setShowKpiModal(false)}
                  className="w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors"
                >
                  ×
                </button>
              </div>
              <div className="text-gray-300 whitespace-pre-wrap leading-relaxed">
                {kpiExplanation}
              </div>
            </DarkCard>
          </div>
        )}

        {/* Loading Indicator */}
        {loading && (
          <div className="fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-lg p-4 shadow-xl z-50">
            <div className="flex items-center space-x-3">
              <DarkSpinner />
              <span className="text-gray-300 font-medium">
                Traitement en cours...
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Composant App principal avec routing
function App() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/tables" element={<TablesListPage />} />
      <Route path="/table/:tableName" element={<TableDetailPage />} />
      <Route path="/visual-builder" element={<VisualBuilderPage />} />
      <Route path="/drag-visual" element={<DragVisualPage />} />
      <Route path="/dashboard" element={<DashboardPage />} />
      <Route path="/mes-visuels" element={<DashboardPage />} />
      <Route path="/config/sql" element={<SQLConfigPage />} />
      <Route path="/configuration" element={<SQLConfigPage />} />
    </Routes>
  );
}

export default App;
