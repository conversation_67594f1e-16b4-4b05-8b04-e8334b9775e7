[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js": "14"}, {"size": 347, "mtime": 1752590394396, "results": "15", "hashOfConfig": "16"}, {"size": 25872, "mtime": 1752668313859, "results": "17", "hashOfConfig": "16"}, {"size": 10413, "mtime": 1752270133550, "results": "18", "hashOfConfig": "16"}, {"size": 11260, "mtime": 1752668364597, "results": "19", "hashOfConfig": "16"}, {"size": 13279, "mtime": 1752588517519, "results": "20", "hashOfConfig": "16"}, {"size": 15060, "mtime": 1752590507804, "results": "21", "hashOfConfig": "16"}, {"size": 7535, "mtime": 1752590442917, "results": "22", "hashOfConfig": "16"}, {"size": 17173, "mtime": 1752595239175, "results": "23", "hashOfConfig": "16"}, {"size": 41363, "mtime": 1752668378593, "results": "24", "hashOfConfig": "16"}, {"size": 25860, "mtime": 1752631029819, "results": "25", "hashOfConfig": "16"}, {"size": 13117, "mtime": 1752599738345, "results": "26", "hashOfConfig": "16"}, {"size": 22875, "mtime": 1752611727294, "results": "27", "hashOfConfig": "16"}, {"size": 6001, "mtime": 1752667492127, "results": "28", "hashOfConfig": "16"}, {"size": 14665, "mtime": 1752668152923, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h4smc4", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js", ["72"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js", ["73"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js", ["74", "75"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js", ["76"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js", ["77"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js", ["78", "79", "80", "81"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js", ["82", "83"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js", ["84", "85"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js", ["86"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 63, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 63, "endColumn": 23}, {"ruleId": "87", "severity": 1, "message": "91", "line": 7, "column": 46, "nodeType": "89", "messageId": "90", "endLine": 7, "endColumn": 61}, {"ruleId": "92", "severity": 1, "message": "93", "line": 32, "column": 6, "nodeType": "94", "endLine": 32, "endColumn": 17, "suggestions": "95"}, {"ruleId": "87", "severity": 1, "message": "96", "line": 73, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 73, "endColumn": 34}, {"ruleId": "87", "severity": 1, "message": "97", "line": 12, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 12, "endColumn": 16}, {"ruleId": "87", "severity": 1, "message": "98", "line": 17, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 17, "endColumn": 12}, {"ruleId": "87", "severity": 1, "message": "98", "line": 18, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 18, "endColumn": 12}, {"ruleId": "87", "severity": 1, "message": "99", "line": 275, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 275, "endColumn": 26}, {"ruleId": "87", "severity": 1, "message": "100", "line": 296, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 296, "endColumn": 28}, {"ruleId": "92", "severity": 1, "message": "101", "line": 375, "column": 6, "nodeType": "94", "endLine": 375, "endColumn": 43, "suggestions": "102"}, {"ruleId": "87", "severity": 1, "message": "103", "line": 46, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 46, "endColumn": 25}, {"ruleId": "87", "severity": 1, "message": "104", "line": 656, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 656, "endColumn": 24}, {"ruleId": "92", "severity": 1, "message": "105", "line": 135, "column": 6, "nodeType": "94", "endLine": 135, "endColumn": 45, "suggestions": "106"}, {"ruleId": "92", "severity": 1, "message": "107", "line": 147, "column": 6, "nodeType": "94", "endLine": 147, "endColumn": 54, "suggestions": "108"}, {"ruleId": "87", "severity": 1, "message": "109", "line": 13, "column": 3, "nodeType": "89", "messageId": "90", "endLine": 13, "endColumn": 14}, "no-unused-vars", "'loadFullSchema' is assigned a value but never used.", "Identifier", "unusedVar", "'SecondaryButton' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadTableInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["110"], "'getColumnTypeBadgeVariant' is assigned a value but never used.", "'SectionHeader' is defined but never used.", "'DarkBadge' is defined but never used.", "'loadConfiguration' is assigned a value but never used.", "'deleteConfiguration' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedVisualization'. Either include it or remove the dependency array.", ["111"], "'getColumnTypeLabel' is assigned a value but never used.", "'positionClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFilterValues'. Either include it or remove the dependency array.", ["112"], "React Hook useEffect has missing dependencies: 'filter' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["113"], "'DarkSpinner' is defined but never used.", {"desc": "114", "fix": "115"}, {"desc": "116", "fix": "117"}, {"desc": "118", "fix": "119"}, {"desc": "120", "fix": "121"}, "Update the dependencies array to be: [loadTableInfo, tableName]", {"range": "122", "text": "123"}, "Update the dependencies array to be: [searchParams, loadingTables, tables, loadSavedVisualization]", {"range": "124", "text": "125"}, "Update the dependencies array to be: [filter.column_name, filter.table_name, loadFilterValues]", {"range": "126", "text": "127"}, "Update the dependencies array to be: [selectedValues, minValue, maxValue, filterType, filter, onUpdate]", {"range": "128", "text": "129"}, [796, 807], "[loadTableInfo, tableName]", [11510, 11547], "[searchParams, loadingTables, tables, loadSavedVisualization]", [4399, 4438], "[filter.column_name, filter.table_name, loadFilterValues]", [4821, 4869], "[selectedValues, minValue, maxValue, filterType, filter, onUpdate]"]