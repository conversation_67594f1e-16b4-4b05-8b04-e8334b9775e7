[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js": "13"}, {"size": 347, "mtime": 1752590394396, "results": "14", "hashOfConfig": "15"}, {"size": 25058, "mtime": 1752596915568, "results": "16", "hashOfConfig": "15"}, {"size": 10413, "mtime": 1752270133550, "results": "17", "hashOfConfig": "15"}, {"size": 9748, "mtime": 1752587476271, "results": "18", "hashOfConfig": "15"}, {"size": 13279, "mtime": 1752588517519, "results": "19", "hashOfConfig": "15"}, {"size": 15060, "mtime": 1752590507804, "results": "20", "hashOfConfig": "15"}, {"size": 7535, "mtime": 1752590442917, "results": "21", "hashOfConfig": "15"}, {"size": 17173, "mtime": 1752595239175, "results": "22", "hashOfConfig": "15"}, {"size": 38685, "mtime": 1752667629099, "results": "23", "hashOfConfig": "15"}, {"size": 25860, "mtime": 1752631029819, "results": "24", "hashOfConfig": "15"}, {"size": 13117, "mtime": 1752599738345, "results": "25", "hashOfConfig": "15"}, {"size": 22875, "mtime": 1752611727294, "results": "26", "hashOfConfig": "15"}, {"size": 6001, "mtime": 1752667492127, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h4smc4", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js", ["67"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js", ["68"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js", ["69", "70"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js", ["71"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js", ["72"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js", ["73", "74", "75"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js", ["76", "77"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js", ["78", "79"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js", ["80"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js", [], [], {"ruleId": "81", "severity": 1, "message": "82", "line": 62, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 62, "endColumn": 23}, {"ruleId": "81", "severity": 1, "message": "85", "line": 7, "column": 46, "nodeType": "83", "messageId": "84", "endLine": 7, "endColumn": 61}, {"ruleId": "86", "severity": 1, "message": "87", "line": 32, "column": 6, "nodeType": "88", "endLine": 32, "endColumn": 17, "suggestions": "89"}, {"ruleId": "81", "severity": 1, "message": "90", "line": 73, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 73, "endColumn": 34}, {"ruleId": "81", "severity": 1, "message": "91", "line": 12, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 12, "endColumn": 16}, {"ruleId": "81", "severity": 1, "message": "92", "line": 17, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 17, "endColumn": 12}, {"ruleId": "81", "severity": 1, "message": "92", "line": 18, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 18, "endColumn": 12}, {"ruleId": "81", "severity": 1, "message": "93", "line": 274, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 274, "endColumn": 26}, {"ruleId": "81", "severity": 1, "message": "94", "line": 295, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 295, "endColumn": 28}, {"ruleId": "81", "severity": 1, "message": "95", "line": 46, "column": 7, "nodeType": "83", "messageId": "84", "endLine": 46, "endColumn": 25}, {"ruleId": "81", "severity": 1, "message": "96", "line": 656, "column": 9, "nodeType": "83", "messageId": "84", "endLine": 656, "endColumn": 24}, {"ruleId": "86", "severity": 1, "message": "97", "line": 135, "column": 6, "nodeType": "88", "endLine": 135, "endColumn": 45, "suggestions": "98"}, {"ruleId": "86", "severity": 1, "message": "99", "line": 147, "column": 6, "nodeType": "88", "endLine": 147, "endColumn": 54, "suggestions": "100"}, {"ruleId": "81", "severity": 1, "message": "101", "line": 13, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 13, "endColumn": 14}, "no-unused-vars", "'loadFullSchema' is assigned a value but never used.", "Identifier", "unusedVar", "'SecondaryButton' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadTableInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["102"], "'getColumnTypeBadgeVariant' is assigned a value but never used.", "'SectionHeader' is defined but never used.", "'DarkBadge' is defined but never used.", "'loadConfiguration' is assigned a value but never used.", "'deleteConfiguration' is assigned a value but never used.", "'getColumnTypeLabel' is assigned a value but never used.", "'positionClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFilterValues'. Either include it or remove the dependency array.", ["103"], "React Hook useEffect has missing dependencies: 'filter' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["104"], "'DarkSpinner' is defined but never used.", {"desc": "105", "fix": "106"}, {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, "Update the dependencies array to be: [loadTableInfo, tableName]", {"range": "111", "text": "112"}, "Update the dependencies array to be: [filter.column_name, filter.table_name, loadFilterValues]", {"range": "113", "text": "114"}, "Update the dependencies array to be: [selectedValues, minValue, maxValue, filterType, filter, onUpdate]", {"range": "115", "text": "116"}, [796, 807], "[loadTableInfo, tableName]", [4399, 4438], "[filter.column_name, filter.table_name, loadFilterValues]", [4821, 4869], "[selectedValues, minValue, maxValue, filterType, filter, onUpdate]"]