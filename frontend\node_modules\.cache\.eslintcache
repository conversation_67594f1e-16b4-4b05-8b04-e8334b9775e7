[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js": "12"}, {"size": 347, "mtime": 1752590394396, "results": "13", "hashOfConfig": "14"}, {"size": 25058, "mtime": 1752596915568, "results": "15", "hashOfConfig": "14"}, {"size": 10413, "mtime": 1752270133550, "results": "16", "hashOfConfig": "14"}, {"size": 9748, "mtime": 1752587476271, "results": "17", "hashOfConfig": "14"}, {"size": 13279, "mtime": 1752588517519, "results": "18", "hashOfConfig": "14"}, {"size": 15060, "mtime": 1752590507804, "results": "19", "hashOfConfig": "14"}, {"size": 7535, "mtime": 1752590442917, "results": "20", "hashOfConfig": "14"}, {"size": 17173, "mtime": 1752595239175, "results": "21", "hashOfConfig": "14"}, {"size": 35132, "mtime": 1752631041505, "results": "22", "hashOfConfig": "14"}, {"size": 25860, "mtime": 1752631029819, "results": "23", "hashOfConfig": "14"}, {"size": 13117, "mtime": 1752599738345, "results": "24", "hashOfConfig": "14"}, {"size": 22875, "mtime": 1752611727294, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h4smc4", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js", ["62"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js", ["63"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js", ["64", "65"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js", ["66"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js", ["67"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js", ["68", "69", "70"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js", ["71", "72"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js", ["73", "74"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js", ["75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 62, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 62, "endColumn": 23}, {"ruleId": "76", "severity": 1, "message": "80", "line": 7, "column": 46, "nodeType": "78", "messageId": "79", "endLine": 7, "endColumn": 61}, {"ruleId": "81", "severity": 1, "message": "82", "line": 32, "column": 6, "nodeType": "83", "endLine": 32, "endColumn": 17, "suggestions": "84"}, {"ruleId": "76", "severity": 1, "message": "85", "line": 73, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 73, "endColumn": 34}, {"ruleId": "76", "severity": 1, "message": "86", "line": 12, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 12, "endColumn": 16}, {"ruleId": "76", "severity": 1, "message": "87", "line": 17, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 17, "endColumn": 12}, {"ruleId": "76", "severity": 1, "message": "87", "line": 18, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 18, "endColumn": 12}, {"ruleId": "76", "severity": 1, "message": "88", "line": 269, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 269, "endColumn": 26}, {"ruleId": "76", "severity": 1, "message": "89", "line": 290, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 290, "endColumn": 28}, {"ruleId": "76", "severity": 1, "message": "90", "line": 46, "column": 7, "nodeType": "78", "messageId": "79", "endLine": 46, "endColumn": 25}, {"ruleId": "76", "severity": 1, "message": "91", "line": 656, "column": 9, "nodeType": "78", "messageId": "79", "endLine": 656, "endColumn": 24}, {"ruleId": "81", "severity": 1, "message": "92", "line": 135, "column": 6, "nodeType": "83", "endLine": 135, "endColumn": 45, "suggestions": "93"}, {"ruleId": "81", "severity": 1, "message": "94", "line": 147, "column": 6, "nodeType": "83", "endLine": 147, "endColumn": 54, "suggestions": "95"}, {"ruleId": "76", "severity": 1, "message": "96", "line": 13, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 13, "endColumn": 14}, "no-unused-vars", "'loadFullSchema' is assigned a value but never used.", "Identifier", "unusedVar", "'SecondaryButton' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadTableInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["97"], "'getColumnTypeBadgeVariant' is assigned a value but never used.", "'SectionHeader' is defined but never used.", "'DarkBadge' is defined but never used.", "'loadConfiguration' is assigned a value but never used.", "'deleteConfiguration' is assigned a value but never used.", "'getColumnTypeLabel' is assigned a value but never used.", "'positionClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFilterValues'. Either include it or remove the dependency array.", ["98"], "React Hook useEffect has missing dependencies: 'filter' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["99"], "'DarkSpinner' is defined but never used.", {"desc": "100", "fix": "101"}, {"desc": "102", "fix": "103"}, {"desc": "104", "fix": "105"}, "Update the dependencies array to be: [loadTableInfo, tableName]", {"range": "106", "text": "107"}, "Update the dependencies array to be: [filter.column_name, filter.table_name, loadFilterValues]", {"range": "108", "text": "109"}, "Update the dependencies array to be: [selectedValues, minValue, maxValue, filterType, filter, onUpdate]", {"range": "110", "text": "111"}, [796, 807], "[loadTableInfo, tableName]", [4399, 4438], "[filter.column_name, filter.table_name, loadFilterValues]", [4821, 4869], "[selectedValues, minValue, maxValue, filterType, filter, onUpdate]"]