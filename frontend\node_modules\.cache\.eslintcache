[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SQLConfigPage.js": "15"}, {"size": 347, "mtime": 1752590394396, "results": "16", "hashOfConfig": "17"}, {"size": 30392, "mtime": 1752671327690, "results": "18", "hashOfConfig": "17"}, {"size": 10413, "mtime": 1752270133550, "results": "19", "hashOfConfig": "17"}, {"size": 12078, "mtime": 1752670541650, "results": "20", "hashOfConfig": "17"}, {"size": 13279, "mtime": 1752588517519, "results": "21", "hashOfConfig": "17"}, {"size": 15060, "mtime": 1752590507804, "results": "22", "hashOfConfig": "17"}, {"size": 7535, "mtime": 1752590442917, "results": "23", "hashOfConfig": "17"}, {"size": 17173, "mtime": 1752595239175, "results": "24", "hashOfConfig": "17"}, {"size": 42046, "mtime": 1752671646486, "results": "25", "hashOfConfig": "17"}, {"size": 25860, "mtime": 1752631029819, "results": "26", "hashOfConfig": "17"}, {"size": 13117, "mtime": 1752599738345, "results": "27", "hashOfConfig": "17"}, {"size": 22875, "mtime": 1752611727294, "results": "28", "hashOfConfig": "17"}, {"size": 6001, "mtime": 1752667492127, "results": "29", "hashOfConfig": "17"}, {"size": 14665, "mtime": 1752668152923, "results": "30", "hashOfConfig": "17"}, {"size": 17172, "mtime": 1752671575676, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h4smc4", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js", ["77", "78"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js", ["79"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js", ["80", "81"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js", ["82"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js", ["83"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js", ["84", "85", "86", "87"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js", ["88", "89"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js", ["90", "91"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js", ["92"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SQLConfigPage.js", ["93"], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 89, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 89, "endColumn": 23}, {"ruleId": "98", "severity": 1, "message": "99", "line": 112, "column": 6, "nodeType": "100", "endLine": 112, "endColumn": 8, "suggestions": "101"}, {"ruleId": "94", "severity": 1, "message": "102", "line": 7, "column": 46, "nodeType": "96", "messageId": "97", "endLine": 7, "endColumn": 61}, {"ruleId": "98", "severity": 1, "message": "103", "line": 32, "column": 6, "nodeType": "100", "endLine": 32, "endColumn": 17, "suggestions": "104"}, {"ruleId": "94", "severity": 1, "message": "105", "line": 73, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 73, "endColumn": 34}, {"ruleId": "94", "severity": 1, "message": "106", "line": 12, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 12, "endColumn": 16}, {"ruleId": "94", "severity": 1, "message": "107", "line": 17, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 17, "endColumn": 12}, {"ruleId": "94", "severity": 1, "message": "107", "line": 18, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 18, "endColumn": 12}, {"ruleId": "94", "severity": 1, "message": "108", "line": 275, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 275, "endColumn": 26}, {"ruleId": "94", "severity": 1, "message": "109", "line": 296, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 296, "endColumn": 28}, {"ruleId": "98", "severity": 1, "message": "110", "line": 375, "column": 6, "nodeType": "100", "endLine": 375, "endColumn": 43, "suggestions": "111"}, {"ruleId": "94", "severity": 1, "message": "112", "line": 46, "column": 7, "nodeType": "96", "messageId": "97", "endLine": 46, "endColumn": 25}, {"ruleId": "94", "severity": 1, "message": "113", "line": 656, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 656, "endColumn": 24}, {"ruleId": "98", "severity": 1, "message": "114", "line": 135, "column": 6, "nodeType": "100", "endLine": 135, "endColumn": 45, "suggestions": "115"}, {"ruleId": "98", "severity": 1, "message": "116", "line": 147, "column": 6, "nodeType": "100", "endLine": 147, "endColumn": 54, "suggestions": "117"}, {"ruleId": "94", "severity": 1, "message": "118", "line": 13, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 13, "endColumn": 14}, {"ruleId": "94", "severity": 1, "message": "118", "line": 16, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 16, "endColumn": 14}, "no-unused-vars", "'loadFullSchema' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'loadDataInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["119"], "'SecondaryButton' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadTableInfo'. Either include it or remove the dependency array.", ["120"], "'getColumnTypeBadgeVariant' is assigned a value but never used.", "'SectionHeader' is defined but never used.", "'DarkBadge' is defined but never used.", "'loadConfiguration' is assigned a value but never used.", "'deleteConfiguration' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedVisualization'. Either include it or remove the dependency array.", ["121"], "'getColumnTypeLabel' is assigned a value but never used.", "'positionClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFilterValues'. Either include it or remove the dependency array.", ["122"], "React Hook useEffect has missing dependencies: 'filter' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["123"], "'DarkSpinner' is defined but never used.", {"desc": "124", "fix": "125"}, {"desc": "126", "fix": "127"}, {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, "Update the dependencies array to be: [loadDataInfo]", {"range": "134", "text": "135"}, "Update the dependencies array to be: [loadTableInfo, tableName]", {"range": "136", "text": "137"}, "Update the dependencies array to be: [searchParams, loadingTables, tables, loadSavedVisualization]", {"range": "138", "text": "139"}, "Update the dependencies array to be: [filter.column_name, filter.table_name, loadFilterValues]", {"range": "140", "text": "141"}, "Update the dependencies array to be: [selectedValues, minValue, maxValue, filterType, filter, onUpdate]", {"range": "142", "text": "143"}, [3840, 3842], "[loadDataInfo]", [796, 807], "[loadTableInfo, tableName]", [11489, 11526], "[searchParams, loadingTables, tables, loadSavedVisualization]", [4399, 4438], "[filter.column_name, filter.table_name, loadFilterValues]", [4821, 4869], "[selectedValues, minValue, maxValue, filterType, filter, onUpdate]"]