[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SQLConfigPage.js": "15"}, {"size": 347, "mtime": 1752590394396, "results": "16", "hashOfConfig": "17"}, {"size": 29837, "mtime": 1752670566393, "results": "18", "hashOfConfig": "17"}, {"size": 10413, "mtime": 1752270133550, "results": "19", "hashOfConfig": "17"}, {"size": 12078, "mtime": 1752670541650, "results": "20", "hashOfConfig": "17"}, {"size": 13279, "mtime": 1752588517519, "results": "21", "hashOfConfig": "17"}, {"size": 15060, "mtime": 1752590507804, "results": "22", "hashOfConfig": "17"}, {"size": 7535, "mtime": 1752590442917, "results": "23", "hashOfConfig": "17"}, {"size": 17173, "mtime": 1752595239175, "results": "24", "hashOfConfig": "17"}, {"size": 41363, "mtime": 1752668378593, "results": "25", "hashOfConfig": "17"}, {"size": 25860, "mtime": 1752631029819, "results": "26", "hashOfConfig": "17"}, {"size": 13117, "mtime": 1752599738345, "results": "27", "hashOfConfig": "17"}, {"size": 22875, "mtime": 1752611727294, "results": "28", "hashOfConfig": "17"}, {"size": 6001, "mtime": 1752667492127, "results": "29", "hashOfConfig": "17"}, {"size": 14665, "mtime": 1752668152923, "results": "30", "hashOfConfig": "17"}, {"size": 16484, "mtime": 1752669811811, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h4smc4", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\App.js", ["77"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowsysUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\YellowMindUI.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesOverview.js", ["78"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TableDetailPage.js", ["79", "80"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\TablesListPage.js", ["81"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\VisualBuilderPage.js", ["82"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragVisualPage.js", ["83", "84", "85", "86"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DragDropComponents.js", ["87", "88"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\FilterComponents.js", ["89", "90"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DataStorytellingComponents.js", ["91"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SaveVisualizationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\DashboardPage.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AIZekri\\frontend\\src\\components\\SQLConfigPage.js", ["92"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "93", "line": 388, "column": 18, "nodeType": null}, {"ruleId": "94", "severity": 1, "message": "95", "line": 7, "column": 46, "nodeType": "96", "messageId": "97", "endLine": 7, "endColumn": 61}, {"ruleId": "98", "severity": 1, "message": "99", "line": 32, "column": 6, "nodeType": "100", "endLine": 32, "endColumn": 17, "suggestions": "101"}, {"ruleId": "94", "severity": 1, "message": "102", "line": 73, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 73, "endColumn": 34}, {"ruleId": "94", "severity": 1, "message": "103", "line": 12, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 12, "endColumn": 16}, {"ruleId": "94", "severity": 1, "message": "104", "line": 17, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 17, "endColumn": 12}, {"ruleId": "94", "severity": 1, "message": "104", "line": 18, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 18, "endColumn": 12}, {"ruleId": "94", "severity": 1, "message": "105", "line": 275, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 275, "endColumn": 26}, {"ruleId": "94", "severity": 1, "message": "106", "line": 296, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 296, "endColumn": 28}, {"ruleId": "98", "severity": 1, "message": "107", "line": 375, "column": 6, "nodeType": "100", "endLine": 375, "endColumn": 43, "suggestions": "108"}, {"ruleId": "94", "severity": 1, "message": "109", "line": 46, "column": 7, "nodeType": "96", "messageId": "97", "endLine": 46, "endColumn": 25}, {"ruleId": "94", "severity": 1, "message": "110", "line": 656, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 656, "endColumn": 24}, {"ruleId": "98", "severity": 1, "message": "111", "line": 135, "column": 6, "nodeType": "100", "endLine": 135, "endColumn": 45, "suggestions": "112"}, {"ruleId": "98", "severity": 1, "message": "113", "line": 147, "column": 6, "nodeType": "100", "endLine": 147, "endColumn": 54, "suggestions": "114"}, {"ruleId": "94", "severity": 1, "message": "115", "line": 13, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 13, "endColumn": 14}, {"ruleId": "94", "severity": 1, "message": "115", "line": 16, "column": 3, "nodeType": "96", "messageId": "97", "endLine": 16, "endColumn": 14}, "Parsing error: Adjacent JSX elements must be wrapped in an enclosing tag. Did you want a JSX fragment <>...</>? (388:18)", "no-unused-vars", "'SecondaryButton' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadTableInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["116"], "'getColumnTypeBadgeVariant' is assigned a value but never used.", "'SectionHeader' is defined but never used.", "'DarkBadge' is defined but never used.", "'loadConfiguration' is assigned a value but never used.", "'deleteConfiguration' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSavedVisualization'. Either include it or remove the dependency array.", ["117"], "'getColumnTypeLabel' is assigned a value but never used.", "'positionClasses' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFilterValues'. Either include it or remove the dependency array.", ["118"], "React Hook useEffect has missing dependencies: 'filter' and 'onUpdate'. Either include them or remove the dependency array. If 'onUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["119"], "'DarkSpinner' is defined but never used.", {"desc": "120", "fix": "121"}, {"desc": "122", "fix": "123"}, {"desc": "124", "fix": "125"}, {"desc": "126", "fix": "127"}, "Update the dependencies array to be: [loadTableInfo, tableName]", {"range": "128", "text": "129"}, "Update the dependencies array to be: [searchParams, loadingTables, tables, loadSavedVisualization]", {"range": "130", "text": "131"}, "Update the dependencies array to be: [filter.column_name, filter.table_name, loadFilterValues]", {"range": "132", "text": "133"}, "Update the dependencies array to be: [selectedValues, minValue, maxValue, filterType, filter, onUpdate]", {"range": "134", "text": "135"}, [796, 807], "[loadTableInfo, tableName]", [11510, 11547], "[searchParams, loadingTables, tables, loadSavedVisualization]", [4399, 4438], "[filter.column_name, filter.table_name, loadFilterValues]", [4821, 4869], "[selectedValues, minValue, maxValue, filterType, filter, onUpdate]"]