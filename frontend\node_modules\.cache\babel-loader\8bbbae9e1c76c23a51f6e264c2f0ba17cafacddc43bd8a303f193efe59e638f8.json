{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\SQLConfigPage.js\",\n  _s = $RefreshSig$();\n/**\n * SQLConfigPage - Page de configuration de la base de données SQL Server\n * Permet de configurer dynamiquement la connexion à SQL Server\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { BrAInBIHeader, DarkCard, SectionHeader, PrimaryButton, SecondaryButton, DarkInput, DarkSpinner } from './YellowMindUI';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\nconst SQLConfigPage = () => {\n  _s();\n  var _testResult$server_in, _testResult$connectio;\n  const navigate = useNavigate();\n\n  // États du formulaire\n  const [config, setConfig] = useState({\n    server: 'localhost\\\\SQLSERVER',\n    database: '',\n    driver: 'ODBC Driver 17 for SQL Server',\n    username: '',\n    password: '',\n    use_windows_auth: true,\n    port: '',\n    timeout: 30\n  });\n\n  // États de l'interface\n  const [loading, setLoading] = useState(false);\n  const [testing, setTesting] = useState(false);\n  const [testResult, setTestResult] = useState(null);\n  const [currentConfig, setCurrentConfig] = useState(null);\n  const [toast, setToast] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 5000);\n  };\n\n  // Charger la configuration actuelle au démarrage\n  useEffect(() => {\n    loadCurrentConfig();\n  }, []);\n  const loadCurrentConfig = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      if (response.data.configured) {\n        setCurrentConfig(response.data.config);\n        // Pré-remplir le formulaire avec la config actuelle (sans le mot de passe)\n        setConfig(prev => ({\n          ...prev,\n          ...response.data.config,\n          password: '' // Ne pas pré-remplir le mot de passe pour la sécurité\n        }));\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement de la configuration:', error);\n    }\n  };\n\n  // Gérer les changements dans le formulaire\n  const handleInputChange = (field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Réinitialiser le résultat du test quand on modifie la config\n    setTestResult(null);\n  };\n\n  // Tester la connexion\n  const testConnection = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n    try {\n      setTesting(true);\n      setTestResult(null);\n      const response = await axios.post(`${API_BASE_URL}/config/sql/test`, config);\n      setTestResult(response.data);\n      if (response.data.success) {\n        var _response$data$connec;\n        showToast(`Connexion réussie ! (${(_response$data$connec = response.data.connection_time) === null || _response$data$connec === void 0 ? void 0 : _response$data$connec.toFixed(2)}s)`);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMsg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n      setTestResult({\n        success: false,\n        message: errorMsg,\n        error_code: 'REQUEST_ERROR'\n      });\n      showToast('Erreur lors du test: ' + errorMsg, 'error');\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  // Sauvegarder la configuration\n  const saveConfiguration = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await axios.post(`${API_BASE_URL}/config/sql`, config);\n      if (response.data.success) {\n        showToast('Configuration sauvegardée avec succès !');\n        setCurrentConfig(response.data.config);\n\n        // Rediriger vers la page principale après un délai\n        setTimeout(() => {\n          navigate('/');\n        }, 2000);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMsg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message;\n      showToast('Erreur lors de la sauvegarde: ' + errorMsg, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-950 text-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n      hasData: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-6 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl\",\n              children: \"\\u2699\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n              children: \"Configuration SQL Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Configurez votre connexion \\xE0 la base de donn\\xE9es SQL Server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), currentConfig && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-400\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-green-300\",\n              children: \"Configuration actuelle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Serveur: \", currentConfig.server]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Base: \", currentConfig.database]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Authentification: \", currentConfig.use_windows_auth ? 'Windows' : 'SQL Server']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this), toast.show && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fixed top-4 right-4 z-50 p-4 rounded-lg border ${toast.type === 'success' ? 'bg-green-900/90 border-green-700 text-green-100' : 'bg-red-900/90 border-red-700 text-red-100'} backdrop-blur-sm`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: toast.type === 'success' ? '✅' : '❌'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: toast.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Param\\xE8tres de connexion\",\n          subtitle: \"Configurez les d\\xE9tails de votre serveur SQL Server\",\n          icon: \"\\uD83D\\uDD27\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Serveur SQL Server *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.server,\n                onChange: e => handleInputChange('server', e.target.value),\n                placeholder: \"localhost\\\\\\\\SQLSERVER\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Exemple: localhost\\\\\\\\SQLSERVER, *************, myserver.domain.com\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Base de donn\\xE9es *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.database,\n                onChange: e => handleInputChange('database', e.target.value),\n                placeholder: \"BrAInBIDemo\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Driver ODBC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.driver,\n                onChange: e => handleInputChange('driver', e.target.value),\n                placeholder: \"ODBC Driver 17 for SQL Server\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Port (optionnel)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"number\",\n                value: config.port,\n                onChange: e => handleInputChange('port', e.target.value),\n                placeholder: \"1433\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-3\",\n              children: \"Type d'authentification\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center space-x-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  checked: config.use_windows_auth,\n                  onChange: () => handleInputChange('use_windows_auth', true),\n                  className: \"text-purple-500 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Authentification Windows\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center space-x-2 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  checked: !config.use_windows_auth,\n                  onChange: () => handleInputChange('use_windows_auth', false),\n                  className: \"text-purple-500 focus:ring-purple-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Authentification SQL Server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), !config.use_windows_auth && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Nom d'utilisateur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"text\",\n                value: config.username,\n                onChange: e => handleInputChange('username', e.target.value),\n                placeholder: \"sa\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Mot de passe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n                type: \"password\",\n                value: config.password,\n                onChange: e => handleInputChange('password', e.target.value),\n                placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\",\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full md:w-1/2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Timeout de connexion (secondes)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(DarkInput, {\n              type: \"number\",\n              value: config.timeout,\n              onChange: e => handleInputChange('timeout', parseInt(e.target.value) || 30),\n              placeholder: \"30\",\n              className: \"w-full\",\n              min: \"5\",\n              max: \"300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mt-6 p-4 rounded-lg border ${testResult.success ? 'bg-green-900/20 border-green-700' : 'bg-red-900/20 border-red-700'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: testResult.success ? '✅' : '❌'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `font-medium ${testResult.success ? 'text-green-300' : 'text-red-300'}`,\n              children: testResult.success ? 'Test réussi' : 'Test échoué'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-300 mb-2\",\n            children: testResult.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), testResult.success && testResult.server_info && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-400 space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Version: \", (_testResult$server_in = testResult.server_info.version) === null || _testResult$server_in === void 0 ? void 0 : _testResult$server_in.substring(0, 50), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Base actuelle: \", testResult.server_info.current_database]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Temps de connexion: \", (_testResult$connectio = testResult.connection_time) === null || _testResult$connectio === void 0 ? void 0 : _testResult$connectio.toFixed(3), \"s\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 17\n          }, this), !testResult.success && testResult.error_code && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-red-400\",\n            children: [\"Code d'erreur: \", testResult.error_code]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-4 mt-8\",\n          children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n            onClick: () => navigate('/'),\n            icon: \"\\uD83C\\uDFE0\",\n            disabled: loading || testing,\n            children: \"Retour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n            onClick: testConnection,\n            icon: testing ? null : \"🔍\",\n            disabled: loading || testing || !config.server || !config.database,\n            loading: testing,\n            children: testing ? 'Test en cours...' : 'Tester la connexion'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n            onClick: saveConfiguration,\n            icon: loading ? null : \"💾\",\n            disabled: loading || testing || !config.server || !config.database,\n            loading: loading,\n            className: \"flex-1\",\n            children: loading ? 'Sauvegarde...' : 'Valider la configuration'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n        className: \"mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Aide \\xE0 la configuration\",\n          subtitle: \"Conseils pour configurer votre connexion SQL Server\",\n          icon: \"\\uD83D\\uDCA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 space-y-4 text-sm text-gray-400\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDD27 Formats de serveur courants :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"localhost\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this), \" - Instance par d\\xE9faut locale\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"localhost\\\\\\\\SQLEXPRESS\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this), \" - Instance nomm\\xE9e locale\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"*************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 23\n                }, this), \" - Serveur distant par IP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  className: \"text-purple-400\",\n                  children: \"server.domain.com\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 23\n                }, this), \" - Serveur distant par nom\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\uD83D\\uDD10 Authentification :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Windows\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 23\n                }, this), \" : Utilise votre compte Windows actuel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SQL Server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 23\n                }, this), \" : N\\xE9cessite un nom d'utilisateur et mot de passe SQL\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-300 mb-2\",\n              children: \"\\u26A0\\uFE0F D\\xE9pannage :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-1 ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 V\\xE9rifiez que SQL Server est d\\xE9marr\\xE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Assurez-vous que TCP/IP est activ\\xE9 dans SQL Server Configuration Manager\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 V\\xE9rifiez les param\\xE8tres de pare-feu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u2022 Testez d'abord avec SQL Server Management Studio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(SQLConfigPage, \"Cv1qlQ0i95Murl/syuqCbzUmc9Y=\", false, function () {\n  return [useNavigate];\n});\n_c = SQLConfigPage;\nexport default SQLConfigPage;\nvar _c;\n$RefreshReg$(_c, \"SQLConfigPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "BrAInBIHeader", "DarkCard", "SectionHeader", "PrimaryButton", "SecondaryButton", "DarkInput", "Dark<PERSON><PERSON>ner", "jsxDEV", "_jsxDEV", "API_BASE_URL", "SQLConfigPage", "_s", "_testResult$server_in", "_testResult$connectio", "navigate", "config", "setConfig", "server", "database", "driver", "username", "password", "use_windows_auth", "port", "timeout", "loading", "setLoading", "testing", "setTesting", "testResult", "setTestResult", "currentConfig", "setCurrentConfig", "toast", "setToast", "show", "message", "type", "showToast", "setTimeout", "loadCurrentConfig", "response", "get", "data", "configured", "prev", "error", "console", "handleInputChange", "field", "value", "testConnection", "post", "success", "_response$data$connec", "connection_time", "toFixed", "_error$response", "_error$response$data", "errorMsg", "detail", "error_code", "saveConfiguration", "_error$response2", "_error$response2$data", "className", "children", "hasData", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "subtitle", "icon", "onChange", "e", "target", "placeholder", "checked", "parseInt", "min", "max", "server_info", "version", "substring", "current_database", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/SQLConfigPage.js"], "sourcesContent": ["/**\n * SQLConfigPage - Page de configuration de la base de données SQL Server\n * Permet de configurer dynamiquement la connexion à SQL Server\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  BrAInBIHeader,\n  DarkCard,\n  SectionHeader,\n  PrimaryButton,\n  SecondaryButton,\n  DarkInput,\n  DarkSpinner\n} from './YellowMindUI';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\nconst SQLConfigPage = () => {\n  const navigate = useNavigate();\n  \n  // États du formulaire\n  const [config, setConfig] = useState({\n    server: 'localhost\\\\SQLSERVER',\n    database: '',\n    driver: 'ODBC Driver 17 for SQL Server',\n    username: '',\n    password: '',\n    use_windows_auth: true,\n    port: '',\n    timeout: 30\n  });\n\n  // États de l'interface\n  const [loading, setLoading] = useState(false);\n  const [testing, setTesting] = useState(false);\n  const [testResult, setTestResult] = useState(null);\n  const [currentConfig, setCurrentConfig] = useState(null);\n  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({ show: true, message, type });\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 5000);\n  };\n\n  // Charger la configuration actuelle au démarrage\n  useEffect(() => {\n    loadCurrentConfig();\n  }, []);\n\n  const loadCurrentConfig = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      if (response.data.configured) {\n        setCurrentConfig(response.data.config);\n        // Pré-remplir le formulaire avec la config actuelle (sans le mot de passe)\n        setConfig(prev => ({\n          ...prev,\n          ...response.data.config,\n          password: '' // Ne pas pré-remplir le mot de passe pour la sécurité\n        }));\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement de la configuration:', error);\n    }\n  };\n\n  // Gérer les changements dans le formulaire\n  const handleInputChange = (field, value) => {\n    setConfig(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Réinitialiser le résultat du test quand on modifie la config\n    setTestResult(null);\n  };\n\n  // Tester la connexion\n  const testConnection = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n\n    try {\n      setTesting(true);\n      setTestResult(null);\n\n      const response = await axios.post(`${API_BASE_URL}/config/sql/test`, config);\n      setTestResult(response.data);\n\n      if (response.data.success) {\n        showToast(`Connexion réussie ! (${response.data.connection_time?.toFixed(2)}s)`);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = error.response?.data?.detail || error.message;\n      setTestResult({\n        success: false,\n        message: errorMsg,\n        error_code: 'REQUEST_ERROR'\n      });\n      showToast('Erreur lors du test: ' + errorMsg, 'error');\n    } finally {\n      setTesting(false);\n    }\n  };\n\n  // Sauvegarder la configuration\n  const saveConfiguration = async () => {\n    if (!config.server || !config.database) {\n      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const response = await axios.post(`${API_BASE_URL}/config/sql`, config);\n\n      if (response.data.success) {\n        showToast('Configuration sauvegardée avec succès !');\n        setCurrentConfig(response.data.config);\n        \n        // Rediriger vers la page principale après un délai\n        setTimeout(() => {\n          navigate('/');\n        }, 2000);\n      } else {\n        showToast(response.data.message, 'error');\n      }\n    } catch (error) {\n      const errorMsg = error.response?.data?.detail || error.message;\n      showToast('Erreur lors de la sauvegarde: ' + errorMsg, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-950 text-gray-100\">\n      <BrAInBIHeader hasData={false} />\n      \n      <div className=\"max-w-4xl mx-auto px-6 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center space-x-3 mb-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\">\n              <span className=\"text-xl\">⚙️</span>\n            </div>\n            <div>\n              <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                Configuration SQL Server\n              </h1>\n              <p className=\"text-gray-400\">\n                Configurez votre connexion à la base de données SQL Server\n              </p>\n            </div>\n          </div>\n\n          {/* État de la configuration actuelle */}\n          {currentConfig && (\n            <div className=\"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6\">\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span className=\"text-green-400\">✅</span>\n                <span className=\"font-medium text-green-300\">Configuration actuelle</span>\n              </div>\n              <div className=\"text-sm text-gray-300\">\n                <div>Serveur: {currentConfig.server}</div>\n                <div>Base: {currentConfig.database}</div>\n                <div>Authentification: {currentConfig.use_windows_auth ? 'Windows' : 'SQL Server'}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Toast de notification */}\n        {toast.show && (\n          <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${\n            toast.type === 'success' \n              ? 'bg-green-900/90 border-green-700 text-green-100' \n              : 'bg-red-900/90 border-red-700 text-red-100'\n          } backdrop-blur-sm`}>\n            <div className=\"flex items-center space-x-2\">\n              <span>{toast.type === 'success' ? '✅' : '❌'}</span>\n              <span>{toast.message}</span>\n            </div>\n          </div>\n        )}\n\n        {/* Formulaire de configuration */}\n        <DarkCard>\n          <SectionHeader\n            title=\"Paramètres de connexion\"\n            subtitle=\"Configurez les détails de votre serveur SQL Server\"\n            icon=\"🔧\"\n          />\n\n          <div className=\"mt-6 space-y-6\">\n            {/* Serveur */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Serveur SQL Server *\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.server}\n                  onChange={(e) => handleInputChange('server', e.target.value)}\n                  placeholder=\"localhost\\\\SQLSERVER\"\n                  className=\"w-full\"\n                />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Exemple: localhost\\\\SQLSERVER, *************, myserver.domain.com\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Base de données *\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.database}\n                  onChange={(e) => handleInputChange('database', e.target.value)}\n                  placeholder=\"BrAInBIDemo\"\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n\n            {/* Driver et Port */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Driver ODBC\n                </label>\n                <DarkInput\n                  type=\"text\"\n                  value={config.driver}\n                  onChange={(e) => handleInputChange('driver', e.target.value)}\n                  placeholder=\"ODBC Driver 17 for SQL Server\"\n                  className=\"w-full\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Port (optionnel)\n                </label>\n                <DarkInput\n                  type=\"number\"\n                  value={config.port}\n                  onChange={(e) => handleInputChange('port', e.target.value)}\n                  placeholder=\"1433\"\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n\n            {/* Type d'authentification */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-3\">\n                Type d'authentification\n              </label>\n              <div className=\"flex space-x-4\">\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    checked={config.use_windows_auth}\n                    onChange={() => handleInputChange('use_windows_auth', true)}\n                    className=\"text-purple-500 focus:ring-purple-500\"\n                  />\n                  <span className=\"text-gray-300\">Authentification Windows</span>\n                </label>\n                <label className=\"flex items-center space-x-2 cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    checked={!config.use_windows_auth}\n                    onChange={() => handleInputChange('use_windows_auth', false)}\n                    className=\"text-purple-500 focus:ring-purple-500\"\n                  />\n                  <span className=\"text-gray-300\">Authentification SQL Server</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Credentials SQL Server */}\n            {!config.use_windows_auth && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Nom d'utilisateur\n                  </label>\n                  <DarkInput\n                    type=\"text\"\n                    value={config.username}\n                    onChange={(e) => handleInputChange('username', e.target.value)}\n                    placeholder=\"sa\"\n                    className=\"w-full\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Mot de passe\n                  </label>\n                  <DarkInput\n                    type=\"password\"\n                    value={config.password}\n                    onChange={(e) => handleInputChange('password', e.target.value)}\n                    placeholder=\"••••••••\"\n                    className=\"w-full\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Timeout */}\n            <div className=\"w-full md:w-1/2\">\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Timeout de connexion (secondes)\n              </label>\n              <DarkInput\n                type=\"number\"\n                value={config.timeout}\n                onChange={(e) => handleInputChange('timeout', parseInt(e.target.value) || 30)}\n                placeholder=\"30\"\n                className=\"w-full\"\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n\n          {/* Résultat du test */}\n          {testResult && (\n            <div className={`mt-6 p-4 rounded-lg border ${\n              testResult.success \n                ? 'bg-green-900/20 border-green-700' \n                : 'bg-red-900/20 border-red-700'\n            }`}>\n              <div className=\"flex items-center space-x-2 mb-2\">\n                <span>{testResult.success ? '✅' : '❌'}</span>\n                <span className={`font-medium ${\n                  testResult.success ? 'text-green-300' : 'text-red-300'\n                }`}>\n                  {testResult.success ? 'Test réussi' : 'Test échoué'}\n                </span>\n              </div>\n              <p className=\"text-sm text-gray-300 mb-2\">{testResult.message}</p>\n              \n              {testResult.success && testResult.server_info && (\n                <div className=\"text-xs text-gray-400 space-y-1\">\n                  <div>Version: {testResult.server_info.version?.substring(0, 50)}...</div>\n                  <div>Base actuelle: {testResult.server_info.current_database}</div>\n                  <div>Temps de connexion: {testResult.connection_time?.toFixed(3)}s</div>\n                </div>\n              )}\n              \n              {!testResult.success && testResult.error_code && (\n                <div className=\"text-xs text-red-400\">\n                  Code d'erreur: {testResult.error_code}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Boutons d'action */}\n          <div className=\"flex space-x-4 mt-8\">\n            <SecondaryButton\n              onClick={() => navigate('/')}\n              icon=\"🏠\"\n              disabled={loading || testing}\n            >\n              Retour\n            </SecondaryButton>\n            \n            <SecondaryButton\n              onClick={testConnection}\n              icon={testing ? null : \"🔍\"}\n              disabled={loading || testing || !config.server || !config.database}\n              loading={testing}\n            >\n              {testing ? 'Test en cours...' : 'Tester la connexion'}\n            </SecondaryButton>\n            \n            <PrimaryButton\n              onClick={saveConfiguration}\n              icon={loading ? null : \"💾\"}\n              disabled={loading || testing || !config.server || !config.database}\n              loading={loading}\n              className=\"flex-1\"\n            >\n              {loading ? 'Sauvegarde...' : 'Valider la configuration'}\n            </PrimaryButton>\n          </div>\n        </DarkCard>\n\n        {/* Aide */}\n        <DarkCard className=\"mt-8\">\n          <SectionHeader\n            title=\"Aide à la configuration\"\n            subtitle=\"Conseils pour configurer votre connexion SQL Server\"\n            icon=\"💡\"\n          />\n          \n          <div className=\"mt-6 space-y-4 text-sm text-gray-400\">\n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">🔧 Formats de serveur courants :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• <code className=\"text-purple-400\">localhost</code> - Instance par défaut locale</li>\n                <li>• <code className=\"text-purple-400\">localhost\\\\SQLEXPRESS</code> - Instance nommée locale</li>\n                <li>• <code className=\"text-purple-400\">*************</code> - Serveur distant par IP</li>\n                <li>• <code className=\"text-purple-400\">server.domain.com</code> - Serveur distant par nom</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">🔐 Authentification :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• <strong>Windows</strong> : Utilise votre compte Windows actuel</li>\n                <li>• <strong>SQL Server</strong> : Nécessite un nom d'utilisateur et mot de passe SQL</li>\n              </ul>\n            </div>\n            \n            <div>\n              <h4 className=\"font-medium text-gray-300 mb-2\">⚠️ Dépannage :</h4>\n              <ul className=\"space-y-1 ml-4\">\n                <li>• Vérifiez que SQL Server est démarré</li>\n                <li>• Assurez-vous que TCP/IP est activé dans SQL Server Configuration Manager</li>\n                <li>• Vérifiez les paramètres de pare-feu</li>\n                <li>• Testez d'abord avec SQL Server Management Studio</li>\n              </ul>\n            </div>\n          </div>\n        </DarkCard>\n      </div>\n    </div>\n  );\n};\n\nexport default SQLConfigPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,SAAS,EACTC,WAAW,QACN,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,YAAY,GAAG,uBAAuB;AAE5C,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAC1B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC;IACnCqB,MAAM,EAAE,sBAAsB;IAC9BC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,+BAA+B;IACvCC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,IAAI;IACtBC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC;IAAEuC,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;;EAEjF;EACA,MAAMC,SAAS,GAAGA,CAACF,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvCE,UAAU,CAAC,MAAM;MACfL,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd2C,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM1C,KAAK,CAAC2C,GAAG,CAAC,GAAGjC,YAAY,qBAAqB,CAAC;MACtE,IAAIgC,QAAQ,CAACE,IAAI,CAACC,UAAU,EAAE;QAC5BZ,gBAAgB,CAACS,QAAQ,CAACE,IAAI,CAAC5B,MAAM,CAAC;QACtC;QACAC,SAAS,CAAC6B,IAAI,KAAK;UACjB,GAAGA,IAAI;UACP,GAAGJ,QAAQ,CAACE,IAAI,CAAC5B,MAAM;UACvBM,QAAQ,EAAE,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE;EACF,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1ClC,SAAS,CAAC6B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACI,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;IACH;IACApB,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACpC,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE;MACtCoB,SAAS,CAAC,iEAAiE,EAAE,OAAO,CAAC;MACrF;IACF;IAEA,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChBE,aAAa,CAAC,IAAI,CAAC;MAEnB,MAAMW,QAAQ,GAAG,MAAM1C,KAAK,CAACqD,IAAI,CAAC,GAAG3C,YAAY,kBAAkB,EAAEM,MAAM,CAAC;MAC5Ee,aAAa,CAACW,QAAQ,CAACE,IAAI,CAAC;MAE5B,IAAIF,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QAAA,IAAAC,qBAAA;QACzBhB,SAAS,CAAC,yBAAAgB,qBAAA,GAAwBb,QAAQ,CAACE,IAAI,CAACY,eAAe,cAAAD,qBAAA,uBAA7BA,qBAAA,CAA+BE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAClF,CAAC,MAAM;QACLlB,SAAS,CAACG,QAAQ,CAACE,IAAI,CAACP,OAAO,EAAE,OAAO,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd,MAAMC,QAAQ,GAAG,EAAAF,eAAA,GAAAX,KAAK,CAACL,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAId,KAAK,CAACV,OAAO;MAC9DN,aAAa,CAAC;QACZuB,OAAO,EAAE,KAAK;QACdjB,OAAO,EAAEuB,QAAQ;QACjBE,UAAU,EAAE;MACd,CAAC,CAAC;MACFvB,SAAS,CAAC,uBAAuB,GAAGqB,QAAQ,EAAE,OAAO,CAAC;IACxD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC/C,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAQ,EAAE;MACtCoB,SAAS,CAAC,iEAAiE,EAAE,OAAO,CAAC;MACrF;IACF;IAEA,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMe,QAAQ,GAAG,MAAM1C,KAAK,CAACqD,IAAI,CAAC,GAAG3C,YAAY,aAAa,EAAEM,MAAM,CAAC;MAEvE,IAAI0B,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;QACzBf,SAAS,CAAC,yCAAyC,CAAC;QACpDN,gBAAgB,CAACS,QAAQ,CAACE,IAAI,CAAC5B,MAAM,CAAC;;QAEtC;QACAwB,UAAU,CAAC,MAAM;UACfzB,QAAQ,CAAC,GAAG,CAAC;QACf,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLwB,SAAS,CAACG,QAAQ,CAACE,IAAI,CAACP,OAAO,EAAE,OAAO,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAiB,gBAAA,EAAAC,qBAAA;MACd,MAAML,QAAQ,GAAG,EAAAI,gBAAA,GAAAjB,KAAK,CAACL,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBJ,MAAM,KAAId,KAAK,CAACV,OAAO;MAC9DE,SAAS,CAAC,gCAAgC,GAAGqB,QAAQ,EAAE,OAAO,CAAC;IACjE,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElB,OAAA;IAAKyD,SAAS,EAAC,wCAAwC;IAAAC,QAAA,gBACrD1D,OAAA,CAACR,aAAa;MAACmE,OAAO,EAAE;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEjC/D,OAAA;MAAKyD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1C1D,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1D,OAAA;UAAKyD,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C1D,OAAA;YAAKyD,SAAS,EAAC,oGAAoG;YAAAC,QAAA,eACjH1D,OAAA;cAAMyD,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACN/D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAIyD,SAAS,EAAC,+FAA+F;cAAAC,QAAA,EAAC;YAE9G;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL/D,OAAA;cAAGyD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAE7B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxC,aAAa,iBACZvB,OAAA;UAAKyD,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1E1D,OAAA;YAAKyD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C1D,OAAA;cAAMyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC/D,OAAA;cAAMyD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN/D,OAAA;YAAKyD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpC1D,OAAA;cAAA0D,QAAA,GAAK,WAAS,EAACnC,aAAa,CAACd,MAAM;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C/D,OAAA;cAAA0D,QAAA,GAAK,QAAM,EAACnC,aAAa,CAACb,QAAQ;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC/D,OAAA;cAAA0D,QAAA,GAAK,oBAAkB,EAACnC,aAAa,CAACT,gBAAgB,GAAG,SAAS,GAAG,YAAY;YAAA;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLtC,KAAK,CAACE,IAAI,iBACT3B,OAAA;QAAKyD,SAAS,EAAE,kDACdhC,KAAK,CAACI,IAAI,KAAK,SAAS,GACpB,iDAAiD,GACjD,2CAA2C,mBAC7B;QAAA6B,QAAA,eAClB1D,OAAA;UAAKyD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C1D,OAAA;YAAA0D,QAAA,EAAOjC,KAAK,CAACI,IAAI,KAAK,SAAS,GAAG,GAAG,GAAG;UAAG;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnD/D,OAAA;YAAA0D,QAAA,EAAOjC,KAAK,CAACG;UAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD/D,OAAA,CAACP,QAAQ;QAAAiE,QAAA,gBACP1D,OAAA,CAACN,aAAa;UACZsE,KAAK,EAAC,4BAAyB;UAC/BC,QAAQ,EAAC,uDAAoD;UAC7DC,IAAI,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEF/D,OAAA;UAAKyD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7B1D,OAAA;YAAKyD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACXa,KAAK,EAAEnC,MAAM,CAACE,MAAO;gBACrB0D,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,QAAQ,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC7D4B,WAAW,EAAC,wBAAsB;gBAClCb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACF/D,OAAA;gBAAGyD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN/D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACXa,KAAK,EAAEnC,MAAM,CAACG,QAAS;gBACvByD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,UAAU,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC/D4B,WAAW,EAAC,aAAa;gBACzBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/D,OAAA;YAAKyD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACXa,KAAK,EAAEnC,MAAM,CAACI,MAAO;gBACrBwD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,QAAQ,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC7D4B,WAAW,EAAC,+BAA+B;gBAC3Cb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,QAAQ;gBACba,KAAK,EAAEnC,MAAM,CAACQ,IAAK;gBACnBoD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,MAAM,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC3D4B,WAAW,EAAC,MAAM;gBAClBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN/D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAOyD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA;cAAKyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B1D,OAAA;gBAAOyD,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAC3D1D,OAAA;kBACE6B,IAAI,EAAC,OAAO;kBACZ0C,OAAO,EAAEhE,MAAM,CAACO,gBAAiB;kBACjCqD,QAAQ,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,kBAAkB,EAAE,IAAI,CAAE;kBAC5DiB,SAAS,EAAC;gBAAuC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACF/D,OAAA;kBAAMyD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACR/D,OAAA;gBAAOyD,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,gBAC3D1D,OAAA;kBACE6B,IAAI,EAAC,OAAO;kBACZ0C,OAAO,EAAE,CAAChE,MAAM,CAACO,gBAAiB;kBAClCqD,QAAQ,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,CAAE;kBAC7DiB,SAAS,EAAC;gBAAuC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACF/D,OAAA;kBAAMyD,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAACxD,MAAM,CAACO,gBAAgB,iBACvBd,OAAA;YAAKyD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD1D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,MAAM;gBACXa,KAAK,EAAEnC,MAAM,CAACK,QAAS;gBACvBuD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,UAAU,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC/D4B,WAAW,EAAC,IAAI;gBAChBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN/D,OAAA;cAAA0D,QAAA,gBACE1D,OAAA;gBAAOyD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;gBACRgC,IAAI,EAAC,UAAU;gBACfa,KAAK,EAAEnC,MAAM,CAACM,QAAS;gBACvBsD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,UAAU,EAAE4B,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAE;gBAC/D4B,WAAW,EAAC,kDAAU;gBACtBb,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD/D,OAAA;YAAKyD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9B1D,OAAA;cAAOyD,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAACH,SAAS;cACRgC,IAAI,EAAC,QAAQ;cACba,KAAK,EAAEnC,MAAM,CAACS,OAAQ;cACtBmD,QAAQ,EAAGC,CAAC,IAAK5B,iBAAiB,CAAC,SAAS,EAAEgC,QAAQ,CAACJ,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC,IAAI,EAAE,CAAE;cAC9E4B,WAAW,EAAC,IAAI;cAChBb,SAAS,EAAC,QAAQ;cAClBgB,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL1C,UAAU,iBACTrB,OAAA;UAAKyD,SAAS,EAAE,8BACdpC,UAAU,CAACwB,OAAO,GACd,kCAAkC,GAClC,8BAA8B,EACjC;UAAAa,QAAA,gBACD1D,OAAA;YAAKyD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C1D,OAAA;cAAA0D,QAAA,EAAOrC,UAAU,CAACwB,OAAO,GAAG,GAAG,GAAG;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7C/D,OAAA;cAAMyD,SAAS,EAAE,eACfpC,UAAU,CAACwB,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACrD;cAAAa,QAAA,EACArC,UAAU,CAACwB,OAAO,GAAG,aAAa,GAAG;YAAa;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN/D,OAAA;YAAGyD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAErC,UAAU,CAACO;UAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEjE1C,UAAU,CAACwB,OAAO,IAAIxB,UAAU,CAACsD,WAAW,iBAC3C3E,OAAA;YAAKyD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C1D,OAAA;cAAA0D,QAAA,GAAK,WAAS,GAAAtD,qBAAA,GAACiB,UAAU,CAACsD,WAAW,CAACC,OAAO,cAAAxE,qBAAA,uBAA9BA,qBAAA,CAAgCyE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,KAAG;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACzE/D,OAAA;cAAA0D,QAAA,GAAK,iBAAe,EAACrC,UAAU,CAACsD,WAAW,CAACG,gBAAgB;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnE/D,OAAA;cAAA0D,QAAA,GAAK,sBAAoB,GAAArD,qBAAA,GAACgB,UAAU,CAAC0B,eAAe,cAAA1C,qBAAA,uBAA1BA,qBAAA,CAA4B2C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CACN,EAEA,CAAC1C,UAAU,CAACwB,OAAO,IAAIxB,UAAU,CAACgC,UAAU,iBAC3CrD,OAAA;YAAKyD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,GAAC,iBACrB,EAACrC,UAAU,CAACgC,UAAU;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGD/D,OAAA;UAAKyD,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC1D,OAAA,CAACJ,eAAe;YACdmF,OAAO,EAAEA,CAAA,KAAMzE,QAAQ,CAAC,GAAG,CAAE;YAC7B4D,IAAI,EAAC,cAAI;YACTc,QAAQ,EAAE/D,OAAO,IAAIE,OAAQ;YAAAuC,QAAA,EAC9B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC,eAElB/D,OAAA,CAACJ,eAAe;YACdmF,OAAO,EAAEpC,cAAe;YACxBuB,IAAI,EAAE/C,OAAO,GAAG,IAAI,GAAG,IAAK;YAC5B6D,QAAQ,EAAE/D,OAAO,IAAIE,OAAO,IAAI,CAACZ,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAS;YACnEO,OAAO,EAAEE,OAAQ;YAAAuC,QAAA,EAEhBvC,OAAO,GAAG,kBAAkB,GAAG;UAAqB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAElB/D,OAAA,CAACL,aAAa;YACZoF,OAAO,EAAEzB,iBAAkB;YAC3BY,IAAI,EAAEjD,OAAO,GAAG,IAAI,GAAG,IAAK;YAC5B+D,QAAQ,EAAE/D,OAAO,IAAIE,OAAO,IAAI,CAACZ,MAAM,CAACE,MAAM,IAAI,CAACF,MAAM,CAACG,QAAS;YACnEO,OAAO,EAAEA,OAAQ;YACjBwC,SAAS,EAAC,QAAQ;YAAAC,QAAA,EAEjBzC,OAAO,GAAG,eAAe,GAAG;UAA0B;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGX/D,OAAA,CAACP,QAAQ;QAACgE,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACxB1D,OAAA,CAACN,aAAa;UACZsE,KAAK,EAAC,4BAAyB;UAC/BC,QAAQ,EAAC,qDAAqD;UAC9DC,IAAI,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEF/D,OAAA;UAAKyD,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnD1D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAIyD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF/D,OAAA;cAAIyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5B1D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAMyD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,oCAA6B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1F/D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAMyD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gCAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClG/D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAMyD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,6BAAyB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1F/D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAMyD,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8BAA0B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEN/D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAIyD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzE/D,OAAA;cAAIyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5B1D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAA0D,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,0CAAsC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzE/D,OAAA;gBAAA0D,QAAA,GAAI,SAAE,eAAA1D,OAAA;kBAAA0D,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,4DAAqD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEN/D,OAAA;YAAA0D,QAAA,gBACE1D,OAAA;cAAIyD,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClE/D,OAAA;cAAIyD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC5B1D,OAAA;gBAAA0D,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C/D,OAAA;gBAAA0D,QAAA,EAAI;cAA0E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF/D,OAAA;gBAAA0D,QAAA,EAAI;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C/D,OAAA;gBAAA0D,QAAA,EAAI;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CA1aID,aAAa;EAAA,QACAZ,WAAW;AAAA;AAAA2F,EAAA,GADxB/E,aAAa;AA4anB,eAAeA,aAAa;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}