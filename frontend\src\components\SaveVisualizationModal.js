/**
 * SaveVisualizationModal - Modal pour sauvegarder une visualisation
 * Permet de saisir le nom et la description d'une visualisation à sauvegarder
 */

import React, { useState } from 'react';
import { DarkCard, PrimaryButton, SecondaryButton, DarkInput } from './YellowMindUI';

const SaveVisualizationModal = ({ 
  isOpen, 
  onClose, 
  onSave, 
  loading = false,
  currentConfig = null 
}) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');

  // Réinitialiser les champs quand le modal s'ouvre
  React.useEffect(() => {
    if (isOpen) {
      setName('');
      setDescription('');
      setTags('');
    }
  }, [isOpen]);

  const handleSave = () => {
    if (!name.trim()) {
      return;
    }

    const tagsArray = tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    onSave({
      name: name.trim(),
      description: description.trim() || null,
      tags: tagsArray
    });
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <DarkCard className="w-full max-w-md">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-lg">💾</span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-100">
                Enregistrer la visualisation
              </h2>
              <p className="text-sm text-gray-400">
                Sauvegardez votre configuration pour la réutiliser plus tard
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-200 transition-colors"
            disabled={loading}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* Nom de la visualisation */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Nom de la visualisation *
            </label>
            <DarkInput
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ex: Ventes par région"
              className="w-full"
              disabled={loading}
              autoFocus
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Description (optionnel)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Décrivez brièvement cette visualisation..."
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
              rows={3}
              disabled={loading}
            />
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Tags (optionnel)
            </label>
            <DarkInput
              type="text"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              placeholder="ventes, région, mensuel (séparés par des virgules)"
              className="w-full"
              disabled={loading}
            />
            <p className="text-xs text-gray-500 mt-1">
              Séparez les tags par des virgules pour faciliter la recherche
            </p>
          </div>

          {/* Aperçu de la configuration */}
          {currentConfig && (
            <div className="bg-gray-800/50 rounded-lg p-3 border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Configuration actuelle :</h4>
              <div className="space-y-1 text-xs text-gray-400">
                <div>📊 Type : {currentConfig.chartType}</div>
                {currentConfig.xAxis && <div>📈 Axe X : {currentConfig.xAxis.name}</div>}
                {currentConfig.yAxis && <div>📊 Axe Y : {currentConfig.yAxis.name}</div>}
                {currentConfig.values && <div>💎 Valeurs : {currentConfig.values.name} ({currentConfig.aggFunction})</div>}
                {currentConfig.legend && <div>🏷️ Légende : {currentConfig.legend.name}</div>}
                {currentConfig.filters && currentConfig.filters.length > 0 && (
                  <div>🔍 Filtres : {currentConfig.filters.length} actif(s)</div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="flex space-x-3 mt-6">
          <SecondaryButton
            onClick={onClose}
            disabled={loading}
            className="flex-1"
          >
            Annuler
          </SecondaryButton>
          <PrimaryButton
            onClick={handleSave}
            disabled={!name.trim() || loading}
            loading={loading}
            className="flex-1"
            icon={loading ? null : "💾"}
          >
            {loading ? 'Sauvegarde...' : 'Enregistrer'}
          </PrimaryButton>
        </div>
      </DarkCard>
    </div>
  );
};

export default SaveVisualizationModal;
