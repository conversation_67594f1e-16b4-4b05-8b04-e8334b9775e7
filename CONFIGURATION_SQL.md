# Configuration SQL Dynamique - BrAInBI

BrAInBI supporte maintenant la configuration dynamique de la base de données SQL Server, permettant de se connecter à n'importe quelle base SQL Server sans modification du code.

## 🎯 Fonctionnalités

- ✅ Configuration dynamique du serveur SQL Server
- ✅ Support de l'authentification Windows et SQL Server
- ✅ Test de connexion avant sauvegarde
- ✅ Gestion d'erreurs détaillée
- ✅ Interface utilisateur intuitive
- ✅ Validation des paramètres

## 🔧 Configuration via l'interface

### 1. Accéder à la page de configuration

- **URL directe** : `http://localhost:3001/config/sql`
- **Via le header** : Cliquez sur "Config" dans la barre de navigation
- **Via la page principale** : Cliquez sur "Configuration SQL" ou la carte "Configuration"

### 2. Remplir le formulaire

#### Paramètres obligatoires :
- **Serveur SQL Server** : Nom ou adresse IP du serveur
  - Exemples : `localhost`, `localhost\\SQLEXPRESS`, `*************`, `server.domain.com`
- **Base de données** : Nom de la base de données à utiliser

#### Paramètres optionnels :
- **Driver ODBC** : Par défaut "ODBC Driver 17 for SQL Server"
- **Port** : Port de connexion (1433 par défaut)
- **Timeout** : Délai d'attente en secondes (30 par défaut)

#### Authentification :
- **Windows** : Utilise votre compte Windows actuel
- **SQL Server** : Nécessite un nom d'utilisateur et mot de passe

### 3. Tester et valider

1. Cliquez sur **"Tester la connexion"** pour vérifier les paramètres
2. Si le test réussit, cliquez sur **"Valider la configuration"**
3. L'application se reconnecte automatiquement avec la nouvelle configuration

## 🔌 Configuration via API

### Endpoints disponibles

#### POST /config/sql/test
Teste une configuration sans la sauvegarder.

```json
{
  "server": "localhost\\SQLSERVER",
  "database": "BrAInBIDemo",
  "driver": "ODBC Driver 17 for SQL Server",
  "use_windows_auth": true,
  "port": null,
  "timeout": 30
}
```

**Réponse :**
```json
{
  "success": true,
  "message": "Connexion réussie à localhost\\SQLSERVER/BrAInBIDemo",
  "connection_time": 0.234,
  "server_info": {
    "version": "Microsoft SQL Server 2019...",
    "current_database": "BrAInBIDemo",
    "server": "localhost\\SQLSERVER"
  }
}
```

#### POST /config/sql
Sauvegarde une configuration après test.

```json
{
  "server": "localhost\\SQLSERVER",
  "database": "BrAInBIDemo",
  "use_windows_auth": false,
  "username": "sa",
  "password": "VotreMotDePasse"
}
```

#### GET /config/sql/current
Récupère la configuration actuelle (sans le mot de passe).

```json
{
  "configured": true,
  "config": {
    "server": "localhost\\SQLSERVER",
    "database": "BrAInBIDemo",
    "driver": "ODBC Driver 17 for SQL Server",
    "use_windows_auth": true,
    "port": null,
    "timeout": 30
  }
}
```

## 📋 Exemples de configuration

### Configuration locale avec Windows Auth
```json
{
  "server": "localhost",
  "database": "BrAInBIDemo",
  "use_windows_auth": true
}
```

### Configuration avec instance nommée
```json
{
  "server": "localhost\\SQLEXPRESS",
  "database": "MyDatabase",
  "use_windows_auth": true
}
```

### Configuration avec authentification SQL
```json
{
  "server": "*************",
  "database": "Production",
  "use_windows_auth": false,
  "username": "brainbi_user",
  "password": "SecurePassword123"
}
```

### Configuration serveur distant avec port
```json
{
  "server": "sql.company.com",
  "database": "Analytics",
  "port": 1433,
  "use_windows_auth": true,
  "timeout": 60
}
```

## ⚠️ Gestion d'erreurs

L'application gère automatiquement les erreurs courantes :

### Erreurs d'authentification
- **Message** : "Échec de l'authentification. Vérifiez le nom d'utilisateur et le mot de passe."
- **Solution** : Vérifier les credentials ou passer en authentification Windows

### Base de données introuvable
- **Message** : "Impossible d'ouvrir la base de données 'XXX'. Vérifiez que la base existe."
- **Solution** : Vérifier le nom de la base ou créer la base de données

### Serveur introuvable
- **Message** : "Serveur 'XXX' introuvable. Vérifiez le nom du serveur et la connectivité réseau."
- **Solution** : Vérifier le nom du serveur, la connectivité réseau, et que SQL Server est démarré

### Driver ODBC manquant
- **Message** : "Driver ODBC 'XXX' non trouvé. Installez le driver ou utilisez un autre nom."
- **Solution** : Installer le driver ODBC ou utiliser un driver disponible

## 🔄 Migration depuis l'ancienne version

L'application reste compatible avec l'ancienne configuration hardcodée. Si aucune configuration dynamique n'est définie, elle utilise les valeurs par défaut du fichier `config.py`.

Pour migrer :
1. Accédez à `/config/sql`
2. Configurez votre base de données
3. Testez et validez la configuration
4. L'ancienne configuration sera remplacée

## 🛠️ Dépannage

### Vérifier l'état de la configuration
```bash
curl http://localhost:8000/config/sql/current
```

### Tester une configuration
```bash
curl -X POST http://localhost:8000/config/sql/test \
  -H "Content-Type: application/json" \
  -d '{
    "server": "localhost",
    "database": "master",
    "use_windows_auth": true
  }'
```

### Vérifier la santé de l'API
```bash
curl http://localhost:8000/health
```

### Logs utiles
- Les erreurs de connexion sont affichées dans la console du serveur
- Les tests de connexion incluent des codes d'erreur spécifiques
- L'interface affiche des messages d'erreur détaillés

## 📝 Notes importantes

1. **Sécurité** : Les mots de passe ne sont pas stockés en clair et ne sont pas retournés par l'API
2. **Performance** : La configuration est mise en cache en mémoire pour éviter les reconnexions
3. **Persistance** : La configuration est stockée dans la variable globale de l'application
4. **Redémarrage** : La configuration doit être refaite après redémarrage du serveur

## 🔗 Liens utiles

- **Interface de configuration** : http://localhost:3001/config/sql
- **Documentation API** : http://localhost:8000/docs
- **État de santé** : http://localhost:8000/health
- **Test de script** : `python backend/test_sql_config.py`
