"""
BrAInBI - Backend FastAPI (Version SQL Server)
Business Intelligence augmentée par l'IA
Connexion directe à SQL Server en mode live pour l'analyse par Azure OpenAI
"""

import os
import pandas as pd
import pyodbc
import openai
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Dict, List, Any, Optional, Tuple
import json
import re
import math
from datetime import datetime
from config import AzureOpenAIConfig, ModelSettings, validate_config, print_config_status

# Configuration Azure OpenAI
openai.api_type = AzureOpenAIConfig.API_TYPE
openai.api_base = AzureOpenAIConfig.API_BASE
openai.api_version = AzureOpenAIConfig.API_VERSION
openai.api_key = AzureOpenAIConfig.API_KEY

# Vérification de la configuration au démarrage
config_valid, config_message = validate_config()
if not config_valid:
    print("⚠️  ATTENTION: Configuration Azure OpenAI non valide!")
    print_config_status()
else:
    print("✅ Configuration Azure OpenAI chargée avec succès")

# Settings pour le modèle
ai_settings = ModelSettings()

app = FastAPI(
    title="BrAInBI API",
    description="Business Intelligence augmentée par l'IA - API pour l'analyse de données SQL Server avec Azure OpenAI",
    version="2.0.0"
)

# Configuration CORS pour permettre les requêtes depuis le frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",  # Vite dev server
        "http://127.0.0.1:5173"   # Vite dev server
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Middleware de logging pour diagnostiquer les requêtes
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.now()

    # Log de la requête entrante
    print(f"🌐 [{start_time.strftime('%H:%M:%S')}] {request.method} {request.url}")
    print(f"🌐 Headers: {dict(request.headers)}")

    response = await call_next(request)

    # Log de la réponse
    process_time = (datetime.now() - start_time).total_seconds()
    print(f"🌐 [{datetime.now().strftime('%H:%M:%S')}] Response: {response.status_code} ({process_time:.2f}s)")

    return response

# Variables globales pour l'historique de conversation
conversation_history: List[Dict[str, str]] = []

# BrAInBI - multi-table live support - Variables globales pour les données multi-tables
dataframes: Dict[str, pd.DataFrame] = {}
table_relations: Dict[str, Any] = {}
schema_metadata: Dict[str, Any] = {}

# Variables globales pour la sauvegarde des visualisations (déclarées après les modèles)
SAVED_VISUALS_FILE = "saved_visualizations.json"

# Configuration SQL Server
SQL_SERVER = "DESKTOP-5LP9GN5\\MSSQLSERVER1"
SQL_DATABASE = "BrAInBI_Test"
SQL_TABLE = "Sales"  # Table par défaut pour compatibilité
SQL_DRIVER = "ODBC Driver 17 for SQL Server"

def clean_nan_values(obj):
    """
    Nettoie les valeurs NaN et Infinity pour la sérialisation JSON
    """
    if isinstance(obj, dict):
        return {key: clean_nan_values(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [clean_nan_values(item) for item in obj]
    elif isinstance(obj, float):
        if pd.isna(obj) or obj == float('inf') or obj == float('-inf'):
            return None
        return obj
    else:
        return obj

def get_sql_connection():
    """
    Crée une connexion SQL Server réutilisable
    Utilise la configuration dynamique si disponible, sinon les valeurs par défaut
    """
    if current_sql_config is not None:
        return get_connection_from_config(current_sql_config)
    else:
        # Configuration par défaut (pour compatibilité)
        conn_str = f"DRIVER={{{SQL_DRIVER}}};SERVER={SQL_SERVER};DATABASE={SQL_DATABASE};Trusted_Connection=yes;"
        return pyodbc.connect(conn_str)



# BrAInBI - multi-table live support - Chargement de toutes les tables
def load_all_tables():
    """
    Charge toutes les tables de la base de données dans un dictionnaire de DataFrames
    Nécessite une configuration SQL valide
    """
    global dataframes, schema_metadata

    # Vérifier qu'une configuration SQL est définie
    if not is_sql_configured():
        print("⚠️ Aucune configuration SQL définie. Utilisez /config/sql pour configurer la connexion.")
        dataframes = {}
        schema_metadata = {}
        return

    try:
        conn = get_sql_connection()

        # Récupérer la liste de toutes les tables
        tables_query = """
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """

        tables_df = pd.read_sql(tables_query, conn)
        dataframes.clear()

        print(f"🔍 Chargement de {len(tables_df)} tables depuis {SQL_DATABASE}...")

        # Charger chaque table
        for _, row in tables_df.iterrows():
            table_name = row['TABLE_NAME']
            try:
                query = f"SELECT * FROM [{table_name}]"
                df = pd.read_sql(query, conn)

                # Vérifier si la table est vide et créer un DataFrame minimal si nécessaire
                if df.empty:
                    # Récupérer au moins la structure des colonnes
                    structure_query = f"SELECT TOP 0 * FROM [{table_name}]"
                    df = pd.read_sql(structure_query, conn)

                dataframes[table_name] = df
                print(f"✅ Table {table_name}: {len(df)} lignes, {len(df.columns)} colonnes")
            except Exception as e:
                print(f"⚠️  Erreur lors du chargement de {table_name}: {e}")
                continue

        conn.close()

        # Mettre à jour les métadonnées
        schema_metadata = {
            "database": SQL_DATABASE,
            "tables_count": len(dataframes),
            "total_rows": sum(len(df) for df in dataframes.values()),
            "tables": {name: {"rows": len(df), "columns": len(df.columns)}
                      for name, df in dataframes.items()}
        }

        print(f"📊 Chargement terminé: {len(dataframes)} tables, {schema_metadata['total_rows']} lignes totales")
        return dataframes

    except Exception as e:
        error_message = f"Erreur lors du chargement des tables: {str(e)}"
        print(f"❌ {error_message}")
        raise HTTPException(status_code=500, detail=error_message)

# BrAInBI - multi-table live support - Détection des relations entre tables
def detect_relations(dataframes_dict):
    """
    Détecte les relations entre tables en analysant les noms de colonnes et les valeurs
    """
    global table_relations

    relations = []

    try:
        table_names = list(dataframes_dict.keys())

        for table1 in table_names:
            df1 = dataframes_dict[table1]

            for table2 in table_names:
                if table1 >= table2:  # Éviter les doublons
                    continue

                df2 = dataframes_dict[table2]

                # Analyser les colonnes pour détecter les relations
                for col1 in df1.columns:
                    for col2 in df2.columns:

                        # Détection par nom de colonne
                        relation_detected = False
                        relation_type = "unknown"
                        confidence = 0.0

                        # Pattern 1: Même nom de colonne
                        if col1.lower() == col2.lower():
                            relation_detected = True
                            relation_type = "same_name"
                            confidence = 0.8

                        # Pattern 2: ID et TableID
                        elif (col1.lower() == 'id' and col2.lower() == f'{table1.lower()}id') or \
                             (col2.lower() == 'id' and col1.lower() == f'{table2.lower()}id'):
                            relation_detected = True
                            relation_type = "primary_foreign"
                            confidence = 0.9

                        # Pattern 3: FK_ prefix
                        elif col1.lower().startswith('fk_') or col2.lower().startswith('fk_'):
                            relation_detected = True
                            relation_type = "foreign_key"
                            confidence = 0.7

                        # Pattern 4: Colonnes se terminant par ID
                        elif col1.lower().endswith('id') and col2.lower().endswith('id'):
                            if col1.lower().replace('id', '') in table2.lower() or \
                               col2.lower().replace('id', '') in table1.lower():
                                relation_detected = True
                                relation_type = "id_pattern"
                                confidence = 0.6

                        if relation_detected:
                            # Vérifier la correspondance des valeurs
                            try:
                                values1 = set(df1[col1].dropna().astype(str).unique()[:100])
                                values2 = set(df2[col2].dropna().astype(str).unique()[:100])

                                if values1 and values2:
                                    common_values = values1 & values2
                                    value_match_ratio = len(common_values) / min(len(values1), len(values2))

                                    if value_match_ratio > 0.1:  # Au moins 10% de valeurs communes
                                        final_confidence = confidence * (0.5 + 0.5 * value_match_ratio)

                                        relations.append({
                                            "from_table": table1,
                                            "from_column": col1,
                                            "to_table": table2,
                                            "to_column": col2,
                                            "relation_type": relation_type,
                                            "confidence": final_confidence,
                                            "common_values": len(common_values),
                                            "value_match_ratio": value_match_ratio
                                        })
                            except Exception:
                                continue

        # Trier par confiance décroissante
        relations.sort(key=lambda x: x['confidence'], reverse=True)

        table_relations = {
            "relations": relations,
            "summary": {
                "total_relations": len(relations),
                "high_confidence": len([r for r in relations if r['confidence'] > 0.7]),
                "medium_confidence": len([r for r in relations if 0.4 <= r['confidence'] <= 0.7]),
                "low_confidence": len([r for r in relations if r['confidence'] < 0.4])
            }
        }

        print(f"🔗 Relations détectées: {len(relations)} ({table_relations['summary']['high_confidence']} haute confiance)")
        return table_relations

    except Exception as e:
        print(f"❌ Erreur lors de la détection des relations: {e}")
        return {"relations": [], "summary": {"total_relations": 0}}

def read_data_from_sql():
    """
    Compatibilité: Lit les données de la table par défaut ou charge toutes les tables
    """
    global dataframes

    # Si les dataframes ne sont pas chargés, charger toutes les tables
    if not dataframes:
        load_all_tables()
        detect_relations(dataframes)

    # Retourner la table par défaut pour compatibilité
    if SQL_TABLE in dataframes:
        return dataframes[SQL_TABLE]
    elif dataframes:
        # Retourner la première table disponible
        first_table = next(iter(dataframes.keys()))
        return dataframes[first_table]
    else:
        raise HTTPException(status_code=500, detail="Aucune table disponible")

# BrAInBI schema detection - Découverte automatique du schéma et des relations
def discover_schema_and_relationships():
    """
    Découvre automatiquement toutes les tables, colonnes et relations de la base de données
    """
    try:
        conn = get_sql_connection()

        # 1. Récupérer toutes les tables
        tables_query = """
        SELECT TABLE_NAME, TABLE_TYPE
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        tables_df = pd.read_sql(tables_query, conn)

        schema_info = {
            "database": SQL_DATABASE,
            "tables": {},
            "relationships": []
        }

        # 2. Pour chaque table, récupérer les colonnes et échantillon
        for _, table_row in tables_df.iterrows():
            table_name = table_row['TABLE_NAME']

            # Colonnes et types
            columns_query = f"""
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION
            """
            columns_df = pd.read_sql(columns_query, conn)

            # Échantillon de données (TOP 5)
            try:
                sample_query = f"SELECT TOP 5 * FROM [{table_name}]"
                sample_df = pd.read_sql(sample_query, conn)
                sample_data = sample_df.to_dict('records')
            except:
                sample_data = []

            # Détecter les clés primaires potentielles
            primary_keys = []
            foreign_keys = []

            for _, col in columns_df.iterrows():
                col_name = col['COLUMN_NAME']
                col_type = col['DATA_TYPE']

                # Détection PK par nom et type
                if (col_name.upper() in ['ID', 'PK'] or
                    col_name.upper().endswith('ID') and len(col_name) <= 10 or
                    col_name.upper().startswith('PK_')):

                    if col_type in ['int', 'bigint', 'uniqueidentifier']:
                        if col_name.upper() == 'ID' or col_name.upper() == f'{table_name.upper()}ID':
                            primary_keys.append(col_name)
                        else:
                            foreign_keys.append({
                                "column": col_name,
                                "referenced_table": col_name.replace('ID', '').replace('Id', ''),
                                "confidence": "name_pattern"
                            })

            schema_info["tables"][table_name] = {
                "columns": columns_df.to_dict('records'),
                "sample_data": sample_data,
                "row_count": len(sample_df) if 'sample_df' in locals() else 0,
                "primary_keys": primary_keys,
                "foreign_keys": foreign_keys
            }

        # 3. Analyser les relations par matching de valeurs
        schema_info["relationships"] = detect_relationships_by_values(conn, schema_info["tables"])

        conn.close()
        return schema_info

    except Exception as e:
        error_message = f"Erreur lors de la découverte du schéma: {str(e)}"
        print(f"❌ {error_message}")
        raise HTTPException(status_code=500, detail=error_message)

def detect_relationships_by_values(conn, tables_info):
    """
    Détecte les relations entre tables en analysant les valeurs
    """
    relationships = []

    try:
        table_names = list(tables_info.keys())

        for i, table1 in enumerate(table_names):
            for j, table2 in enumerate(table_names):
                if i >= j:  # Éviter les doublons et auto-références
                    continue

                # Chercher des colonnes avec des noms similaires
                cols1 = [col['COLUMN_NAME'] for col in tables_info[table1]['columns']]
                cols2 = [col['COLUMN_NAME'] for col in tables_info[table2]['columns']]

                for col1 in cols1:
                    for col2 in cols2:
                        # Vérifier si les noms suggèrent une relation
                        if (col1.upper() == col2.upper() or
                            col1.upper().endswith('ID') and col2.upper() == 'ID' or
                            col2.upper().endswith('ID') and col1.upper() == 'ID'):

                            # Vérifier par échantillonnage de valeurs
                            try:
                                query1 = f"SELECT DISTINCT TOP 10 [{col1}] FROM [{table1}] WHERE [{col1}] IS NOT NULL"
                                query2 = f"SELECT DISTINCT TOP 10 [{col2}] FROM [{table2}] WHERE [{col2}] IS NOT NULL"

                                values1 = pd.read_sql(query1, conn)[col1].tolist()
                                values2 = pd.read_sql(query2, conn)[col2].tolist()

                                # Calculer le pourcentage de valeurs communes
                                common_values = set(values1) & set(values2)
                                if len(common_values) > 0:
                                    confidence = len(common_values) / max(len(set(values1)), len(set(values2)))

                                    if confidence > 0.3:  # Seuil de confiance
                                        relationships.append({
                                            "from_table": table1,
                                            "from_column": col1,
                                            "to_table": table2,
                                            "to_column": col2,
                                            "confidence": confidence,
                                            "common_values_count": len(common_values),
                                            "relationship_type": "foreign_key" if col1.upper().endswith('ID') else "reference"
                                        })
                            except:
                                continue

        return relationships

    except Exception as e:
        print(f"Erreur lors de la détection des relations: {str(e)}")
        return []

# Fonctions utilitaires pour la gestion des visualisations sauvegardées
def load_saved_visualizations():
    """Charge les visualisations sauvegardées depuis le fichier JSON"""
    global saved_visualizations
    try:
        if os.path.exists(SAVED_VISUALS_FILE):
            with open(SAVED_VISUALS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                saved_visualizations = {}
                for viz_id, viz_data in data.items():
                    # Convertir les dates string en datetime
                    viz_data['created_at'] = datetime.fromisoformat(viz_data['created_at'])
                    viz_data['updated_at'] = datetime.fromisoformat(viz_data['updated_at'])
                    # Reconstruire les filtres
                    filters = []
                    for filter_data in viz_data.get('filters', []):
                        filters.append(AppliedFilter(**filter_data))
                    viz_data['filters'] = filters
                    saved_visualizations[viz_id] = SavedVisualization(**viz_data)
        print(f"✅ Chargé {len(saved_visualizations)} visualisations sauvegardées")
    except Exception as e:
        print(f"⚠️ Erreur lors du chargement des visualisations: {str(e)}")
        saved_visualizations = {}

def save_visualizations_to_file():
    """Sauvegarde les visualisations dans le fichier JSON"""
    try:
        data = {}
        for viz_id, viz in saved_visualizations.items():
            viz_dict = viz.model_dump()
            # Convertir les dates en string
            viz_dict['created_at'] = viz_dict['created_at'].isoformat()
            viz_dict['updated_at'] = viz_dict['updated_at'].isoformat()
            data[viz_id] = viz_dict

        with open(SAVED_VISUALS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"✅ Sauvegardé {len(saved_visualizations)} visualisations")
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde des visualisations: {str(e)}")

def generate_visualization_id() -> str:
    """Génère un ID unique pour une visualisation"""
    import uuid
    return str(uuid.uuid4())



def call_azure_gpt(prompt: str, conversation_context: List[Dict] = None, system_message: str = None) -> str:
    """Appelle Azure OpenAI GPT avec gestion d'erreurs robuste"""
    try:
        messages = []
        
        # Message système
        if system_message:
            messages.append({"role": "system", "content": system_message})
        else:
            messages.append({
                "role": "system", 
                "content": "Tu es un expert en analyse de données et business intelligence. Réponds de manière claire et précise."
            })
        
        # Ajouter le contexte de conversation si fourni
        if conversation_context:
            for entry in conversation_context[-5:]:  # Garder seulement les 5 derniers échanges
                messages.append({"role": "user", "content": entry["user"]})
                messages.append({"role": "assistant", "content": entry["ai"]})
        
        # Ajouter le prompt actuel
        messages.append({"role": "user", "content": prompt})
        
        response = openai.ChatCompletion.create(
            engine=ai_settings.deployment_name,
            messages=messages,
            temperature=ai_settings.temperature,
            max_tokens=ai_settings.max_tokens
        )
        
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        error_msg = f"Erreur Azure OpenAI: {str(e)}"
        print(f"❌ {error_msg}")
        return f"Désolé, je ne peux pas traiter votre demande pour le moment. Erreur: {error_msg}"

def extract_json_from_text(text: str) -> Optional[Dict]:
    """Extrait un objet JSON valide d'un texte"""
    try:
        # Chercher des patterns JSON dans le texte
        json_patterns = [
            r'\{[^{}]*\}',  # Simple object
            r'\{[^{}]*\{[^{}]*\}[^{}]*\}',  # Nested object
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        return None
    except Exception:
        return None

def analyze_data_quality(df: pd.DataFrame) -> str:
    """Analyse la qualité des données avec l'IA"""
    try:
        # Statistiques de base sur la qualité
        total_rows = len(df)
        missing_data = df.isnull().sum()
        duplicate_rows = df.duplicated().sum()
        
        # Informations sur les types de données
        data_types = df.dtypes.to_dict()
        
        # Statistiques descriptives pour les colonnes numériques
        numeric_stats = df.describe(include='number').to_string() if len(df.select_dtypes(include='number').columns) > 0 else "Aucune colonne numérique"
        
        quality_summary = f"""
        Analyse de qualité des données:
        - Nombre total de lignes: {total_rows}
        - Lignes dupliquées: {duplicate_rows}
        - Valeurs manquantes par colonne: {missing_data.to_dict()}
        - Types de données: {data_types}
        
        Statistiques numériques:
        {numeric_stats}
        """
        
        prompt = f"""
        {quality_summary}
        
        Analyse cette qualité de données et donne des recommandations concrètes pour améliorer la qualité.
        Identifie les problèmes potentiels et suggère des actions correctives.
        Sois concis mais précis.
        """
        
        return call_azure_gpt(prompt, system_message="Tu es un expert en qualité des données. Donne des conseils pratiques et précis.")
    
    except Exception as e:
        return f"Erreur lors de l'analyse de qualité: {str(e)}"

def generate_dynamic_chart(df: pd.DataFrame, chart_request: str = None) -> Optional[Dict]:
    """Génère un graphique dynamique basé sur les données et une demande spécifique"""
    try:
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()

        # Si une demande spécifique est faite, essayer de l'interpréter
        if chart_request and any(word in chart_request.lower() for word in ['bar', 'barre', 'colonne']):
            if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
                grouped = df.groupby(categorical_cols[0])[numeric_cols[0]].sum().head(10)
                return {
                    "type": "bar",
                    "title": f"{numeric_cols[0]} par {categorical_cols[0]}",
                    "x": grouped.index.tolist(),
                    "y": grouped.values.tolist(),
                    "xlabel": categorical_cols[0],
                    "ylabel": numeric_cols[0]
                }

        elif chart_request and any(word in chart_request.lower() for word in ['pie', 'camembert', 'secteur']):
            if len(categorical_cols) >= 1:
                value_counts = df[categorical_cols[0]].value_counts().head(8)
                return {
                    "type": "pie",
                    "title": f"Répartition par {categorical_cols[0]}",
                    "labels": value_counts.index.tolist(),
                    "values": value_counts.values.tolist()
                }

        # Graphique par défaut : barres si possible
        if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
            grouped = df.groupby(categorical_cols[0])[numeric_cols[0]].sum().head(10)
            return {
                "type": "bar",
                "title": f"{numeric_cols[0]} par {categorical_cols[0]}",
                "x": grouped.index.tolist(),
                "y": grouped.values.tolist(),
                "xlabel": categorical_cols[0],
                "ylabel": numeric_cols[0]
            }
        
        return None
    except Exception as e:
        print(f"Erreur génération graphique dynamique: {e}")
        return None

# Modèles Pydantic
class QuestionRequest(BaseModel):
    question: str

class ExplainKPIRequest(BaseModel):
    kpi_name: str
    context: Optional[str] = ""

class VisualRequest(BaseModel):
    question: str
    data_context: Optional[str] = ""

class ModelSettingsRequest(BaseModel):
    model_name: str
    deployment_name: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None

class AnalysisResponse(BaseModel):
    analysis: str
    chart_data: Optional[Dict] = None
    chart_json: Optional[str] = None

class DataInfoResponse(BaseModel):
    table_name: str
    columns: List[str]
    sample_data: List[Dict]
    total_rows: int
    data_quality: Optional[str] = None

class VisualResponse(BaseModel):
    chart_json: Dict
    success: bool
    message: str

class InsightsResponse(BaseModel):
    suggestions: List[str]
    success: bool
    message: str

# BrAInBI schema detection - Modèles pour le schéma de base de données
class ColumnInfo(BaseModel):
    COLUMN_NAME: str
    DATA_TYPE: str
    IS_NULLABLE: str
    COLUMN_DEFAULT: Optional[str]
    CHARACTER_MAXIMUM_LENGTH: Optional[int]
    NUMERIC_PRECISION: Optional[int]
    NUMERIC_SCALE: Optional[int]

class TableInfo(BaseModel):
    columns: List[ColumnInfo]
    sample_data: List[Dict[str, Any]]
    row_count: int
    primary_keys: List[str]
    foreign_keys: List[Dict[str, str]]

class Relationship(BaseModel):
    from_table: str
    from_column: str
    to_table: str
    to_column: str
    confidence: float
    common_values_count: int
    relationship_type: str

class SchemaMetadata(BaseModel):
    database: str
    tables: Dict[str, TableInfo]
    relationships: List[Relationship]

# Modèles pour les filtres
class AppliedFilter(BaseModel):
    column_name: str
    table_name: str
    filter_type: str  # "in", "range", "equals"
    values: List[Any]  # Pour "in" et "equals"
    min_value: Optional[Any] = None  # Pour "range"
    max_value: Optional[Any] = None  # Pour "range"

# Modèles pour le Visual Builder
class VisualBuilderRequest(BaseModel):
    table: str
    x_axis: str
    y_axis: Optional[str] = None
    chart_type: str  # bar, line, pie, scatter

class VisualBuilderResponse(BaseModel):
    type: str
    title: str
    x: List[Any]
    y: List[Any]
    xlabel: str
    ylabel: str
    success: bool
    message: str

# Modèles pour le Drag & Drop Visual Builder
class DragVisualRequest(BaseModel):
    table: str  # Table principale (pour compatibilité)
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None
    legend: Optional[str] = None
    values: Optional[str] = None  # Colonne pour les valeurs à agréger
    agg_function: str = "SUM"  # SUM, AVG, COUNT, MIN, MAX
    chart_type: str = "bar"  # bar, line, pie, scatter, stacked_bar
    # Nouvelles propriétés pour multi-tables
    x_axis_table: Optional[str] = None
    y_axis_table: Optional[str] = None
    legend_table: Optional[str] = None
    values_table: Optional[str] = None
    # Filtres
    filters: List[AppliedFilter] = []

# Modèles pour la sauvegarde des visualisations
class SavedVisualization(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    chart_type: str
    table: str
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None
    legend: Optional[str] = None
    values: Optional[str] = None
    agg_function: str = "SUM"
    x_axis_table: Optional[str] = None
    y_axis_table: Optional[str] = None
    legend_table: Optional[str] = None
    values_table: Optional[str] = None
    filters: List[AppliedFilter] = []
    created_at: datetime
    updated_at: datetime
    user_id: str = "default_user"  # Pour l'instant, utilisateur factice
    tags: List[str] = []  # Tags optionnels pour catégoriser

class SaveVisualizationRequest(BaseModel):
    name: str
    description: Optional[str] = None
    chart_type: str
    table: str
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None
    legend: Optional[str] = None
    values: Optional[str] = None
    agg_function: str = "SUM"
    x_axis_table: Optional[str] = None
    y_axis_table: Optional[str] = None
    legend_table: Optional[str] = None
    values_table: Optional[str] = None
    filters: List[AppliedFilter] = []
    tags: List[str] = []

class SavedVisualizationResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    chart_type: str
    created_at: datetime
    updated_at: datetime
    user_id: str
    tags: List[str] = []
    preview_data: Optional[Dict[str, Any]] = None  # Données de prévisualisation

# Modèles pour la configuration SQL dynamique
class SQLConfig(BaseModel):
    server: str  # ex: localhost\\SQLSERVER
    database: str  # nom de la base de données
    driver: str = "ODBC Driver 17 for SQL Server"  # driver ODBC par défaut
    username: Optional[str] = None  # pour l'authentification SQL Server
    password: Optional[str] = None  # pour l'authentification SQL Server
    use_windows_auth: bool = True  # True pour Windows Auth, False pour SQL Auth
    port: Optional[int] = None  # port optionnel (1433 par défaut)
    timeout: int = 30  # timeout de connexion en secondes

class SQLConfigRequest(BaseModel):
    server: str
    database: str
    driver: Optional[str] = "ODBC Driver 17 for SQL Server"
    username: Optional[str] = None
    password: Optional[str] = None
    use_windows_auth: Optional[bool] = True
    port: Optional[int] = None
    timeout: Optional[int] = 30

class SQLTestResponse(BaseModel):
    success: bool
    message: str
    connection_time: Optional[float] = None  # temps de connexion en secondes
    server_info: Optional[Dict[str, Any]] = None  # infos sur le serveur
    error_code: Optional[str] = None  # code d'erreur spécifique

class SQLConfigResponse(BaseModel):
    success: bool
    message: str
    config: Optional[SQLConfig] = None

# Fonctions pour la gestion de la configuration SQL dynamique
def get_connection_from_config(config: SQLConfig):
    """
    Crée une connexion SQL Server à partir d'une configuration
    """
    conn_str = build_connection_string(config)
    return pyodbc.connect(conn_str)

def is_sql_configured() -> bool:
    """
    Vérifie si une configuration SQL est définie
    """
    return current_sql_config is not None

def get_current_database_name() -> str:
    """
    Retourne le nom de la base de données actuelle
    """
    if current_sql_config is not None:
        return current_sql_config.database
    else:
        return SQL_DATABASE

def get_current_server_name() -> str:
    """
    Retourne le nom du serveur actuel
    """
    if current_sql_config is not None:
        return current_sql_config.server
    else:
        return SQL_SERVER

def check_sql_configuration():
    """
    Vérifie qu'une configuration SQL est définie et lève une HTTPException si ce n'est pas le cas
    """
    if not is_sql_configured():
        raise HTTPException(
            status_code=400,
            detail="Configuration SQL non définie. Utilisez l'endpoint /config/sql pour configurer la connexion à la base de données."
        )

class DragVisualResponse(BaseModel):
    type: str
    title: str
    x: List[Any]
    y: List[Any]
    legend: Optional[List[Any]] = None
    xlabel: str
    ylabel: str
    success: bool
    message: str

# Variables globales pour la sauvegarde des visualisations (après définition des modèles)
saved_visualizations: Dict[str, SavedVisualization] = {}

# Variable globale pour la configuration SQL dynamique
current_sql_config: Optional[SQLConfig] = None

# Modèles pour les filtres
class FilterValue(BaseModel):
    value: Any
    label: str
    count: Optional[int] = None

class FilterColumn(BaseModel):
    column_name: str
    table_name: str
    data_type: str
    distinct_values: List[FilterValue]
    is_date: bool = False
    min_value: Optional[Any] = None
    max_value: Optional[Any] = None

class FilterRequest(BaseModel):
    column_name: str
    table_name: Optional[str] = None

# Modèles pour la narration intelligente
class NarrateRequest(BaseModel):
    focus: Optional[str] = None  # "Product", "Region", "Sales", etc.
    time_period: Optional[str] = "12_months"  # "12_months", "6_months", "current_year"
    include_recommendations: bool = True

class NarrativeSection(BaseModel):
    title: str
    content: str
    insights: List[str] = []
    metrics: Dict[str, Any] = {}

class NarrateResponse(BaseModel):
    success: bool
    narrative: str
    sections: List[NarrativeSection]
    data_summary: Dict[str, Any]
    generated_at: str
    focus_area: Optional[str] = None

# Endpoints
@app.get("/")
async def root():
    return {
        "message": "BrAInBI - Business Intelligence augmentée par l'IA",
        "status": "active",
        "version": "2.0.0",
        "description": "Explore your Data. Amplified by AI.",
        "data_source": "SQL Server Live"
    }

@app.get("/health")
async def health_check():
    """
    Endpoint de santé pour vérifier que l'API est accessible
    """
    # Test de la connexion SQL Server
    if not is_sql_configured():
        sql_status = "NOT_CONFIGURED"
    else:
        try:
            # Test rapide de connexion SQL
            conn = get_sql_connection()
            conn.close()
            sql_status = f"OK ({get_current_server_name()}/{get_current_database_name()})"
        except Exception:
            sql_status = "ERROR"

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "OK",
            "sql": sql_status,
            "azure_openai": "OK" if config_valid else "ERROR"
        }
    }

@app.get("/data/info")
async def get_data_info():
    """Retourne les informations sur toutes les données SQL Server en mode live"""
    check_sql_configuration()

    global dataframes, schema_metadata

    try:
        # BrAInBI - multi-table live support - Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Préparer les informations multi-tables
        tables_info = {}
        for table_name, df in dataframes.items():
            tables_info[table_name] = {
                "columns": df.columns.tolist(),
                "total_rows": len(df),
                "sample_data": df.head(3).to_dict('records'),
                "data_types": df.dtypes.astype(str).to_dict()
            }

        return {
            "database": SQL_DATABASE,
            "tables_count": len(dataframes),
            "total_rows": sum(len(df) for df in dataframes.values()),
            "tables": tables_info,
            "schema_metadata": schema_metadata,
            "relations_summary": table_relations.get("summary", {})
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des données: {str(e)}")

@app.post("/ask")
async def ask_question(request: QuestionRequest):
    """Pose une question personnalisée sur toutes les données SQL Server en mode live"""
    global conversation_history, dataframes, table_relations

    try:
        # BrAInBI - multi-table live support - Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Construire le contexte multi-tables pour l'IA
        tables_context = f"""
        BASE DE DONNÉES: {SQL_DATABASE}
        TABLES DISPONIBLES ({len(dataframes)} tables):
        """

        for table_name, df in dataframes.items():
            tables_context += f"""

        TABLE: {table_name}
        - Lignes: {len(df)}
        - Colonnes: {', '.join(df.columns.tolist())}
        - Types: {', '.join([f"{col}({dtype})" for col, dtype in df.dtypes.astype(str).items()])}

        Aperçu des données (5 premières lignes):
        {df.head(5).to_csv(index=False)}

        Statistiques descriptives:
        {df.describe(include='all').to_string()}
        """

        # Ajouter les relations détectées
        relations_context = "\nRELATIONS ENTRE TABLES:\n"
        if table_relations and "relations" in table_relations:
            for rel in table_relations["relations"][:10]:  # Top 10 relations
                relations_context += f"- {rel['from_table']}.{rel['from_column']} ↔ {rel['to_table']}.{rel['to_column']} "
                relations_context += f"(confiance: {rel['confidence']:.2f}, type: {rel['relation_type']})\n"

        prompt = f"""
        CONTEXTE MULTI-TABLES (SQL Server - données live):
        {tables_context}
        {relations_context}

        Question de l'utilisateur: {request.question}

        INSTRUCTIONS:
        - Analyse toutes les tables disponibles pour répondre à la question
        - Utilise les relations détectées pour faire des jointures logiques
        - Si la question concerne plusieurs tables, propose des analyses croisées
        - Sois précis avec les chiffres et cite les tables utilisées
        - Suggère des visualisations appropriées si pertinent
        - Si des données manquent dans une table, vérifie les autres tables

        Réponds de manière complète en exploitant toutes les données disponibles.
        """

        # Appel avec historique de conversation
        ai_response = call_azure_gpt(prompt, conversation_history)

        # Ajouter à l'historique
        conversation_history.append({
            "user": request.question,
            "ai": ai_response
        })

        # Extraction automatique de JSON de la réponse IA
        extracted_json = extract_json_from_text(ai_response)
        chart_json = None
        if extracted_json:
            chart_json = json.dumps(extracted_json)

        # Génération d'un graphique si demandé et pas déjà extrait
        chart_data = None
        if not chart_json and any(word in request.question.lower() for word in ['graphique', 'chart', 'visualise', 'plot', 'courbe', 'diagramme']):
            # BrAInBI - multi-table live support - Utiliser la première table disponible pour les graphiques
            first_table = next(iter(dataframes.values())) if dataframes else None
            if first_table is not None:
                chart_data = generate_dynamic_chart(first_table, request.question)
                if chart_data:
                    chart_json = json.dumps(chart_data)

        return AnalysisResponse(
            analysis=ai_response,
            chart_data=chart_data,
            chart_json=chart_json
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'analyse: {str(e)}")

@app.post("/generate-visual")
async def generate_visual(request: VisualRequest):
    """Génère uniquement une visualisation JSON basée sur la question utilisateur"""
    global dataframes

    try:
        # BrAInBI - multi-table live support - Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Contexte multi-tables pour la génération de visualisations
        tables_summary = f"""
        Base de données: {SQL_DATABASE}
        Tables disponibles: {', '.join(dataframes.keys())}
        """

        for table_name, df in dataframes.items():
            tables_summary += f"""

        Table {table_name}:
        - Colonnes: {', '.join(df.columns.tolist())}
        - Lignes: {len(df)}
        - Aperçu: {df.head(3).to_csv(index=False)}
        """

        # Prompt spécialisé pour la génération de graphiques
        prompt = f"""
        {tables_summary}

        Question utilisateur: {request.question}

        Analyse toutes les tables disponibles et génère UNIQUEMENT un JSON de graphique au format suivant (sans texte d'explication):
        {{
          "type": "bar|pie|line|scatter|histogram",
          "x": [données pour axe X],
          "y": [données pour axe Y],
          "title": "Titre du graphique",
          "xlabel": "Label axe X (optionnel)",
          "ylabel": "Label axe Y (optionnel)",
          "table_used": "nom de la table utilisée"
        }}

        Pour un graphique en secteurs (pie), utilise:
        {{
          "type": "pie",
          "labels": [noms des catégories],
          "values": [valeurs correspondantes],
          "title": "Titre du graphique",
          "table_used": "nom de la table utilisée"
        }}

        Choisis la table la plus appropriée pour répondre à la question.
        Réponds UNIQUEMENT avec le JSON, rien d'autre.
        """

        ai_response = call_azure_gpt(prompt, system_message="Tu es un expert en visualisation de données. Tu génères uniquement du JSON valide pour les graphiques.")

        # Extraire le JSON de la réponse
        try:
            # Nettoyer la réponse pour extraire le JSON
            clean_response = ai_response.strip()
            if clean_response.startswith('```json'):
                clean_response = clean_response[7:]
            if clean_response.endswith('```'):
                clean_response = clean_response[:-3]
            clean_response = clean_response.strip()

            chart_json = json.loads(clean_response)

            return VisualResponse(
                chart_json=chart_json,
                success=True,
                message="Graphique généré avec succès"
            )

        except json.JSONDecodeError as e:
            # Fallback: générer un graphique avec la méthode existante
            first_table = next(iter(dataframes.values())) if dataframes else None
            if first_table is not None:
                chart_data = generate_dynamic_chart(first_table, request.question)
                if chart_data:
                    return VisualResponse(
                        chart_json=chart_data,
                        success=True,
                        message="Graphique généré avec méthode de fallback"
                    )

            return VisualResponse(
                chart_json={},
                success=False,
                message=f"Impossible de générer le graphique: {str(e)}"
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la génération: {str(e)}")

@app.post("/suggest-insights")
async def suggest_insights():
    """Suggère des questions intéressantes basées sur toutes les données SQL Server"""
    global dataframes, table_relations

    try:
        # BrAInBI - multi-table live support - Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Construire un résumé complet de toutes les tables
        tables_summary = f"""
        BASE DE DONNÉES: {SQL_DATABASE}
        NOMBRE DE TABLES: {len(dataframes)}

        DÉTAIL DES TABLES:
        """

        for table_name, df in dataframes.items():
            tables_summary += f"""

        TABLE: {table_name}
        - Lignes: {len(df)}
        - Colonnes: {', '.join(df.columns.tolist())}
        - Types principaux: {', '.join(df.dtypes.value_counts().head(3).index.tolist())}
        - Aperçu: {df.head(2).to_csv(index=False)}
        """

        # Ajouter les relations détectées
        relations_summary = "\nRELATIONS DÉTECTÉES:\n"
        if table_relations and "relations" in table_relations:
            for rel in table_relations["relations"][:5]:  # Top 5 relations
                relations_summary += f"- {rel['from_table']} ↔ {rel['to_table']} "
                relations_summary += f"(via {rel['from_column']}/{rel['to_column']}, confiance: {rel['confidence']:.2f})\n"

        data_summary = tables_summary + relations_summary

        prompt = f"""
        {data_summary}

        Basé sur ces données multi-tables et les relations détectées, génère 5 à 7 suggestions de questions intéressantes que l'utilisateur pourrait poser.

        Les questions doivent être:
        - Pertinentes pour l'analyse business multi-dimensionnelle
        - Exploiter les différentes tables disponibles
        - Inclure des analyses croisées entre tables liées
        - Variées (tendances, comparaisons, anomalies, corrélations, agrégations)
        - Spécifiques aux colonnes et relations disponibles

        Réponds avec une liste de questions séparées par des retours à la ligne, sans numérotation.
        Exemples de types de questions:
        - Questions sur une table spécifique
        - Questions croisées entre tables liées
        - Questions de tendances temporelles
        - Questions de performance et comparaisons
        - Questions d'analyse de corrélations
        """

        ai_response = call_azure_gpt(prompt, system_message="Tu es un expert en analyse de données business. Génère des questions pertinentes et actionables.")

        # Parser les suggestions
        suggestions = [line.strip() for line in ai_response.split('\n') if line.strip() and not line.strip().startswith('-')]
        suggestions = [s for s in suggestions if len(s) > 10]  # Filtrer les suggestions trop courtes

        return InsightsResponse(
            suggestions=suggestions[:5],  # Limiter à 5 suggestions max
            success=True,
            message=f"Généré {len(suggestions)} suggestions"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la génération des suggestions: {str(e)}")

@app.post("/explain-kpi")
async def explain_kpi(request: ExplainKPIRequest):
    """Mode pédagogique : explique un KPI ou une mesure"""
    global conversation_history

    try:
        # Lire les données depuis SQL Server en mode live
        current_data = read_data_from_sql()

        # Contexte des données
        data_info = f"""
        Base de données: {SQL_DATABASE}
        Table: {SQL_TABLE}
        Colonnes disponibles: {', '.join(current_data.columns.tolist())}
        Nombre de lignes: {len(current_data)}
        """

        # Prompt pédagogique
        prompt = f"""
        {data_info}

        L'utilisateur souhaite comprendre le KPI ou la mesure: "{request.kpi_name}"

        Contexte supplémentaire: {request.context}

        Explique de manière pédagogique:
        1. Ce qu'est ce KPI/mesure et comment il est calculé
        2. Son importance dans l'analyse business
        3. Comment l'interpréter (valeurs bonnes/mauvaises)
        4. Des exemples concrets basés sur les données disponibles
        5. Des visualisations recommandées pour ce KPI

        Sois pédagogique mais précis.
        """

        ai_response = call_azure_gpt(prompt, system_message="Tu es un expert pédagogique en business intelligence. Explique clairement les concepts.")

        return {"analysis": ai_response}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'explication: {str(e)}")

@app.post("/settings/model")
async def update_model_settings(request: ModelSettingsRequest):
    """Met à jour les paramètres du modèle IA"""
    global ai_settings

    try:
        ai_settings.model_name = request.model_name
        if request.deployment_name:
            ai_settings.deployment_name = request.deployment_name
        if request.temperature is not None:
            ai_settings.temperature = request.temperature
        if request.max_tokens is not None:
            ai_settings.max_tokens = request.max_tokens

        return {
            "success": True,
            "message": "Paramètres mis à jour",
            "current_settings": {
                "model_name": ai_settings.model_name,
                "deployment_name": ai_settings.deployment_name,
                "temperature": ai_settings.temperature,
                "max_tokens": ai_settings.max_tokens
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour: {str(e)}")

@app.get("/settings/model")
async def get_model_settings():
    """Récupère les paramètres actuels du modèle"""
    return {
        "model_name": ai_settings.model_name,
        "deployment_name": ai_settings.deployment_name,
        "temperature": ai_settings.temperature,
        "max_tokens": ai_settings.max_tokens
    }

@app.get("/conversation/history")
async def get_conversation_history():
    """Récupère l'historique de conversation"""
    return conversation_history

@app.delete("/conversation/history")
async def clear_conversation_history():
    """Efface l'historique de conversation"""
    global conversation_history
    conversation_history = []
    return {"message": "Historique effacé"}

# BrAInBI - multi-table live support - Endpoint pour exposer le schéma et les relations
@app.get("/schema/relations")
async def get_schema_relations():
    """Retourne le schéma complet de la base de données avec les relations détectées"""
    global dataframes, table_relations, schema_metadata

    try:
        # Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Construire la réponse complète
        response = {
            "database": SQL_DATABASE,
            "tables_count": len(dataframes),
            "total_rows": sum(len(df) for df in dataframes.values()),
            "tables": {},
            "relations": table_relations.get("relations", []),
            "relations_summary": table_relations.get("summary", {}),
            "schema_metadata": schema_metadata
        }

        # Détails de chaque table
        for table_name, df in dataframes.items():
            response["tables"][table_name] = {
                "columns": [{"name": col, "type": str(dtype)} for col, dtype in df.dtypes.items()],
                "row_count": len(df),
                "sample_data": df.head(3).to_dict('records'),
                "column_count": len(df.columns),
                "numeric_columns": df.select_dtypes(include=['number']).columns.tolist(),
                "categorical_columns": df.select_dtypes(include=['object', 'category']).columns.tolist()
            }

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération du schéma: {str(e)}")

# BrAInBI - multi-table live support - Endpoint pour l'aperçu complet des tables
@app.get("/schema/full")
async def get_full_schema_overview():
    """Retourne un aperçu complet de toutes les tables avec exemples et relations pour l'interface utilisateur"""
    global dataframes, table_relations, schema_metadata

    try:
        # Charger toutes les tables si nécessaire
        if not dataframes:
            load_all_tables()
            detect_relations(dataframes)

        # Construire la réponse détaillée pour l'interface
        overview = {
            "database_info": {
                "name": SQL_DATABASE,
                "server": SQL_SERVER,
                "tables_count": len(dataframes),
                "total_rows": sum(len(df) for df in dataframes.values()),
                "last_updated": datetime.now().isoformat()
            },
            "tables": [],
            "relations": [],
            "summary": {
                "numeric_tables": 0,
                "text_tables": 0,
                "mixed_tables": 0,
                "total_columns": 0
            }
        }

        # Détails de chaque table pour l'affichage
        for table_name, df in dataframes.items():
            # Analyser les types de colonnes
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            text_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
            date_cols = df.select_dtypes(include=['datetime']).columns.tolist()

            # Déterminer le type principal de la table
            table_type = "mixed"
            if len(numeric_cols) > len(text_cols):
                table_type = "numeric"
                overview["summary"]["numeric_tables"] += 1
            elif len(text_cols) > len(numeric_cols):
                table_type = "text"
                overview["summary"]["text_tables"] += 1
            else:
                overview["summary"]["mixed_tables"] += 1

            overview["summary"]["total_columns"] += len(df.columns)

            # Informations détaillées de la table
            table_info = {
                "name": table_name,
                "row_count": len(df),
                "column_count": len(df.columns),
                "table_type": table_type,
                "columns": [
                    {
                        "name": col,
                        "type": str(dtype),
                        "category": "numeric" if col in numeric_cols else "text" if col in text_cols else "date" if col in date_cols else "other",
                        "null_count": int(df[col].isnull().sum()) if len(df) > 0 else 0,
                        "unique_count": int(df[col].nunique()) if len(df) > 0 else 0
                    }
                    for col, dtype in df.dtypes.items()
                ],
                "sample_data": df.head(5).to_dict('records') if len(df) > 0 else [],
                "statistics": {
                    "numeric_columns": len(numeric_cols),
                    "text_columns": len(text_cols),
                    "date_columns": len(date_cols),
                    "null_percentage": round((df.isnull().sum().sum() / max(1, len(df) * len(df.columns))) * 100, 2) if len(df) > 0 and len(df.columns) > 0 else 0.0
                }
            }

            overview["tables"].append(table_info)

        # Relations formatées pour l'affichage
        if table_relations and "relations" in table_relations:
            for rel in table_relations["relations"]:
                overview["relations"].append({
                    "id": f"{rel['from_table']}_{rel['from_column']}_{rel['to_table']}_{rel['to_column']}",
                    "from_table": rel['from_table'],
                    "from_column": rel['from_column'],
                    "to_table": rel['to_table'],
                    "to_column": rel['to_column'],
                    "relation_type": rel['relation_type'],
                    "confidence": round(rel['confidence'], 3),
                    "confidence_level": "high" if rel['confidence'] > 0.7 else "medium" if rel['confidence'] > 0.4 else "low",
                    "description": f"{rel['from_table']}.{rel['from_column']} → {rel['to_table']}.{rel['to_column']}"
                })

        # Trier les tables par nombre de lignes (décroissant)
        overview["tables"].sort(key=lambda x: x["row_count"], reverse=True)

        # Nettoyer les valeurs NaN avant la sérialisation JSON
        clean_overview = clean_nan_values(overview)

        return clean_overview

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération de l'aperçu complet: {str(e)}")

@app.get("/schema/tables")
async def get_all_tables():
    """Retourne la liste de toutes les tables disponibles"""
    check_sql_configuration()

    try:
        conn = get_sql_connection()

        tables_query = """
        SELECT
            TABLE_NAME,
            TABLE_TYPE,
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as COLUMN_COUNT
        FROM INFORMATION_SCHEMA.TABLES t
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """

        tables_df = pd.read_sql(tables_query, conn)
        conn.close()

        return {
            "database": SQL_DATABASE,
            "tables": tables_df.to_dict('records'),
            "total_tables": len(tables_df)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des tables: {str(e)}")

@app.get("/tables/{table_name}/test")
async def test_table_info(table_name: str):
    """Test endpoint pour déboguer"""
    try:
        return {"message": f"Test pour la table {table_name}", "status": "ok"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur de test: {str(e)}")

@app.get("/tables/{table_name}/info")
async def get_table_info(table_name: str):
    """
    Retourne les informations complètes d'une table spécifique pour la navigation
    Inclut : colonnes avec types/nullable/clés, échantillon de données (5 lignes), relations
    """
    check_sql_configuration()

    try:
        conn = get_sql_connection()

        # Vérifier que la table existe
        check_query = f"""
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{table_name}' AND TABLE_TYPE = 'BASE TABLE'
        """

        exists_df = pd.read_sql(check_query, conn)
        if exists_df.iloc[0]['table_exists'] == 0:
            conn.close()
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' non trouvée")

        # 1. Récupérer les colonnes avec leurs métadonnées
        columns_query = f"""
        SELECT
            c.COLUMN_NAME,
            c.DATA_TYPE,
            c.IS_NULLABLE,
            c.COLUMN_DEFAULT,
            c.CHARACTER_MAXIMUM_LENGTH,
            c.NUMERIC_PRECISION,
            c.NUMERIC_SCALE,
            c.ORDINAL_POSITION
        FROM INFORMATION_SCHEMA.COLUMNS c
        WHERE c.TABLE_NAME = '{table_name}'
        ORDER BY c.ORDINAL_POSITION
        """
        columns_df = pd.read_sql(columns_query, conn)

        # 2. Récupérer les clés primaires
        primary_keys_query = f"""
        SELECT kcu.COLUMN_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            AND tc.TABLE_NAME = kcu.TABLE_NAME
        WHERE tc.TABLE_NAME = '{table_name}'
            AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ORDER BY kcu.ORDINAL_POSITION
        """
        primary_keys_df = pd.read_sql(primary_keys_query, conn)
        primary_keys = primary_keys_df['COLUMN_NAME'].tolist() if not primary_keys_df.empty else []

        # 3. Récupérer les clés étrangères avec leurs références
        foreign_keys_query = f"""
        SELECT
            kcu.COLUMN_NAME as source_column,
            ccu.TABLE_NAME as target_table,
            ccu.COLUMN_NAME as target_column,
            tc.CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            AND tc.TABLE_NAME = kcu.TABLE_NAME
        JOIN INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE ccu
            ON tc.CONSTRAINT_NAME = ccu.CONSTRAINT_NAME
        WHERE tc.TABLE_NAME = '{table_name}'
            AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
        ORDER BY kcu.ORDINAL_POSITION
        """
        foreign_keys_df = pd.read_sql(foreign_keys_query, conn)

        # 4. Récupérer les relations inverses (tables qui référencent cette table)
        reverse_relations_query = f"""
        SELECT
            kcu.TABLE_NAME as source_table,
            kcu.COLUMN_NAME as source_column,
            ccu.COLUMN_NAME as target_column,
            tc.CONSTRAINT_NAME
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
        JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            AND tc.TABLE_NAME = kcu.TABLE_NAME
        JOIN INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE ccu
            ON tc.CONSTRAINT_NAME = ccu.CONSTRAINT_NAME
        WHERE ccu.TABLE_NAME = '{table_name}'
            AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
        ORDER BY kcu.TABLE_NAME, kcu.ORDINAL_POSITION
        """
        reverse_relations_df = pd.read_sql(reverse_relations_query, conn)

        # 5. Échantillon de données (5 premières lignes)
        sample_query = f"SELECT TOP 5 * FROM [{table_name}]"
        sample_df = pd.read_sql(sample_query, conn)

        # 6. Nombre total de lignes
        count_query = f"SELECT COUNT(*) as total_rows FROM [{table_name}]"
        count_df = pd.read_sql(count_query, conn)

        conn.close()

        # Construire la liste des colonnes avec leurs métadonnées
        columns = []
        for _, col in columns_df.iterrows():
            column_info = {
                "name": col['COLUMN_NAME'],
                "type": col['DATA_TYPE'],
                "is_nullable": col['IS_NULLABLE'] == 'YES',
                "is_primary": col['COLUMN_NAME'] in primary_keys,
                "is_foreign": col['COLUMN_NAME'] in foreign_keys_df['source_column'].tolist(),
                "max_length": col['CHARACTER_MAXIMUM_LENGTH'],
                "precision": col['NUMERIC_PRECISION'],
                "scale": col['NUMERIC_SCALE'],
                "default_value": col['COLUMN_DEFAULT']
            }
            # Nettoyer les valeurs NaN
            column_info = clean_nan_values(column_info)
            columns.append(column_info)

        # Construire la liste des relations
        relations = []

        # Relations sortantes (cette table référence d'autres tables)
        for _, fk in foreign_keys_df.iterrows():
            relations.append({
                "type": "outgoing",
                "source_column": fk['source_column'],
                "target_table": fk['target_table'],
                "target_column": fk['target_column'],
                "description": f"{table_name}.{fk['source_column']} → {fk['target_table']}.{fk['target_column']}"
            })

        # Relations entrantes (d'autres tables référencent cette table)
        for _, rel in reverse_relations_df.iterrows():
            relations.append({
                "type": "incoming",
                "source_table": rel['source_table'],
                "source_column": rel['source_column'],
                "target_column": rel['target_column'],
                "description": f"{rel['source_table']}.{rel['source_column']} → {table_name}.{rel['target_column']}"
            })

        # Préparer les données d'échantillon
        sample_data = clean_nan_values(sample_df.to_dict('records'))

        response_data = {
            "table_name": table_name,
            "columns": columns,
            "sample_data": sample_data,
            "relations": relations,
            "total_rows": int(count_df.iloc[0]['total_rows']),
            "primary_keys": primary_keys,
            "foreign_keys_count": len(foreign_keys_df),
            "incoming_relations_count": len(reverse_relations_df)
        }

        # Nettoyer toute la réponse pour éviter les valeurs NaN
        return clean_nan_values(response_data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des informations de la table '{table_name}': {str(e)}")

@app.get("/tables/{table_name}/columns")
async def get_table_columns(table_name: str):
    """
    Retourne les colonnes d'une table avec leurs types pour le visual builder
    Sépare les colonnes numériques des catégorielles
    """
    try:
        conn = get_sql_connection()

        # Vérifier que la table existe
        check_query = f"""
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{table_name}' AND TABLE_TYPE = 'BASE TABLE'
        """

        exists_df = pd.read_sql(check_query, conn)
        if exists_df.iloc[0]['table_exists'] == 0:
            conn.close()
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' non trouvée")

        # Récupérer les colonnes avec leurs types
        columns_query = f"""
        SELECT
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            CHARACTER_MAXIMUM_LENGTH,
            NUMERIC_PRECISION,
            NUMERIC_SCALE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """
        columns_df = pd.read_sql(columns_query, conn)
        conn.close()

        # Classifier les colonnes par type
        numeric_columns = []
        categorical_columns = []
        date_columns = []
        all_columns = []

        for _, col in columns_df.iterrows():
            column_info = {
                "name": col['COLUMN_NAME'],
                "type": col['DATA_TYPE'],
                "is_nullable": col['IS_NULLABLE'] == 'YES'
            }

            all_columns.append(column_info)

            # Classification des types
            data_type = col['DATA_TYPE'].lower()

            if data_type in ['int', 'bigint', 'smallint', 'tinyint', 'decimal', 'numeric', 'float', 'real', 'money', 'smallmoney']:
                numeric_columns.append(column_info)
            elif data_type in ['date', 'datetime', 'datetime2', 'smalldatetime', 'time', 'timestamp']:
                date_columns.append(column_info)
            else:
                categorical_columns.append(column_info)

        return {
            "table_name": table_name,
            "all_columns": all_columns,
            "numeric_columns": numeric_columns,
            "categorical_columns": categorical_columns,
            "date_columns": date_columns,
            "total_columns": len(all_columns)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des colonnes de la table '{table_name}': {str(e)}")

@app.post("/visual-builder")
async def create_visualization(request: VisualBuilderRequest):
    """
    Crée une visualisation basée sur les paramètres fournis
    Génère dynamiquement les requêtes SQL avec agrégation appropriée
    """
    try:
        conn = get_sql_connection()

        # Vérifier que la table existe
        check_query = f"""
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{request.table}' AND TABLE_TYPE = 'BASE TABLE'
        """

        exists_df = pd.read_sql(check_query, conn)
        if exists_df.iloc[0]['table_exists'] == 0:
            conn.close()
            raise HTTPException(status_code=404, detail=f"Table '{request.table}' non trouvée")

        # Vérifier que les colonnes existent et récupérer leurs types
        columns_query = f"""
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{request.table}'
        """
        columns_df = pd.read_sql(columns_query, conn)
        available_columns = columns_df['COLUMN_NAME'].tolist()
        column_types = dict(zip(columns_df['COLUMN_NAME'], columns_df['DATA_TYPE']))

        if request.x_axis not in available_columns:
            conn.close()
            raise HTTPException(status_code=400, detail=f"Colonne X '{request.x_axis}' non trouvée dans la table '{request.table}'")

        if request.y_axis and request.y_axis not in available_columns:
            conn.close()
            raise HTTPException(status_code=400, detail=f"Colonne Y '{request.y_axis}' non trouvée dans la table '{request.table}'")

        # Vérifier que la colonne Y est numérique si requise
        if request.y_axis:
            y_type = column_types.get(request.y_axis, '').lower()
            numeric_types = ['int', 'bigint', 'smallint', 'tinyint', 'decimal', 'numeric', 'float', 'real', 'money', 'smallmoney']
            if y_type not in numeric_types:
                conn.close()
                raise HTTPException(status_code=400, detail=f"Colonne Y '{request.y_axis}' doit être numérique pour ce type de graphique. Type actuel: {y_type}")

        # Validation du type de graphique
        valid_chart_types = ['bar', 'line', 'pie', 'scatter']
        if request.chart_type.lower() not in valid_chart_types:
            conn.close()
            raise HTTPException(status_code=400, detail=f"Type de graphique '{request.chart_type}' non supporté. Types supportés: {', '.join(valid_chart_types)}")

        # Vérifier que Y est fourni pour les graphiques qui en ont besoin
        if request.chart_type.lower() in ['bar', 'line', 'scatter'] and not request.y_axis:
            conn.close()
            raise HTTPException(status_code=400, detail=f"Une colonne Y numérique est requise pour le graphique {request.chart_type}")

        # Générer la requête SQL selon le type de graphique
        chart_data = await generate_chart_data(conn, request)
        conn.close()

        return chart_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de la visualisation: {str(e)}")

async def generate_chart_data(conn, request: VisualBuilderRequest) -> VisualBuilderResponse:
    """
    Génère les données du graphique selon le type demandé
    """
    try:
        chart_type = request.chart_type.lower()
        table = request.table
        x_axis = request.x_axis
        y_axis = request.y_axis

        if chart_type == "pie":
            # Pour pie chart : COUNT par catégorie ou SUM si y_axis fourni
            if y_axis:
                query = f"""
                SELECT {x_axis}, SUM(CAST({y_axis} AS FLOAT)) as value
                FROM [{table}]
                WHERE {x_axis} IS NOT NULL AND {y_axis} IS NOT NULL
                GROUP BY {x_axis}
                ORDER BY value DESC
                """
                ylabel = f"Somme de {y_axis}"
            else:
                query = f"""
                SELECT {x_axis}, COUNT(*) as value
                FROM [{table}]
                WHERE {x_axis} IS NOT NULL
                GROUP BY {x_axis}
                ORDER BY value DESC
                """
                ylabel = "Nombre d'occurrences"

            df = pd.read_sql(query, conn)

            if df.empty:
                raise HTTPException(status_code=400, detail=f"Aucune donnée trouvée dans la table '{table}' pour les colonnes spécifiées")

            # Nettoyer les données pour éviter les problèmes de sérialisation
            df = df.dropna()
            if df.empty:
                raise HTTPException(status_code=400, detail=f"Aucune donnée valide trouvée après nettoyage des valeurs nulles")

            return VisualBuilderResponse(
                type="pie",
                title=f"Répartition par {x_axis}",
                x=df[x_axis].tolist(),  # labels pour pie chart
                y=df['value'].tolist(),  # values pour pie chart
                xlabel=x_axis,
                ylabel=ylabel,
                success=True,
                message=f"Graphique généré avec succès ({len(df)} éléments)"
            )

        elif chart_type in ["bar", "line", "scatter"]:
            if not y_axis:
                raise HTTPException(status_code=400, detail=f"Colonne Y requise pour le graphique {chart_type}")

            # Pour bar, line, scatter : GROUP BY x_axis, SUM(y_axis)
            query = f"""
            SELECT {x_axis}, SUM(CAST({y_axis} AS FLOAT)) as value
            FROM [{table}]
            WHERE {x_axis} IS NOT NULL AND {y_axis} IS NOT NULL
            GROUP BY {x_axis}
            ORDER BY {x_axis}
            """

            df = pd.read_sql(query, conn)

            if df.empty:
                raise HTTPException(status_code=400, detail=f"Aucune donnée trouvée dans la table '{table}' pour les colonnes spécifiées")

            # Nettoyer les données pour éviter les problèmes de sérialisation
            df = df.dropna()
            if df.empty:
                raise HTTPException(status_code=400, detail=f"Aucune donnée valide trouvée après nettoyage des valeurs nulles")

            return VisualBuilderResponse(
                type=chart_type,
                title=f"{y_axis} par {x_axis}",
                x=df[x_axis].tolist(),
                y=df['value'].tolist(),
                xlabel=x_axis,
                ylabel=f"Somme de {y_axis}",
                success=True,
                message=f"Graphique généré avec succès ({len(df)} points de données)"
            )

        else:
            raise HTTPException(status_code=400, detail=f"Type de graphique '{chart_type}' non supporté. Types supportés: bar, line, pie, scatter")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la génération des données: {str(e)}")

@app.get("/filters/columns")
async def get_filterable_columns():
    """
    Retourne toutes les colonnes filtrables de toutes les tables
    """
    try:
        conn = get_sql_connection()

        # Récupérer toutes les colonnes de toutes les tables
        query = """
        SELECT
            t.TABLE_NAME,
            c.COLUMN_NAME,
            c.DATA_TYPE,
            c.IS_NULLABLE
        FROM INFORMATION_SCHEMA.TABLES t
        JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
        WHERE t.TABLE_TYPE = 'BASE TABLE'
        ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
        """

        df = pd.read_sql(query, conn)
        conn.close()

        # Organiser par table
        tables = {}
        for _, row in df.iterrows():
            table_name = row['TABLE_NAME']
            if table_name not in tables:
                tables[table_name] = []

            # Déterminer si c'est une colonne de date
            is_date = row['DATA_TYPE'].lower() in ['date', 'datetime', 'datetime2', 'smalldatetime', 'time', 'timestamp']

            tables[table_name].append({
                "column_name": row['COLUMN_NAME'],
                "data_type": row['DATA_TYPE'],
                "is_nullable": row['IS_NULLABLE'] == 'YES',
                "is_date": is_date
            })

        return {
            "success": True,
            "tables": tables,
            "total_columns": len(df)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des colonnes filtrables: {str(e)}")

@app.post("/filters/values")
async def get_filter_values(request: FilterRequest):
    """
    Retourne les valeurs distinctes pour une colonne donnée
    """
    try:
        conn = get_sql_connection()

        # Déterminer la table si non spécifiée
        table_name = request.table_name
        if not table_name:
            table_name = find_table_for_column(conn, request.column_name)
            if not table_name:
                conn.close()
                raise HTTPException(status_code=404, detail=f"Colonne '{request.column_name}' non trouvée")

        # Vérifier que la colonne existe dans la table
        check_query = f"""
        SELECT DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}' AND COLUMN_NAME = '{request.column_name}'
        """

        check_df = pd.read_sql(check_query, conn)
        if check_df.empty:
            conn.close()
            raise HTTPException(status_code=404, detail=f"Colonne '{request.column_name}' non trouvée dans la table '{table_name}'")

        data_type = check_df.iloc[0]['DATA_TYPE']
        is_date = data_type.lower() in ['date', 'datetime', 'datetime2', 'smalldatetime', 'time', 'timestamp']

        if is_date:
            # Pour les dates, retourner min/max
            query = f"""
            SELECT
                MIN([{request.column_name}]) as min_value,
                MAX([{request.column_name}]) as max_value,
                COUNT(DISTINCT [{request.column_name}]) as distinct_count
            FROM [{table_name}]
            WHERE [{request.column_name}] IS NOT NULL
            """

            df = pd.read_sql(query, conn)
            conn.close()

            if df.empty or df.iloc[0]['distinct_count'] == 0:
                return FilterColumn(
                    column_name=request.column_name,
                    table_name=table_name,
                    data_type=data_type,
                    distinct_values=[],
                    is_date=True,
                    min_value=None,
                    max_value=None
                )

            return FilterColumn(
                column_name=request.column_name,
                table_name=table_name,
                data_type=data_type,
                distinct_values=[],
                is_date=True,
                min_value=df.iloc[0]['min_value'],
                max_value=df.iloc[0]['max_value']
            )

        else:
            # Pour les autres types, retourner les valeurs distinctes
            query = f"""
            SELECT
                [{request.column_name}] as value,
                COUNT(*) as count
            FROM [{table_name}]
            WHERE [{request.column_name}] IS NOT NULL
            GROUP BY [{request.column_name}]
            ORDER BY count DESC, [{request.column_name}]
            """

            df = pd.read_sql(query, conn)
            conn.close()

            # Limiter à 100 valeurs distinctes pour éviter les surcharges
            df = df.head(100)

            distinct_values = []
            for _, row in df.iterrows():
                distinct_values.append(FilterValue(
                    value=row['value'],
                    label=str(row['value']),
                    count=int(row['count'])
                ))

            return FilterColumn(
                column_name=request.column_name,
                table_name=table_name,
                data_type=data_type,
                distinct_values=distinct_values,
                is_date=False
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des valeurs de filtre: {str(e)}")

def clean_numpy_types(obj):
    """
    Convertit récursivement les types numpy en types Python natifs pour la sérialisation JSON
    """
    if isinstance(obj, dict):
        return {key: clean_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [clean_numpy_types(item) for item in obj]
    elif pd.isna(obj):
        return None
    elif hasattr(obj, 'item'):  # Types numpy (int64, float64, etc.)
        return obj.item()
    elif isinstance(obj, (pd.Timestamp, pd.Timedelta)):
        return str(obj)
    elif isinstance(obj, bytes):
        return obj.decode('utf-8', errors='ignore')
    else:
        return obj

@app.get("/narrate/test")
async def test_narration_connectivity():
    """
    Test de connectivité pour diagnostiquer les problèmes de narration
    """
    try:
        print("🧪 [TEST] Début du test de connectivité narration...")

        # Test 1: Connexion SQL
        try:
            conn = get_sql_connection()
            print("✅ [TEST] Connexion SQL OK")
        except Exception as e:
            print(f"❌ [TEST] Erreur connexion SQL: {e}")
            return {"success": False, "error": "Connexion SQL échouée", "details": str(e)}

        # Test 2: Requête simple
        try:
            test_query = "SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
            result = pd.read_sql(test_query, conn)
            table_count = result.iloc[0]['table_count']
            print(f"✅ [TEST] Requête SQL OK - {table_count} tables trouvées")
        except Exception as e:
            conn.close()
            print(f"❌ [TEST] Erreur requête SQL: {e}")
            return {"success": False, "error": "Requête SQL échouée", "details": str(e)}

        # Test 3: Azure OpenAI
        try:
            test_prompt = "Dis simplement 'Test OK' en français."
            ai_response = call_azure_gpt(test_prompt, system_message="Tu réponds brièvement.")
            print(f"✅ [TEST] Azure OpenAI OK - Réponse: {ai_response[:50]}...")
        except Exception as e:
            conn.close()
            print(f"❌ [TEST] Erreur Azure OpenAI: {e}")
            return {"success": False, "error": "Azure OpenAI échoué", "details": str(e)}

        conn.close()

        # Nettoyer la réponse pour éviter les problèmes de sérialisation
        response_data = {
            "success": True,
            "message": "Tous les tests sont passés avec succès",
            "tests": {
                "sql_connection": "OK",
                "sql_query": f"OK - {table_count} tables",
                "azure_openai": "OK",
                "ai_response_preview": ai_response[:100] if ai_response else "Vide"
            }
        }

        return clean_numpy_types(response_data)

    except Exception as e:
        print(f"❌ [TEST] Erreur inattendue: {e}")
        return {"success": False, "error": "Erreur inattendue", "details": str(e)}

def analyze_database_structure(conn):
    """
    Analyse la structure complète de la base de données pour la narration
    """
    try:
        print("🔍 [NARRATION] Début de l'analyse de la structure de la base de données...")

        # Récupérer toutes les tables et leurs colonnes
        tables_query = """
        SELECT
            t.TABLE_NAME,
            c.COLUMN_NAME,
            c.DATA_TYPE,
            c.IS_NULLABLE,
            CASE
                WHEN c.DATA_TYPE IN ('date', 'datetime', 'datetime2', 'smalldatetime') THEN 'date'
                WHEN c.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN 'numeric'
                ELSE 'text'
            END as column_category
        FROM INFORMATION_SCHEMA.TABLES t
        JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
        WHERE t.TABLE_TYPE = 'BASE TABLE'
        ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
        """

        print("🔍 [NARRATION] Exécution de la requête de structure...")
        structure_df = pd.read_sql(tables_query, conn)
        print(f"🔍 [NARRATION] {len(structure_df)} colonnes trouvées dans {len(structure_df['TABLE_NAME'].unique())} tables")

        if structure_df.empty:
            print("⚠️ [NARRATION] Aucune table trouvée dans la base de données")
            return {}

        # Organiser par table
        tables_info = {}
        for table_name in structure_df['TABLE_NAME'].unique():
            print(f"🔍 [NARRATION] Analyse de la table: {table_name}")
            table_columns = structure_df[structure_df['TABLE_NAME'] == table_name]

            # Compter les lignes dans la table
            try:
                count_query = f"SELECT COUNT(*) as row_count FROM [{table_name}]"
                count_result = pd.read_sql(count_query, conn)
                row_count = int(count_result.iloc[0]['row_count'])  # Conversion explicite en int Python
                print(f"🔍 [NARRATION] Table {table_name}: {row_count} lignes")
            except Exception as count_error:
                print(f"⚠️ [NARRATION] Erreur comptage pour {table_name}: {count_error}")
                row_count = 0

            date_columns = table_columns[table_columns['column_category'] == 'date']['COLUMN_NAME'].tolist()
            numeric_columns = table_columns[table_columns['column_category'] == 'numeric']['COLUMN_NAME'].tolist()
            text_columns = table_columns[table_columns['column_category'] == 'text']['COLUMN_NAME'].tolist()

            tables_info[table_name] = {
                'columns': table_columns.to_dict('records'),
                'row_count': row_count,
                'date_columns': date_columns,
                'numeric_columns': numeric_columns,
                'text_columns': text_columns
            }

            print(f"🔍 [NARRATION] {table_name}: {len(date_columns)} dates, {len(numeric_columns)} numériques, {len(text_columns)} texte")

        print(f"✅ [NARRATION] Analyse terminée: {len(tables_info)} tables analysées")
        return tables_info

    except Exception as e:
        print(f"❌ [NARRATION] Erreur lors de l'analyse de la structure: {e}")
        import traceback
        print(f"❌ [NARRATION] Stack trace: {traceback.format_exc()}")
        return {}

def get_sample_data_for_narration(conn, tables_info, focus=None):
    """
    Récupère des données d'échantillon pour la narration
    """
    try:
        print(f"🔍 [NARRATION] Récupération d'échantillons (focus: {focus})")
        sample_data = {}

        for table_name, info in tables_info.items():
            if focus and focus.lower() not in table_name.lower():
                print(f"🔍 [NARRATION] Table {table_name} ignorée (focus: {focus})")
                continue

            print(f"🔍 [NARRATION] Échantillonnage de {table_name}...")
            # Récupérer un échantillon de données
            sample_query = f"SELECT TOP 3 * FROM [{table_name}]"
            try:
                sample_df = pd.read_sql(sample_query, conn)
                # Utiliser la fonction de nettoyage pour les types numpy
                sample_data[table_name] = {
                    'sample': clean_numpy_types(sample_df.to_dict('records')),
                    'columns': [str(col) for col in sample_df.columns]
                }
                print(f"✅ [NARRATION] {table_name}: {len(sample_df)} échantillons récupérés")
            except Exception as e:
                print(f"⚠️ [NARRATION] Erreur échantillon pour {table_name}: {e}")
                sample_data[table_name] = {'sample': [], 'columns': []}

        print(f"✅ [NARRATION] Échantillonnage terminé: {len(sample_data)} tables")
        return sample_data

    except Exception as e:
        print(f"❌ [NARRATION] Erreur lors de la récupération des échantillons: {e}")
        import traceback
        print(f"❌ [NARRATION] Stack trace: {traceback.format_exc()}")
        return {}

def get_time_series_analysis(conn, tables_info):
    """
    Analyse les tendances temporelles si des colonnes de date sont disponibles
    """
    try:
        print("🔍 [NARRATION] Début de l'analyse temporelle...")
        time_analysis = {}

        for table_name, info in tables_info.items():
            if not info['date_columns']:
                print(f"🔍 [NARRATION] {table_name}: aucune colonne de date trouvée")
                continue

            date_column = info['date_columns'][0]  # Prendre la première colonne de date
            numeric_columns = info['numeric_columns'][:2]  # Limiter à 2 colonnes numériques

            print(f"🔍 [NARRATION] {table_name}: analyse temporelle sur {date_column} avec {len(numeric_columns)} colonnes numériques")

            if not numeric_columns:
                print(f"🔍 [NARRATION] {table_name}: aucune colonne numérique pour l'analyse temporelle")
                continue

            # Construire la requête d'analyse temporelle simplifiée
            try:
                # D'abord, vérifier s'il y a des données récentes
                check_query = f"""
                SELECT COUNT(*) as recent_count
                FROM [{table_name}]
                WHERE [{date_column}] IS NOT NULL
                  AND [{date_column}] >= DATEADD(month, -12, GETDATE())
                """

                check_df = pd.read_sql(check_query, conn)
                recent_count = int(check_df.iloc[0]['recent_count'])  # Conversion explicite

                if recent_count == 0:
                    print(f"🔍 [NARRATION] {table_name}: aucune donnée récente (12 derniers mois)")
                    # Essayer avec toutes les données
                    query = f"""
                    SELECT TOP 10
                        CAST([{date_column}] AS DATE) as date_value,
                        COUNT(*) as record_count
                    FROM [{table_name}]
                    WHERE [{date_column}] IS NOT NULL
                    GROUP BY CAST([{date_column}] AS DATE)
                    ORDER BY CAST([{date_column}] AS DATE) DESC
                    """
                else:
                    # Utiliser les données récentes
                    query = f"""
                    SELECT TOP 10
                        CAST([{date_column}] AS DATE) as date_value,
                        COUNT(*) as record_count
                    FROM [{table_name}]
                    WHERE [{date_column}] IS NOT NULL
                      AND [{date_column}] >= DATEADD(month, -12, GETDATE())
                    GROUP BY CAST([{date_column}] AS DATE)
                    ORDER BY CAST([{date_column}] AS DATE) DESC
                    """

                time_df = pd.read_sql(query, conn)
                if not time_df.empty:
                    # Utiliser la fonction de nettoyage pour les types numpy
                    time_analysis[table_name] = {
                        'date_column': str(date_column),
                        'data': clean_numpy_types(time_df.to_dict('records')),
                        'period_count': int(len(time_df)),
                        'numeric_columns': [str(col) for col in numeric_columns],
                        'recent_data': bool(recent_count > 0)
                    }
                    print(f"✅ [NARRATION] {table_name}: {len(time_df)} périodes analysées")
                else:
                    print(f"⚠️ [NARRATION] {table_name}: aucune donnée temporelle trouvée")

            except Exception as e:
                print(f"⚠️ [NARRATION] Erreur analyse temporelle pour {table_name}: {e}")

        print(f"✅ [NARRATION] Analyse temporelle terminée: {len(time_analysis)} tables avec données temporelles")
        return time_analysis

    except Exception as e:
        print(f"❌ [NARRATION] Erreur lors de l'analyse temporelle: {e}")
        import traceback
        print(f"❌ [NARRATION] Stack trace: {traceback.format_exc()}")
        return {}

def find_table_for_column(conn, column_name):
    """
    Trouve dans quelle table se trouve une colonne donnée
    """
    try:
        query = f"""
        SELECT TABLE_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE COLUMN_NAME = '{column_name}'
        """
        df = pd.read_sql(query, conn)
        if df.empty:
            return None
        return df.iloc[0]['TABLE_NAME']
    except Exception:
        return None

def find_table_relations(conn):
    """
    Récupère toutes les relations de clés étrangères entre les tables
    """
    try:
        relations_query = """
        SELECT
            fk.name AS constraint_name,
            tp.name AS parent_table,
            cp.name AS parent_column,
            tr.name AS referenced_table,
            cr.name AS referenced_column
        FROM sys.foreign_keys fk
        INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
        INNER JOIN sys.tables tp ON fk.parent_object_id = tp.object_id
        INNER JOIN sys.columns cp ON fkc.parent_object_id = cp.object_id AND fkc.parent_column_id = cp.column_id
        INNER JOIN sys.tables tr ON fk.referenced_object_id = tr.object_id
        INNER JOIN sys.columns cr ON fkc.referenced_object_id = cr.object_id AND fkc.referenced_column_id = cr.column_id
        """
        df = pd.read_sql(relations_query, conn)
        return df
    except Exception as e:
        print(f"Erreur lors de la récupération des relations: {e}")
        return pd.DataFrame()

def find_join_path(relations_df, table1, table2):
    """
    Trouve le chemin de jointure entre deux tables
    """
    if table1 == table2:
        return []

    # Recherche directe
    direct_relation = relations_df[
        ((relations_df['parent_table'] == table1) & (relations_df['referenced_table'] == table2)) |
        ((relations_df['parent_table'] == table2) & (relations_df['referenced_table'] == table1))
    ]

    if not direct_relation.empty:
        relation = direct_relation.iloc[0]
        if relation['parent_table'] == table1:
            return [{
                'from_table': table1,
                'from_column': relation['parent_column'],
                'to_table': table2,
                'to_column': relation['referenced_column']
            }]
        else:
            return [{
                'from_table': table1,
                'from_column': relation['referenced_column'],
                'to_table': table2,
                'to_column': relation['parent_column']
            }]

    # Si pas de relation directe, chercher via une table intermédiaire (pour l'instant, on se limite aux relations directes)
    return []

def build_filter_conditions(filters: List[AppliedFilter], tables_needed: set, column_table_map: dict):
    """
    Construit les conditions WHERE pour les filtres
    """
    if not filters:
        return []

    conditions = []

    for filter_item in filters:
        column_name = filter_item.column_name
        table_name = filter_item.table_name
        filter_type = filter_item.filter_type

        # Déterminer le nom qualifié de la colonne
        if len(tables_needed) > 1:
            qualified_column = f"[{table_name}].[{column_name}]"
        else:
            qualified_column = f"[{column_name}]"

        if filter_type == "in" and filter_item.values:
            # Filtre de type IN (sélection multiple)
            values_str = ", ".join([f"'{str(v)}'" for v in filter_item.values])
            conditions.append(f"{qualified_column} IN ({values_str})")

        elif filter_type == "equals" and filter_item.values:
            # Filtre d'égalité
            value = filter_item.values[0]
            conditions.append(f"{qualified_column} = '{str(value)}'")

        elif filter_type == "range" and (filter_item.min_value is not None or filter_item.max_value is not None):
            # Filtre de plage (pour les dates/nombres)
            range_conditions = []
            if filter_item.min_value is not None:
                range_conditions.append(f"{qualified_column} >= '{str(filter_item.min_value)}'")
            if filter_item.max_value is not None:
                range_conditions.append(f"{qualified_column} <= '{str(filter_item.max_value)}'")

            if range_conditions:
                conditions.append(f"({' AND '.join(range_conditions)})")

    return conditions

@app.post("/drag-visual")
async def create_drag_visualization(request: DragVisualRequest):
    """
    Crée une visualisation basée sur les paramètres drag & drop
    Support des légendes, agrégations flexibles et graphiques avancés
    Avec jointures automatiques multi-tables
    """
    check_sql_configuration()

    try:
        conn = get_sql_connection()

        # Déterminer les tables nécessaires et construire les jointures
        tables_needed = set()
        column_table_map = {}

        # Mapper chaque colonne à sa table
        for field_name, field_value in [
            ('x_axis', request.x_axis),
            ('y_axis', request.y_axis),
            ('legend', request.legend),
            ('values', request.values)
        ]:
            if field_value:
                # Utiliser la table spécifiée ou détecter automatiquement
                table_field = getattr(request, f"{field_name}_table", None)
                if table_field:
                    table_name = table_field
                else:
                    table_name = find_table_for_column(conn, field_value)

                if table_name:
                    tables_needed.add(table_name)
                    column_table_map[field_value] = table_name
                else:
                    conn.close()
                    raise HTTPException(status_code=400, detail=f"Colonne '{field_value}' non trouvée dans aucune table")

        if len(tables_needed) == 0:
            conn.close()
            raise HTTPException(status_code=400, detail="Aucune colonne valide spécifiée")

        # Récupérer les types de colonnes pour toutes les tables nécessaires
        column_types = {}
        for table_name in tables_needed:
            columns_query = f"""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = '{table_name}'
            """
            columns_df = pd.read_sql(columns_query, conn)
            for _, row in columns_df.iterrows():
                column_types[row['COLUMN_NAME']] = row['DATA_TYPE']

        # Validation de la fonction d'agrégation
        valid_agg_functions = ['SUM', 'AVG', 'COUNT', 'MIN', 'MAX']
        if request.agg_function.upper() not in valid_agg_functions:
            conn.close()
            raise HTTPException(status_code=400, detail=f"Fonction d'agrégation '{request.agg_function}' non supportée. Fonctions supportées: {', '.join(valid_agg_functions)}")

        # Générer la visualisation avec jointures automatiques
        chart_data = await generate_multi_table_chart_data(conn, request, column_table_map, column_types, tables_needed)
        conn.close()

        return chart_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de la visualisation drag & drop: {str(e)}")

@app.post("/narrate")
async def generate_data_narrative(request: NarrateRequest):
    """
    Génère une narration intelligente des données avec Azure OpenAI
    """
    try:
        print(f"🚀 [NARRATION] Début de génération - Focus: {request.focus}, Période: {request.time_period}")

        # Vérifier la connexion
        try:
            conn = get_sql_connection()
            print("✅ [NARRATION] Connexion SQL établie")
        except Exception as conn_error:
            print(f"❌ [NARRATION] Erreur de connexion SQL: {conn_error}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Impossible de se connecter à la base de données",
                    "details": str(conn_error),
                    "type": "connection_error"
                }
            )

        # Analyser la structure de la base de données
        tables_info = analyze_database_structure(conn)
        if not tables_info:
            conn.close()
            print("❌ [NARRATION] Aucune table trouvée")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Aucune table trouvée dans la base de données",
                    "details": "La base de données semble vide ou inaccessible",
                    "type": "no_data_error"
                }
            )

        # Récupérer des échantillons de données
        sample_data = get_sample_data_for_narration(conn, tables_info, request.focus)

        # Analyser les tendances temporelles
        time_analysis = get_time_series_analysis(conn, tables_info)

        conn.close()
        print("✅ [NARRATION] Analyse des données terminée")

        # Préparer le contexte pour l'IA
        context = {
            "tables_info": tables_info,
            "sample_data": sample_data,
            "time_analysis": time_analysis,
            "focus": request.focus,
            "time_period": request.time_period
        }

        print("🤖 [NARRATION] Génération avec Azure OpenAI...")
        # Générer la narration avec Azure OpenAI
        narrative_result = await generate_narrative_with_ai(context, request.include_recommendations)

        if not narrative_result or not narrative_result.get("narrative"):
            print("❌ [NARRATION] Échec de génération IA")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Échec de la génération de narration par l'IA",
                    "details": "Le modèle IA n'a pas pu générer de contenu valide",
                    "type": "ai_generation_error"
                }
            )

        # Préparer la réponse avec nettoyage des types numpy
        data_summary = clean_numpy_types({
            "total_tables": len(tables_info),
            "total_records": sum(info['row_count'] for info in tables_info.values()),
            "tables_with_dates": len([t for t, info in tables_info.items() if info['date_columns']]),
            "focus_area": request.focus
        })

        print("✅ [NARRATION] Génération terminée avec succès")

        # Nettoyer toute la réponse pour éviter les erreurs de sérialisation
        clean_response = clean_numpy_types({
            "success": True,
            "narrative": narrative_result["narrative"],
            "sections": narrative_result["sections"],
            "data_summary": data_summary,
            "generated_at": datetime.now().isoformat(),
            "focus_area": request.focus
        })

        print("✅ [NARRATION] Réponse nettoyée et prête pour sérialisation")
        return NarrateResponse(**clean_response)

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"❌ [NARRATION] Erreur inattendue: {e}")
        import traceback
        print(f"❌ [NARRATION] Stack trace: {traceback.format_exc()}")

        raise HTTPException(
            status_code=500,
            detail={
                "error": "Erreur inattendue lors de la génération de la narration",
                "details": str(e),
                "type": "unexpected_error"
            }
        )

async def generate_narrative_with_ai(context: dict, include_recommendations: bool = True):
    """
    Génère la narration avec Azure OpenAI
    """
    try:
        print("🤖 [NARRATION] Préparation du prompt pour l'IA...")

        # Construire le prompt pour l'IA
        tables_summary = []
        total_records = 0
        for table_name, info in context["tables_info"].items():
            record_count = info.get('row_count', 0)
            total_records += record_count
            column_count = len(info.get('columns', []))
            tables_summary.append(f"- {table_name}: {record_count:,} enregistrements, {column_count} colonnes")

        print(f"🤖 [NARRATION] {len(tables_summary)} tables à analyser, {total_records:,} enregistrements total")

        # Limiter les échantillons pour éviter un prompt trop long
        sample_summary = []
        for table_name, data in context["sample_data"].items():
            if data.get('sample'):
                # Prendre seulement le premier échantillon et limiter les champs
                sample = data['sample'][0] if data['sample'] else {}
                limited_sample = {k: v for k, v in list(sample.items())[:3]}  # Max 3 champs
                sample_summary.append(f"Exemple {table_name}: {limited_sample}")

        time_summary = []
        for table_name, analysis in context["time_analysis"].items():
            period_count = analysis.get('period_count', 0)
            date_column = analysis.get('date_column', 'date')
            time_summary.append(f"Données temporelles {table_name}: {period_count} périodes sur {date_column}")

        focus_instruction = f"Focus spécial sur: {context['focus']}" if context['focus'] else "Analyse générale de toutes les données"

        # Construire un prompt plus robuste
        prompt = f"""Tu es un analyste de données expert. Crée une narration métier claire et factuelle.

DONNÉES DISPONIBLES:
{chr(10).join(tables_summary)}

ÉCHANTILLONS (exemples):
{chr(10).join(sample_summary[:2])}

DONNÉES TEMPORELLES:
{chr(10).join(time_summary) if time_summary else "Aucune donnée temporelle disponible"}

CONSIGNES:
- {focus_instruction}
- Langage simple et accessible
- Basé uniquement sur les données fournies
- Structure claire avec sections
- Maximum 400 mots

STRUCTURE:
## Introduction
[Présentation des données]

## Faits saillants
[Points clés observés]

## Interprétation métier
[Signification pour l'activité]

{"## Recommandations" if include_recommendations else ""}
{"[Actions basées sur l'analyse]" if include_recommendations else ""}

Génère la narration:"""

        print(f"🤖 [NARRATION] Prompt préparé ({len(prompt)} caractères)")
        print(f"🤖 [NARRATION] Appel à Azure OpenAI...")

        # Appel à Azure OpenAI avec gestion d'erreur
        try:
            narrative_text = call_azure_gpt(
                prompt,
                system_message="Tu es un expert en analyse de données et en storytelling métier. Réponds toujours en français avec des sections claires."
            )

            if not narrative_text or len(narrative_text.strip()) < 50:
                print("⚠️ [NARRATION] Réponse IA trop courte ou vide")
                raise Exception("Réponse IA invalide ou trop courte")

            print(f"✅ [NARRATION] Réponse IA reçue ({len(narrative_text)} caractères)")

        except Exception as ai_error:
            print(f"❌ [NARRATION] Erreur appel Azure OpenAI: {ai_error}")
            raise Exception(f"Échec de l'appel Azure OpenAI: {ai_error}")

        # Parser la réponse en sections
        sections = parse_narrative_sections(narrative_text)
        print(f"✅ [NARRATION] {len(sections)} sections parsées")

        return {
            "narrative": narrative_text,
            "sections": sections
        }

    except Exception as e:
        print(f"❌ [NARRATION] Erreur génération IA: {e}")
        print("🔄 [NARRATION] Utilisation du fallback...")

        # Fallback en cas d'erreur IA
        fallback_narrative = generate_fallback_narrative(context)
        return {
            "narrative": fallback_narrative,
            "sections": parse_narrative_sections(fallback_narrative)
        }

def parse_narrative_sections(narrative_text: str):
    """
    Parse le texte narratif en sections structurées
    """
    sections = []
    current_section = None

    lines = narrative_text.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith('##'):
            # Nouvelle section
            if current_section:
                sections.append(current_section)

            current_section = NarrativeSection(
                title=line.replace('##', '').strip(),
                content="",
                insights=[],
                metrics={}
            )
        elif current_section and line:
            if line.startswith('- ') or line.startswith('• '):
                current_section.insights.append(line[2:])
            else:
                current_section.content += line + " "

    if current_section:
        sections.append(current_section)

    return sections

def generate_fallback_narrative(context: dict):
    """
    Génère une narration de base en cas d'erreur IA
    """
    tables_info = context.get("tables_info", {})
    tables_count = len(tables_info)
    total_records = sum(info.get('row_count', 0) for info in tables_info.values())

    # Identifier les tables principales
    main_tables = []
    for table_name, info in tables_info.items():
        if info.get('row_count', 0) > 0:
            main_tables.append(f"{table_name} ({info['row_count']:,} lignes)")

    # Identifier les données temporelles
    time_tables = []
    for table_name, info in tables_info.items():
        if info.get('date_columns'):
            time_tables.append(table_name)

    focus_text = f" avec un focus sur {context.get('focus')}" if context.get('focus') else ""

    narrative = f"""## Introduction
L'analyse{focus_text} porte sur {tables_count} table(s) contenant un total de {total_records:,} enregistrements dans la base de données BrAInBI.

## Faits saillants
- Tables principales : {', '.join(main_tables[:3]) if main_tables else 'Aucune donnée'}
- Données temporelles disponibles : {len(time_tables)} table(s) avec historique
- Structure de données : {tables_count} entités métier identifiées

## Interprétation métier
La base de données contient des informations structurées permettant une analyse approfondie de l'activité. {"Les données temporelles permettent un suivi des tendances." if time_tables else "Aucune donnée temporelle détectée pour l'analyse des tendances."}

## Recommandations
- Utiliser les outils de visualisation pour explorer les données en détail
- Configurer des filtres pour affiner les analyses par période ou segment
- Exploiter les relations entre tables pour des analyses croisées
{"- Analyser les tendances temporelles sur " + ", ".join(time_tables[:2]) if time_tables else "- Envisager l'ajout de données temporelles pour le suivi des évolutions"}"""

    return narrative.strip()

async def generate_multi_table_chart_data(conn, request: DragVisualRequest, column_table_map: dict, column_types: dict, tables_needed: set) -> DragVisualResponse:
    """
    Génère les données du graphique avec jointures automatiques multi-tables
    """
    try:
        x_axis = request.x_axis
        y_axis = request.y_axis
        legend = request.legend
        values = request.values
        agg_func = request.agg_function.upper()
        chart_type = request.chart_type.lower()

        # Déterminer la colonne de valeurs à agréger
        value_column = values or y_axis

        if not value_column and agg_func != 'COUNT':
            raise HTTPException(status_code=400, detail="Une colonne de valeurs est requise pour les fonctions d'agrégation autres que COUNT")

        # Construire la requête avec jointures si nécessaire
        if len(tables_needed) == 1:
            # Une seule table, requête simple
            table_name = list(tables_needed)[0]
            from_clause = f"FROM [{table_name}]"
        else:
            # Plusieurs tables, construire les jointures
            relations_df = find_table_relations(conn)
            if relations_df.empty:
                raise HTTPException(status_code=400, detail="Aucune relation trouvée entre les tables pour effectuer les jointures")

            # Prendre la première table comme table principale
            main_table = list(tables_needed)[0]
            other_tables = list(tables_needed)[1:]

            # Construire les jointures
            joins = []
            for other_table in other_tables:
                join_path = find_join_path(relations_df, main_table, other_table)
                if not join_path:
                    raise HTTPException(status_code=400, detail=f"Aucune relation trouvée entre {main_table} et {other_table}")

                for join_info in join_path:
                    join_clause = f"LEFT JOIN [{join_info['to_table']}] ON [{join_info['from_table']}].[{join_info['from_column']}] = [{join_info['to_table']}].[{join_info['to_column']}]"
                    joins.append(join_clause)

            from_clause = f"FROM [{main_table}] " + " ".join(joins)

        # Construire les parties SELECT et GROUP BY
        select_parts = []
        group_by_parts = []

        # Axe X avec préfixe de table si nécessaire
        if x_axis:
            x_table = column_table_map.get(x_axis, list(tables_needed)[0])
            if len(tables_needed) > 1:
                select_parts.append(f"[{x_table}].[{x_axis}] as {x_axis}")
            else:
                select_parts.append(x_axis)
            group_by_parts.append(f"[{x_table}].[{x_axis}]" if len(tables_needed) > 1 else x_axis)

        # Légende avec préfixe de table si nécessaire
        if legend and legend != x_axis:
            legend_table = column_table_map.get(legend, list(tables_needed)[0])
            if len(tables_needed) > 1:
                select_parts.append(f"[{legend_table}].[{legend}] as {legend}")
            else:
                select_parts.append(legend)
            group_by_parts.append(f"[{legend_table}].[{legend}]" if len(tables_needed) > 1 else legend)

        # Valeur agrégée
        if agg_func == 'COUNT':
            select_parts.append("COUNT(*) as agg_value")
            ylabel = "Nombre d'occurrences"
        else:
            if not value_column:
                raise HTTPException(status_code=400, detail=f"Colonne de valeurs requise pour {agg_func}")

            # Vérifier que la colonne de valeurs est numérique
            value_type = column_types.get(value_column, '').lower()
            numeric_types = ['int', 'bigint', 'smallint', 'tinyint', 'decimal', 'numeric', 'float', 'real', 'money', 'smallmoney']
            if value_type not in numeric_types:
                raise HTTPException(status_code=400, detail=f"Colonne de valeurs '{value_column}' doit être numérique pour {agg_func}. Type actuel: {value_type}")

            value_table = column_table_map.get(value_column, list(tables_needed)[0])
            if len(tables_needed) > 1:
                select_parts.append(f"{agg_func}(CAST([{value_table}].[{value_column}] AS FLOAT)) as agg_value")
            else:
                select_parts.append(f"{agg_func}(CAST({value_column} AS FLOAT)) as agg_value")
            ylabel = f"{agg_func} de {value_column}"

        # Construire la requête complète
        if not select_parts:
            raise HTTPException(status_code=400, detail="Au moins un axe ou une légende doit être spécifié")

        # Construire les conditions WHERE pour les valeurs non nulles
        where_conditions = []
        for col in group_by_parts:
            where_conditions.append(f"{col} IS NOT NULL")

        if value_column and agg_func != 'COUNT':
            value_table = column_table_map.get(value_column, list(tables_needed)[0])
            if len(tables_needed) > 1:
                where_conditions.append(f"[{value_table}].[{value_column}] IS NOT NULL")
            else:
                where_conditions.append(f"{value_column} IS NOT NULL")

        # Ajouter les conditions de filtre
        filter_conditions = build_filter_conditions(request.filters, tables_needed, column_table_map)
        where_conditions.extend(filter_conditions)

        query = f"""
        SELECT {', '.join(select_parts)}
        {from_clause}
        """

        if where_conditions:
            query += f" WHERE {' AND '.join(where_conditions)}"

        if group_by_parts:
            query += f" GROUP BY {', '.join(group_by_parts)}"

        query += f" ORDER BY {group_by_parts[0] if group_by_parts else '1'}"

        print(f"🔍 Requête générée: {query}")  # Debug

        df = pd.read_sql(query, conn)

        if df.empty:
            raise HTTPException(status_code=400, detail=f"Aucune donnée trouvée pour les colonnes spécifiées")

        # Nettoyer les données
        df = df.dropna()
        if df.empty:
            raise HTTPException(status_code=400, detail="Aucune donnée valide trouvée après nettoyage des valeurs nulles")

        # Préparer les données selon le type de graphique (même logique que l'ancienne fonction)
        return await prepare_chart_response(df, chart_type, x_axis, legend, ylabel, len(df))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la génération des données multi-tables: {str(e)}")

async def prepare_chart_response(df, chart_type, x_axis, legend, ylabel, data_count):
    """
    Prépare la réponse du graphique selon le type demandé
    """
    if chart_type == "pie":
        if not x_axis:
            raise HTTPException(status_code=400, detail="Axe X requis pour le graphique circulaire")

        return DragVisualResponse(
            type="pie",
            title=f"Répartition par {x_axis}",
            x=df[x_axis].tolist(),
            y=df['agg_value'].tolist(),
            xlabel=x_axis,
            ylabel=ylabel,
            success=True,
            message=f"Graphique généré avec succès ({data_count} éléments)",
            data_points=data_count
        )

    elif legend and legend in df.columns:
        # Graphique avec légende - données groupées
        pivot_df = df.pivot_table(
            index=x_axis if x_axis else legend,
            columns=legend if legend != x_axis else None,
            values='agg_value',
            fill_value=0
        )

        x_values = pivot_df.index.tolist()
        legend_values = pivot_df.columns.tolist() if hasattr(pivot_df, 'columns') else []
        y_values = pivot_df.values.tolist() if len(legend_values) > 1 else pivot_df.tolist()

        return DragVisualResponse(
            type=chart_type,
            title=f"{ylabel} par {x_axis}" + (f" et {legend}" if legend and legend != x_axis else ""),
            x=x_values,
            y=y_values,
            legend=legend_values if len(legend_values) > 1 else None,
            xlabel=x_axis or legend,
            ylabel=ylabel,
            success=True,
            message=f"Graphique avec légende généré avec succès ({data_count} points de données)",
            data_points=data_count
        )

    else:
        # Graphique simple sans légende
        if not x_axis:
            raise HTTPException(status_code=400, detail="Axe X requis pour ce type de graphique")

        return DragVisualResponse(
            type=chart_type,
            title=f"{ylabel} par {x_axis}",
            x=df[x_axis].tolist(),
            y=df['agg_value'].tolist(),
            xlabel=x_axis,
            ylabel=ylabel,
            success=True,
            message=f"Graphique généré avec succès ({data_count} points de données)",
            data_points=data_count
        )

async def generate_drag_chart_data(conn, request: DragVisualRequest, column_types: dict) -> DragVisualResponse:
    """
    Génère les données du graphique pour l'interface drag & drop
    """
    try:
        table = request.table
        x_axis = request.x_axis
        y_axis = request.y_axis
        legend = request.legend
        values = request.values
        agg_func = request.agg_function.upper()
        chart_type = request.chart_type.lower()

        # Déterminer la colonne de valeurs à agréger
        value_column = values or y_axis

        if not value_column and agg_func != 'COUNT':
            raise HTTPException(status_code=400, detail="Une colonne de valeurs est requise pour les fonctions d'agrégation autres que COUNT")

        # Construire la requête SQL selon les paramètres
        select_parts = []
        group_by_parts = []

        # Axe X
        if x_axis:
            select_parts.append(x_axis)
            group_by_parts.append(x_axis)

        # Légende
        if legend and legend != x_axis:
            select_parts.append(legend)
            group_by_parts.append(legend)

        # Valeur agrégée
        if agg_func == 'COUNT':
            select_parts.append("COUNT(*) as agg_value")
            ylabel = "Nombre d'occurrences"
        else:
            if not value_column:
                raise HTTPException(status_code=400, detail=f"Colonne de valeurs requise pour {agg_func}")

            # Vérifier que la colonne de valeurs est numérique
            value_type = column_types.get(value_column, '').lower()
            numeric_types = ['int', 'bigint', 'smallint', 'tinyint', 'decimal', 'numeric', 'float', 'real', 'money', 'smallmoney']
            if value_type not in numeric_types:
                raise HTTPException(status_code=400, detail=f"Colonne de valeurs '{value_column}' doit être numérique pour {agg_func}. Type actuel: {value_type}")

            select_parts.append(f"{agg_func}(CAST({value_column} AS FLOAT)) as agg_value")
            ylabel = f"{agg_func} de {value_column}"

        # Construire la requête
        if not select_parts:
            raise HTTPException(status_code=400, detail="Au moins un axe ou une légende doit être spécifié")

        query = f"""
        SELECT {', '.join(select_parts)}
        FROM [{table}]
        WHERE {' AND '.join([f'{col} IS NOT NULL' for col in group_by_parts + ([value_column] if value_column and agg_func != 'COUNT' else [])])}
        """

        if group_by_parts:
            query += f" GROUP BY {', '.join(group_by_parts)}"

        query += f" ORDER BY {group_by_parts[0] if group_by_parts else '1'}"

        df = pd.read_sql(query, conn)

        if df.empty:
            raise HTTPException(status_code=400, detail=f"Aucune donnée trouvée dans la table '{table}' pour les colonnes spécifiées")

        # Nettoyer les données
        df = df.dropna()
        if df.empty:
            raise HTTPException(status_code=400, detail="Aucune donnée valide trouvée après nettoyage des valeurs nulles")

        # Préparer les données selon le type de graphique
        if chart_type == "pie":
            # Pour pie chart, utiliser x_axis comme labels et agg_value comme values
            if not x_axis:
                raise HTTPException(status_code=400, detail="Axe X requis pour le graphique circulaire")

            return DragVisualResponse(
                type="pie",
                title=f"Répartition par {x_axis}",
                x=df[x_axis].tolist(),
                y=df['agg_value'].tolist(),
                xlabel=x_axis,
                ylabel=ylabel,
                success=True,
                message=f"Graphique généré avec succès ({len(df)} éléments)",
                data_points=len(df)
            )

        elif legend and legend in df.columns:
            # Graphique avec légende - données groupées
            pivot_df = df.pivot_table(
                index=x_axis if x_axis else legend,
                columns=legend if legend != x_axis else None,
                values='agg_value',
                fill_value=0
            )

            x_values = pivot_df.index.tolist()
            legend_values = pivot_df.columns.tolist() if hasattr(pivot_df, 'columns') else []
            y_values = pivot_df.values.tolist() if len(legend_values) > 1 else pivot_df.tolist()

            return DragVisualResponse(
                type=chart_type,
                title=f"{ylabel} par {x_axis}" + (f" et {legend}" if legend and legend != x_axis else ""),
                x=x_values,
                y=y_values,
                legend=legend_values if len(legend_values) > 1 else None,
                xlabel=x_axis or legend,
                ylabel=ylabel,
                success=True,
                message=f"Graphique avec légende généré avec succès ({len(df)} points de données)",
                data_points=len(df)
            )

        else:
            # Graphique simple sans légende
            if not x_axis:
                raise HTTPException(status_code=400, detail="Axe X requis pour ce type de graphique")

            return DragVisualResponse(
                type=chart_type,
                title=f"{ylabel} par {x_axis}",
                x=df[x_axis].tolist(),
                y=df['agg_value'].tolist(),
                xlabel=x_axis,
                ylabel=ylabel,
                success=True,
                message=f"Graphique généré avec succès ({len(df)} points de données)",
                data_points=len(df)
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la génération des données drag & drop: {str(e)}")

@app.get("/schema/table/{table_name}")
async def get_table_details(table_name: str):
    """Retourne les détails d'une table spécifique"""
    try:
        conn = get_sql_connection()

        # Vérifier que la table existe
        check_query = f"""
        SELECT COUNT(*) as table_exists
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = '{table_name}' AND TABLE_TYPE = 'BASE TABLE'
        """

        exists_df = pd.read_sql(check_query, conn)
        if exists_df.iloc[0]['table_exists'] == 0:
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' non trouvée")

        # Colonnes
        columns_query = f"""
        SELECT
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            CHARACTER_MAXIMUM_LENGTH,
            NUMERIC_PRECISION,
            NUMERIC_SCALE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = '{table_name}'
        ORDER BY ORDINAL_POSITION
        """
        columns_df = pd.read_sql(columns_query, conn)

        # Échantillon de données
        sample_query = f"SELECT TOP 10 * FROM [{table_name}]"
        sample_df = pd.read_sql(sample_query, conn)

        # Nombre total de lignes
        count_query = f"SELECT COUNT(*) as total_rows FROM [{table_name}]"
        count_df = pd.read_sql(count_query, conn)

        conn.close()

        return {
            "table_name": table_name,
            "columns": columns_df.to_dict('records'),
            "sample_data": sample_df.to_dict('records'),
            "total_rows": count_df.iloc[0]['total_rows'],
            "column_count": len(columns_df)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des détails de la table: {str(e)}")

# Endpoints pour la configuration SQL dynamique
@app.post("/config/sql", response_model=SQLConfigResponse)
async def configure_sql(request: SQLConfigRequest):
    """Configure la connexion SQL Server"""
    global current_sql_config

    try:
        # Créer la configuration
        config = SQLConfig(
            server=request.server,
            database=request.database,
            driver=request.driver or "ODBC Driver 17 for SQL Server",
            username=request.username,
            password=request.password,
            use_windows_auth=request.use_windows_auth if request.use_windows_auth is not None else True,
            port=request.port,
            timeout=request.timeout or 30
        )

        # Tester la connexion avant de sauvegarder
        test_result = await test_sql_connection_internal(config)
        if not test_result.success:
            return SQLConfigResponse(
                success=False,
                message=f"Configuration invalide: {test_result.message}",
                config=None
            )

        # Sauvegarder la configuration
        current_sql_config = config

        # Recharger les données avec la nouvelle configuration
        try:
            load_all_tables()
            detect_relations(dataframes)
        except Exception as e:
            print(f"⚠️ Erreur lors du rechargement des données: {str(e)}")

        return SQLConfigResponse(
            success=True,
            message="Configuration SQL sauvegardée avec succès",
            config=config
        )

    except Exception as e:
        return SQLConfigResponse(
            success=False,
            message=f"Erreur lors de la configuration: {str(e)}",
            config=None
        )

@app.post("/config/sql/test", response_model=SQLTestResponse)
async def test_sql_connection(request: SQLConfigRequest):
    """Teste la connexion SQL Server avec la configuration fournie"""
    try:
        config = SQLConfig(
            server=request.server,
            database=request.database,
            driver=request.driver or "ODBC Driver 17 for SQL Server",
            username=request.username,
            password=request.password,
            use_windows_auth=request.use_windows_auth if request.use_windows_auth is not None else True,
            port=request.port,
            timeout=request.timeout or 30
        )

        return await test_sql_connection_internal(config)

    except Exception as e:
        return SQLTestResponse(
            success=False,
            message=f"Erreur lors du test: {str(e)}",
            error_code="CONFIG_ERROR"
        )

async def test_sql_connection_internal(config: SQLConfig) -> SQLTestResponse:
    """Fonction interne pour tester une connexion SQL"""
    import time

    try:
        start_time = time.time()

        # Construire la chaîne de connexion
        conn_str = build_connection_string(config)

        # Tenter la connexion
        conn = pyodbc.connect(conn_str)

        # Exécuter une requête simple
        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test, @@VERSION as version, DB_NAME() as current_db")
        result = cursor.fetchone()

        connection_time = time.time() - start_time

        # Récupérer les informations du serveur
        server_info = {
            "version": result.version if result else "Unknown",
            "current_database": result.current_db if result else config.database,
            "server": config.server
        }

        cursor.close()
        conn.close()

        return SQLTestResponse(
            success=True,
            message=f"Connexion réussie à {config.server}/{config.database}",
            connection_time=connection_time,
            server_info=server_info
        )

    except pyodbc.Error as e:
        error_code = "SQL_ERROR"
        message = str(e)

        # Analyser les erreurs courantes
        if "Login failed" in message:
            error_code = "AUTH_ERROR"
            message = "Échec de l'authentification. Vérifiez le nom d'utilisateur et le mot de passe."
        elif "Cannot open database" in message:
            error_code = "DATABASE_ERROR"
            message = f"Impossible d'ouvrir la base de données '{config.database}'. Vérifiez que la base existe."
        elif "server was not found" in message or "could not open a connection" in message:
            error_code = "SERVER_ERROR"
            message = f"Serveur '{config.server}' introuvable. Vérifiez le nom du serveur et la connectivité réseau."
        elif "driver" in message.lower():
            error_code = "DRIVER_ERROR"
            message = f"Driver ODBC '{config.driver}' non trouvé. Installez le driver ou utilisez un autre nom."

        return SQLTestResponse(
            success=False,
            message=message,
            error_code=error_code
        )

    except Exception as e:
        return SQLTestResponse(
            success=False,
            message=f"Erreur inattendue: {str(e)}",
            error_code="UNKNOWN_ERROR"
        )

def build_connection_string(config: SQLConfig) -> str:
    """Construit la chaîne de connexion ODBC à partir de la configuration"""
    conn_parts = [f"DRIVER={{{config.driver}}}"]

    # Serveur et port
    if config.port:
        conn_parts.append(f"SERVER={config.server},{config.port}")
    else:
        conn_parts.append(f"SERVER={config.server}")

    # Base de données
    conn_parts.append(f"DATABASE={config.database}")

    # Authentification
    if config.use_windows_auth:
        conn_parts.append("Trusted_Connection=yes")
    else:
        if config.username:
            conn_parts.append(f"UID={config.username}")
        if config.password:
            conn_parts.append(f"PWD={config.password}")

    # Timeout
    conn_parts.append(f"Connection Timeout={config.timeout}")

    return ";".join(conn_parts) + ";"

@app.get("/config/sql/current")
async def get_current_sql_config():
    """Récupère la configuration SQL actuelle (sans le mot de passe)"""
    if current_sql_config is None:
        return {
            "configured": False,
            "message": "Aucune configuration SQL définie"
        }

    # Retourner la config sans le mot de passe
    config_dict = current_sql_config.model_dump()
    config_dict.pop("password", None)  # Supprimer le mot de passe pour la sécurité

    return {
        "configured": True,
        "config": config_dict
    }

# Endpoints pour la gestion des visualisations sauvegardées
@app.post("/saved-visuals")
async def save_visualization(request: SaveVisualizationRequest):
    """Sauvegarde une nouvelle visualisation"""
    try:
        # Générer un ID unique
        viz_id = generate_visualization_id()

        # Créer l'objet SavedVisualization
        now = datetime.now()
        saved_viz = SavedVisualization(
            id=viz_id,
            name=request.name,
            description=request.description,
            chart_type=request.chart_type,
            table=request.table,
            x_axis=request.x_axis,
            y_axis=request.y_axis,
            legend=request.legend,
            values=request.values,
            agg_function=request.agg_function,
            x_axis_table=request.x_axis_table,
            y_axis_table=request.y_axis_table,
            legend_table=request.legend_table,
            values_table=request.values_table,
            filters=request.filters,
            created_at=now,
            updated_at=now,
            tags=request.tags
        )

        # Ajouter au cache
        saved_visualizations[viz_id] = saved_viz

        # Sauvegarder dans le fichier
        save_visualizations_to_file()

        return {
            "success": True,
            "message": "Visualisation sauvegardée avec succès",
            "id": viz_id,
            "visualization": SavedVisualizationResponse(
                id=saved_viz.id,
                name=saved_viz.name,
                description=saved_viz.description,
                chart_type=saved_viz.chart_type,
                created_at=saved_viz.created_at,
                updated_at=saved_viz.updated_at,
                user_id=saved_viz.user_id,
                tags=saved_viz.tags
            )
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la sauvegarde: {str(e)}")

@app.get("/saved-visuals")
async def get_saved_visualizations():
    """Récupère la liste de toutes les visualisations sauvegardées"""
    try:
        visualizations = []
        for viz_id, viz in saved_visualizations.items():
            visualizations.append(SavedVisualizationResponse(
                id=viz.id,
                name=viz.name,
                description=viz.description,
                chart_type=viz.chart_type,
                created_at=viz.created_at,
                updated_at=viz.updated_at,
                user_id=viz.user_id,
                tags=viz.tags
            ))

        # Trier par date de création (plus récent en premier)
        visualizations.sort(key=lambda x: x.created_at, reverse=True)

        return {
            "success": True,
            "count": len(visualizations),
            "visualizations": visualizations
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération: {str(e)}")

@app.get("/saved-visuals/{viz_id}")
async def get_visualization_details(viz_id: str):
    """Récupère les détails complets d'une visualisation sauvegardée"""
    try:
        if viz_id not in saved_visualizations:
            raise HTTPException(status_code=404, detail="Visualisation non trouvée")

        viz = saved_visualizations[viz_id]
        return {
            "success": True,
            "visualization": viz
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération: {str(e)}")

@app.delete("/saved-visuals/{viz_id}")
async def delete_visualization(viz_id: str):
    """Supprime une visualisation sauvegardée"""
    try:
        if viz_id not in saved_visualizations:
            raise HTTPException(status_code=404, detail="Visualisation non trouvée")

        # Récupérer le nom pour le message de confirmation
        viz_name = saved_visualizations[viz_id].name

        # Supprimer du cache
        del saved_visualizations[viz_id]

        # Sauvegarder dans le fichier
        save_visualizations_to_file()

        return {
            "success": True,
            "message": f"Visualisation '{viz_name}' supprimée avec succès"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression: {str(e)}")

if __name__ == "__main__":
    try:
        import uvicorn
        print("🚀 Démarrage du serveur BrAInBI...")
        print("🧠 Business Intelligence augmentée par l'IA")
        print("🗄️  Connexion SQL Server multi-tables en mode live")
        print("🔍 Découverte automatique du schéma relationnel")
        print("� Chargement automatique de toutes les tables")
        print("💾 Chargement des visualisations sauvegardées")
        print("�📍 URL: http://localhost:8000")
        print("📚 Documentation: http://localhost:8000/docs")
        print("🔗 Schéma des relations: http://localhost:8000/schema/relations")
        print("📋 Informations des données: http://localhost:8000/data/info")
        print("💡 Explore your Data. Amplified by AI.")

        # Charger les visualisations sauvegardées
        try:
            print("\n💾 Chargement des visualisations sauvegardées...")
            load_saved_visualizations()
            print("✅ Visualisations chargées\n")
        except Exception as e:
            print(f"⚠️ Erreur lors du chargement des visualisations: {str(e)}")

        # Note: Les tables ne sont plus chargées automatiquement au démarrage
        # Elles seront chargées après configuration via /config/sql
        print("⚠️  Configuration SQL requise avant utilisation")
        print("   Utilisez l'endpoint POST /config/sql pour configurer votre base de données\n")
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
    except ImportError:
        print("❌ uvicorn non installé. Installez avec: pip install uvicorn")
    except Exception as e:
        print(f"❌ Erreur de démarrage: {e}")
