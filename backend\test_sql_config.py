#!/usr/bin/env python3
"""
Script de test pour la configuration SQL dynamique de BrAInBI
Teste les différents endpoints et configurations
"""

import requests
import json
import time

API_BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None, description=""):
    """Teste un endpoint et affiche le résultat"""
    print(f"\n🔍 Test: {description}")
    print(f"   {method} {endpoint}")
    
    try:
        if method == "GET":
            response = requests.get(f"{API_BASE_URL}{endpoint}")
        elif method == "POST":
            response = requests.post(f"{API_BASE_URL}{endpoint}", json=data)
        elif method == "DELETE":
            response = requests.delete(f"{API_BASE_URL}{endpoint}")
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Succès")
            if isinstance(result, dict) and 'message' in result:
                print(f"   Message: {result['message']}")
            return True, result
        else:
            print(f"   ❌ Erreur: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False, None

def main():
    print("🚀 Test de la configuration SQL dynamique BrAInBI")
    print("=" * 60)
    
    # Test 1: Vérifier l'état initial
    print("\n📋 PHASE 1: Vérification de l'état initial")
    test_endpoint("GET", "/health", description="Santé de l'API")
    test_endpoint("GET", "/config/sql/current", description="Configuration SQL actuelle")
    
    # Test 2: Tester une configuration invalide
    print("\n📋 PHASE 2: Test de configuration invalide")
    invalid_config = {
        "server": "serveur_inexistant",
        "database": "base_inexistante",
        "driver": "ODBC Driver 17 for SQL Server",
        "use_windows_auth": True
    }
    test_endpoint("POST", "/config/sql/test", invalid_config, "Test connexion invalide")
    
    # Test 3: Tester les endpoints sans configuration
    print("\n📋 PHASE 3: Test des endpoints sans configuration")
    test_endpoint("GET", "/data/info", description="Infos données sans config")
    test_endpoint("GET", "/schema/tables", description="Tables sans config")
    
    # Test 4: Configuration valide (exemple avec localhost)
    print("\n📋 PHASE 4: Test de configuration valide")
    valid_config = {
        "server": "localhost\\SQLSERVER",
        "database": "BrAInBIDemo",
        "driver": "ODBC Driver 17 for SQL Server",
        "use_windows_auth": True,
        "timeout": 30
    }
    
    print("\n⚠️  ATTENTION: Modifiez les paramètres ci-dessous selon votre environnement:")
    print(f"   Serveur: {valid_config['server']}")
    print(f"   Base: {valid_config['database']}")
    print("\n   Pour tester avec votre configuration, modifiez le fichier test_sql_config.py")
    
    # Demander confirmation avant de tester la configuration valide
    response = input("\n❓ Voulez-vous tester avec cette configuration ? (y/N): ")
    if response.lower() == 'y':
        success, result = test_endpoint("POST", "/config/sql/test", valid_config, "Test connexion valide")
        
        if success and result.get('success'):
            print(f"   Temps de connexion: {result.get('connection_time', 'N/A')}s")
            if 'server_info' in result:
                print(f"   Version SQL: {result['server_info'].get('version', 'N/A')[:50]}...")
            
            # Si le test réussit, sauvegarder la configuration
            save_response = input("\n❓ Sauvegarder cette configuration ? (y/N): ")
            if save_response.lower() == 'y':
                test_endpoint("POST", "/config/sql", valid_config, "Sauvegarde configuration")
                
                # Tester les endpoints avec configuration
                print("\n📋 PHASE 5: Test des endpoints avec configuration")
                test_endpoint("GET", "/config/sql/current", description="Configuration actuelle")
                test_endpoint("GET", "/health", description="Santé avec config")
                test_endpoint("GET", "/data/info", description="Infos données avec config")
    
    # Test 5: Test des différents types d'authentification
    print("\n📋 PHASE 6: Exemples de configurations")
    
    configs_examples = [
        {
            "name": "Windows Auth (localhost)",
            "config": {
                "server": "localhost",
                "database": "master",
                "use_windows_auth": True
            }
        },
        {
            "name": "SQL Auth (exemple)",
            "config": {
                "server": "localhost",
                "database": "master",
                "use_windows_auth": False,
                "username": "sa",
                "password": "VotreMotDePasse"
            }
        },
        {
            "name": "Serveur distant avec port",
            "config": {
                "server": "*************",
                "database": "Production",
                "port": 1433,
                "use_windows_auth": True
            }
        }
    ]
    
    print("\n💡 Exemples de configurations possibles:")
    for i, example in enumerate(configs_examples, 1):
        print(f"\n   {i}. {example['name']}:")
        for key, value in example['config'].items():
            if key == 'password':
                value = "***"
            print(f"      {key}: {value}")
    
    print("\n✅ Tests terminés!")
    print("\n📝 Résumé:")
    print("   - L'API supporte maintenant la configuration SQL dynamique")
    print("   - Les endpoints vérifient la configuration avant d'accéder aux données")
    print("   - Différents types d'authentification sont supportés")
    print("   - Les erreurs de connexion sont gérées proprement")
    print("\n🔗 Endpoints disponibles:")
    print("   POST /config/sql/test - Tester une configuration")
    print("   POST /config/sql - Sauvegarder une configuration")
    print("   GET /config/sql/current - Voir la configuration actuelle")
    print("   GET /health - État de l'API et de la connexion SQL")

if __name__ == "__main__":
    main()
