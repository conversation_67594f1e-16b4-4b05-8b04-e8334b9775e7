{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\components\\\\DragVisualPage.js\",\n  _s = $RefreshSig$();\n/**\n * DragVisualPage - Interface drag & drop pour créer des visualisations\n * Permet de glisser-déposer des colonnes pour construire des graphiques\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport { BrAInBIHeader, DarkCard, SectionHeader, PrimaryButton, SecondaryButton, DarkSpinner, DarkBadge } from './YellowMindUI';\nimport { ColumnsPanel, DropZonesPanel, FixedDropBar, AggregationSelector, ChartTypeSelector, DraggableColumn } from './DragDropComponents';\nimport { FiltersSection } from './FilterComponents';\nimport { NarrationSettings, DataStorytellingPanel, NetworkDiagnostic, useDataStorytelling } from './DataStorytellingComponents';\nimport SaveVisualizationModal from './SaveVisualizationModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\nconst DragVisualPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n\n  // États pour les données\n  const [tables, setTables] = useState([]);\n  const [columns, setColumns] = useState({});\n  const [chartData, setChartData] = useState(null);\n\n  // États pour les sélections\n  const [selectedTable, setSelectedTable] = useState('');\n  const [xAxis, setXAxis] = useState(null);\n  const [yAxis, setYAxis] = useState(null);\n  const [legend, setLegend] = useState(null);\n  const [values, setValues] = useState(null);\n  const [aggFunction, setAggFunction] = useState('SUM');\n  const [chartType, setChartType] = useState('bar');\n\n  // États pour l'interface\n  const [loading, setLoading] = useState(false);\n  const [loadingTables, setLoadingTables] = useState(true);\n  const [loadingColumns, setLoadingColumns] = useState(false);\n  const [error, setError] = useState('');\n  const [activeId, setActiveId] = useState(null);\n  const [showDebugJson, setShowDebugJson] = useState(false);\n  const [filters, setFilters] = useState([]);\n  const [showNetworkDiagnostic, setShowNetworkDiagnostic] = useState(false);\n  const [showFixedDropBar, setShowFixedDropBar] = useState(true);\n  const [lastScrollY, setLastScrollY] = useState(0);\n  const [autoHideBar, setAutoHideBar] = useState(false);\n  const [toast, setToast] = useState({\n    show: false,\n    message: '',\n    type: 'success'\n  });\n\n  // États pour la sauvegarde des visualisations\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const [savingVisualization, setSavingVisualization] = useState(false);\n  const [savedConfigurations, setSavedConfigurations] = useState([]);\n\n  // Hook pour la narration intelligente\n  const {\n    narrative,\n    loading: narrativeLoading,\n    error: narrativeError,\n    debugInfo,\n    generateNarrative,\n    copyToClipboard,\n    exportNarrative\n  } = useDataStorytelling();\n\n  // Charger les tables au démarrage\n  useEffect(() => {\n    loadTables();\n  }, []);\n\n  // Gestion du scroll pour auto-masquer la barre fixe\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n      if (autoHideBar) {\n        if (currentScrollY > lastScrollY && currentScrollY > 100) {\n          // Scroll vers le bas - masquer la barre\n          setShowFixedDropBar(false);\n        } else if (currentScrollY < lastScrollY) {\n          // Scroll vers le haut - montrer la barre\n          setShowFixedDropBar(true);\n        }\n      }\n      setLastScrollY(currentScrollY);\n    };\n    if (autoHideBar) {\n      window.addEventListener('scroll', handleScroll, {\n        passive: true\n      });\n      return () => window.removeEventListener('scroll', handleScroll);\n    }\n  }, [lastScrollY, autoHideBar]);\n\n  // Raccourci clavier pour basculer la barre fixe (Ctrl/Cmd + B)\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {\n        e.preventDefault();\n        setShowFixedDropBar(!showFixedDropBar);\n        if (!showFixedDropBar) setAutoHideBar(false);\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [showFixedDropBar]);\n\n  // Charger les colonnes quand une table est sélectionnée\n  useEffect(() => {\n    if (selectedTable) {\n      loadColumns(selectedTable);\n      // NE PAS reset les sélections pour permettre l'analyse multi-tables\n      // Les utilisateurs peuvent analyser des colonnes de différentes tables\n      setChartData(null); // Reset seulement le graphique\n    }\n  }, [selectedTable]);\n  const loadTables = async () => {\n    try {\n      setLoadingTables(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/schema/tables`);\n      setTables(response.data.tables || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Erreur lors du chargement des tables: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n    } finally {\n      setLoadingTables(false);\n    }\n  };\n  const loadColumns = async tableName => {\n    try {\n      setLoadingColumns(true);\n      const response = await axios.get(`${API_BASE_URL}/tables/${tableName}/columns`);\n      setColumns(response.data);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Erreur lors du chargement des colonnes: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message));\n    } finally {\n      setLoadingColumns(false);\n    }\n  };\n  const handleDragStart = event => {\n    setActiveId(event.active.id);\n  };\n  const handleDragEnd = event => {\n    const {\n      active,\n      over\n    } = event;\n    setActiveId(null);\n    if (!over) return;\n    const columnId = active.id;\n    const dropZoneId = over.id;\n\n    // Trouver la colonne correspondante\n    const allColumns = [...(columns.all_columns || []), ...(columns.numeric_columns || []), ...(columns.categorical_columns || []), ...(columns.date_columns || [])];\n    const column = allColumns.find(col => `column-${col.name}` === columnId);\n    if (!column) return;\n\n    // Ajouter la table source à la colonne\n    const columnWithTable = {\n      ...column,\n      table: selectedTable\n    };\n\n    // Assigner la colonne à la zone appropriée\n    assignColumnToAxis(dropZoneId, columnWithTable);\n  };\n\n  // Fonction pour assigner une colonne à un axe (utilisée par drag & drop et menu contextuel)\n  const assignColumnToAxis = (axisId, columnWithTable) => {\n    switch (axisId) {\n      case 'x-axis':\n      case 'x-axis-fixed':\n        setXAxis(columnWithTable);\n        break;\n      case 'y-axis':\n      case 'y-axis-fixed':\n        setYAxis(columnWithTable);\n        break;\n      case 'legend':\n      case 'legend-fixed':\n        setLegend(columnWithTable);\n        break;\n      case 'values':\n      case 'values-fixed':\n        setValues(columnWithTable);\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Fonction pour le menu contextuel\n  const handleAddToAxis = (axisId, columnWithTable) => {\n    assignColumnToAxis(axisId, columnWithTable);\n\n    // Afficher un toast de confirmation\n    const axisNames = {\n      'x-axis': 'Axe X',\n      'y-axis': 'Axe Y',\n      'legend': 'Légende',\n      'values': 'Valeurs'\n    };\n    setToast({\n      show: true,\n      message: `${columnWithTable.name} ajouté à ${axisNames[axisId]}`,\n      type: 'success'\n    });\n\n    // Masquer le toast après 3 secondes\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 3000);\n  };\n\n  // Fonctions pour sauvegarder/restaurer les configurations\n  const saveCurrentConfiguration = () => {\n    const config = {\n      id: Date.now(),\n      name: `Configuration ${new Date().toLocaleTimeString()}`,\n      table: selectedTable,\n      xAxis,\n      yAxis,\n      legend,\n      values,\n      aggFunction,\n      chartType,\n      filters,\n      timestamp: new Date().toISOString()\n    };\n    const newConfigs = [...savedConfigurations, config];\n    setSavedConfigurations(newConfigs);\n    localStorage.setItem('brainbi_saved_configs', JSON.stringify(newConfigs));\n    setToast({\n      show: true,\n      message: 'Configuration sauvegardée',\n      type: 'success'\n    });\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 3000);\n  };\n  const loadConfiguration = config => {\n    setSelectedTable(config.table);\n    setXAxis(config.xAxis);\n    setYAxis(config.yAxis);\n    setLegend(config.legend);\n    setValues(config.values);\n    setAggFunction(config.aggFunction);\n    setChartType(config.chartType);\n    setFilters(config.filters || []);\n    setToast({\n      show: true,\n      message: `Configuration \"${config.name}\" chargée`,\n      type: 'success'\n    });\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 3000);\n  };\n  const deleteConfiguration = configId => {\n    const newConfigs = savedConfigurations.filter(c => c.id !== configId);\n    setSavedConfigurations(newConfigs);\n    localStorage.setItem('brainbi_saved_configs', JSON.stringify(newConfigs));\n  };\n\n  // Fonction pour sauvegarder la visualisation dans le backend\n  const saveVisualizationToBackend = async saveData => {\n    if (!selectedTable || !xAxis && !values) {\n      showToast('Configuration incomplète pour la sauvegarde', 'error');\n      return;\n    }\n    try {\n      setSavingVisualization(true);\n      const requestData = {\n        name: saveData.name,\n        description: saveData.description,\n        chart_type: chartType,\n        table: selectedTable,\n        x_axis: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.name) || null,\n        y_axis: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.name) || null,\n        legend: (legend === null || legend === void 0 ? void 0 : legend.name) || null,\n        values: (values === null || values === void 0 ? void 0 : values.name) || null,\n        agg_function: aggFunction,\n        x_axis_table: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.table) || null,\n        y_axis_table: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.table) || null,\n        legend_table: (legend === null || legend === void 0 ? void 0 : legend.table) || null,\n        values_table: (values === null || values === void 0 ? void 0 : values.table) || null,\n        filters: filters.filter(f => f.filter_type === 'range' && (f.min_value || f.max_value) || f.filter_type !== 'range' && f.values && f.values.length > 0).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        })),\n        tags: saveData.tags || []\n      };\n      const response = await axios.post(`${API_BASE_URL}/saved-visuals`, requestData);\n      if (response.data.success) {\n        setShowSaveModal(false);\n        showToast(`Visualisation \"${saveData.name}\" sauvegardée avec succès`);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur lors de la sauvegarde:', error);\n      showToast('Erreur lors de la sauvegarde: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message), 'error');\n    } finally {\n      setSavingVisualization(false);\n    }\n  };\n\n  // Charger les configurations sauvegardées au démarrage\n  useEffect(() => {\n    const saved = localStorage.getItem('brainbi_saved_configs');\n    if (saved) {\n      try {\n        setSavedConfigurations(JSON.parse(saved));\n      } catch (error) {\n        console.error('Erreur lors du chargement des configurations:', error);\n      }\n    }\n  }, []);\n\n  // Charger une visualisation sauvegardée si spécifiée dans l'URL\n  useEffect(() => {\n    const loadVizId = searchParams.get('load');\n    if (loadVizId) {\n      // Attendre que les tables soient chargées avant de charger la visualisation\n      if (!loadingTables && tables.length > 0) {\n        loadSavedVisualization(loadVizId);\n      }\n    }\n  }, [searchParams, loadingTables, tables]);\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({\n      show: true,\n      message,\n      type\n    });\n    setTimeout(() => {\n      setToast({\n        show: false,\n        message: '',\n        type: 'success'\n      });\n    }, 4000);\n  };\n\n  // Fonction utilitaire pour extraire les messages d'erreur\n  const extractErrorMessage = error => {\n    var _error$response2, _error$response2$data;\n    let errorMsg = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message;\n\n    // Si l'erreur est un objet (erreur de validation Pydantic), la convertir en chaîne\n    if (typeof errorMsg === 'object') {\n      if (Array.isArray(errorMsg)) {\n        errorMsg = errorMsg.map(err => {\n          if (typeof err === 'object') {\n            return err.msg || `${err.type}: ${err.input}` || JSON.stringify(err);\n          }\n          return err;\n        }).join(', ');\n      } else {\n        errorMsg = errorMsg.msg || `${errorMsg.type}: ${errorMsg.input}` || JSON.stringify(errorMsg);\n      }\n    }\n    return String(errorMsg);\n  };\n\n  // Fonction pour charger une visualisation sauvegardée\n  const loadSavedVisualization = async vizId => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/saved-visuals/${vizId}`);\n      if (response.data.success) {\n        const viz = response.data.visualization;\n\n        // Restaurer la configuration\n        setSelectedTable(viz.table);\n        setChartType(viz.chart_type);\n        setAggFunction(viz.agg_function);\n\n        // Restaurer les axes (nous devons reconstruire les objets colonnes)\n        if (viz.x_axis) {\n          setXAxis({\n            name: viz.x_axis,\n            table: viz.x_axis_table || viz.table,\n            type: 'unknown' // Le type sera déterminé lors du chargement des colonnes\n          });\n        }\n        if (viz.y_axis) {\n          setYAxis({\n            name: viz.y_axis,\n            table: viz.y_axis_table || viz.table,\n            type: 'unknown'\n          });\n        }\n        if (viz.legend) {\n          setLegend({\n            name: viz.legend,\n            table: viz.legend_table || viz.table,\n            type: 'unknown'\n          });\n        }\n        if (viz.values) {\n          setValues({\n            name: viz.values,\n            table: viz.values_table || viz.table,\n            type: 'unknown'\n          });\n        }\n\n        // Restaurer les filtres\n        setFilters(viz.filters || []);\n        showToast(`Visualisation \"${viz.name}\" chargée avec succès`);\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('Erreur lors du chargement de la visualisation:', error);\n      showToast('Erreur lors du chargement: ' + (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message), 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const generateVisualization = async () => {\n    if (!selectedTable) {\n      setError('Veuillez sélectionner une table');\n      return;\n    }\n    if (!xAxis && !values) {\n      setError('Veuillez glisser au moins une colonne dans Axe X ou Valeurs');\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n      const requestData = {\n        table: selectedTable,\n        // Table principale pour compatibilité\n        x_axis: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.name) || null,\n        y_axis: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.name) || null,\n        legend: (legend === null || legend === void 0 ? void 0 : legend.name) || null,\n        values: (values === null || values === void 0 ? void 0 : values.name) || null,\n        agg_function: aggFunction,\n        chart_type: chartType,\n        // Nouvelles propriétés pour multi-tables\n        x_axis_table: (xAxis === null || xAxis === void 0 ? void 0 : xAxis.table) || null,\n        y_axis_table: (yAxis === null || yAxis === void 0 ? void 0 : yAxis.table) || null,\n        legend_table: (legend === null || legend === void 0 ? void 0 : legend.table) || null,\n        values_table: (values === null || values === void 0 ? void 0 : values.table) || null,\n        // Filtres\n        filters: filters.filter(f => f.filter_type === 'range' && (f.min_value || f.max_value) || f.filter_type !== 'range' && f.values && f.values.length > 0).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        }))\n      };\n      const response = await axios.post(`${API_BASE_URL}/drag-visual`, requestData);\n      setChartData(response.data);\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError('Erreur lors de la génération: ' + (((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const clearAll = () => {\n    setXAxis(null);\n    setYAxis(null);\n    setLegend(null);\n    setValues(null);\n    setChartData(null);\n  };\n  const renderChart = () => {\n    if (!chartData) return null;\n    const plotData = [];\n\n    // Construire le titre avec les filtres actifs\n    let title = chartData.title;\n    if (activeFiltersCount > 0) {\n      title += ` (${activeFiltersCount} filtre${activeFiltersCount > 1 ? 's' : ''} appliqué${activeFiltersCount > 1 ? 's' : ''})`;\n    }\n    const layout = {\n      title: title,\n      autosize: true,\n      height: 500,\n      margin: {\n        l: 50,\n        r: 50,\n        t: 80,\n        b: 50\n      },\n      paper_bgcolor: 'rgba(0,0,0,0)',\n      plot_bgcolor: 'rgba(0,0,0,0)',\n      font: {\n        color: '#F3F4F6'\n      }\n    };\n    switch (chartData.type) {\n      case 'bar':\n        if (chartData.legend && Array.isArray(chartData.y[0])) {\n          // Graphique avec légende\n          chartData.legend.forEach((legendItem, index) => {\n            plotData.push({\n              x: chartData.x,\n              y: chartData.y.map(row => row[index] || 0),\n              type: 'bar',\n              name: legendItem,\n              marker: {\n                color: `hsl(${index * 60 % 360}, 70%, 60%)`\n              }\n            });\n          });\n        } else {\n          plotData.push({\n            x: chartData.x,\n            y: chartData.y,\n            type: 'bar',\n            marker: {\n              color: '#a74eff'\n            }\n          });\n        }\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: {\n            color: '#a74eff',\n            width: 3\n          },\n          marker: {\n            color: '#3db5ff',\n            size: 8\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      case 'pie':\n        plotData.push({\n          labels: chartData.x,\n          values: chartData.y,\n          type: 'pie',\n          marker: {\n            colors: ['#a74eff', '#3db5ff', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b']\n          },\n          textinfo: 'label+percent',\n          textposition: 'outside'\n        });\n        break;\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: {\n            color: '#a74eff',\n            size: 10,\n            line: {\n              color: '#3db5ff',\n              width: 1\n            }\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel\n        };\n        layout.yaxis = {\n          title: chartData.ylabel\n        };\n        break;\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-400\",\n          children: [\"Type de graphique non support\\xE9: \", chartData.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 16\n        }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Plot, {\n      data: plotData,\n      layout: layout,\n      config: {\n        responsive: true,\n        displayModeBar: true\n      },\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this);\n  };\n  const canGenerate = selectedTable && (xAxis || values);\n\n  // Détecter si c'est une analyse multi-tables\n  const isMultiTable = () => {\n    const tables = new Set();\n    [xAxis, yAxis, legend, values].forEach(col => {\n      if (col !== null && col !== void 0 && col.table) tables.add(col.table);\n    });\n    return tables.size > 1;\n  };\n\n  // Compter les filtres actifs\n  const activeFiltersCount = filters.filter(f => f.filter_type === 'range' && (f.min_value || f.max_value) || f.filter_type !== 'range' && f.values && f.values.length > 0).length;\n  return /*#__PURE__*/_jsxDEV(DndContext, {\n    collisionDetection: closestCenter,\n    onDragStart: handleDragStart,\n    onDragEnd: handleDragEnd,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-950 text-gray-100\",\n      children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n        hasData: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `max-w-7xl mx-auto px-6 py-8 ${showFixedDropBar ? 'pb-32' : 'pb-8'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xl\",\n                    children: \"\\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\",\n                  children: \"Cr\\xE9ateur Drag & Drop\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-400\",\n                children: \"Glissez-d\\xE9posez vos colonnes pour cr\\xE9er des visualisations interactives\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-purple-400 mt-1\",\n                children: \"\\uD83D\\uDCA1 Jointures automatiques : analysez des colonnes de diff\\xE9rentes tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), searchParams.get('load') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 flex items-center space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-400\",\n                  children: \"\\uD83D\\uDCC2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-300\",\n                  children: \"Visualisation charg\\xE9e depuis le dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-400 mt-1\",\n                children: \"\\uD83D\\uDCD6 Narration IA : g\\xE9n\\xE9rez des histoires intelligentes \\xE0 partir de vos donn\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"\\uD83D\\uDCA1 Astuce : Utilisez Ctrl+B pour basculer la barre fixe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => {\n                  setShowFixedDropBar(!showFixedDropBar);\n                  if (!showFixedDropBar) setAutoHideBar(false);\n                },\n                icon: showFixedDropBar ? \"👁️\" : \"👁️‍🗨️\",\n                className: showFixedDropBar ? \"!border-purple-500 !text-purple-400\" : \"\",\n                children: \"Barre fixe\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => setAutoHideBar(!autoHideBar),\n                icon: autoHideBar ? \"🔄\" : \"📌\",\n                className: autoHideBar ? \"!border-blue-500 !text-blue-400\" : \"\",\n                disabled: !showFixedDropBar,\n                children: \"Auto-masquer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: saveCurrentConfiguration,\n                icon: \"\\uD83D\\uDCBE\",\n                disabled: !selectedTable || !xAxis && !values,\n                children: \"Sauvegarder (local)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: () => setShowSaveModal(true),\n                icon: \"\\uD83D\\uDD12\",\n                disabled: !selectedTable || !xAxis && !values,\n                className: \"!py-2 !px-4\",\n                children: \"Enregistrer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/dashboard'),\n                icon: \"\\uD83D\\uDCCA\",\n                children: \"Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/visual-builder'),\n                icon: \"\\uD83D\\uDCC8\",\n                children: \"Visual Builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/tables'),\n                icon: \"\\uD83D\\uDCCB\",\n                children: \"Tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/'),\n                icon: \"\\uD83C\\uDFE0\",\n                children: \"Accueil\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(DarkCard, {\n            children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n              title: \"1. S\\xE9lectionner la table\",\n              subtitle: \"Choisissez la source de donn\\xE9es\",\n              icon: \"\\uD83D\\uDDC2\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 15\n            }, this), loadingTables ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center py-4\",\n              children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-gray-400\",\n                children: \"Chargement...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedTable,\n              onChange: e => setSelectedTable(e.target.value),\n              className: \"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"-- S\\xE9lectionner une table --\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 19\n              }, this), tables.map((table, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: table.TABLE_NAME,\n                children: [table.TABLE_NAME, \" (\", table.COLUMN_COUNT, \" colonnes)\"]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), selectedTable && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 xl:grid-cols-5 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"xl:col-span-2\",\n            children: /*#__PURE__*/_jsxDEV(DarkCard, {\n              className: \"h-fit\",\n              children: loadingColumns ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-400\",\n                  children: \"Chargement des colonnes...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(ColumnsPanel, {\n                columns: columns.all_columns || [],\n                title: \"Colonnes disponibles\",\n                onAddToAxis: handleAddToAxis,\n                selectedTable: selectedTable,\n                currentAssignments: {\n                  'x-axis': xAxis,\n                  'y-axis': yAxis,\n                  'legend': legend,\n                  'values': values\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"xl:col-span-3 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(FiltersSection, {\n              filters: filters,\n              onFiltersChange: setFilters\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                  title: \"2. Configurer les axes\",\n                  subtitle: \"Glissez les colonnes dans les zones appropri\\xE9es\",\n                  icon: \"\\uD83C\\uDFAF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: () => generateNarrative({\n                      focus: null,\n                      time_period: '12_months',\n                      include_recommendations: true\n                    }),\n                    icon: \"\\uD83D\\uDCD6\",\n                    disabled: narrativeLoading,\n                    className: \"!text-blue-400 !border-blue-600 hover:!bg-blue-600/20\",\n                    children: narrativeLoading ? 'Génération...' : 'Narration rapide'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: clearAll,\n                    icon: \"\\uD83D\\uDDD1\\uFE0F\",\n                    className: \"!text-red-400 !border-red-600 hover:!bg-red-600/20\",\n                    children: \"Tout effacer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DropZonesPanel, {\n                xAxis: xAxis,\n                yAxis: yAxis,\n                legend: legend,\n                values: values,\n                onClearX: () => setXAxis(null),\n                onClearY: () => setYAxis(null),\n                onClearLegend: () => setLegend(null),\n                onClearValues: () => setValues(null)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                title: \"3. Configuration\",\n                subtitle: \"Choisissez l'agr\\xE9gation et le type de graphique\",\n                icon: \"\\u2699\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(AggregationSelector, {\n                  value: aggFunction,\n                  onChange: setAggFunction,\n                  disabled: !values && aggFunction !== 'COUNT'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(ChartTypeSelector, {\n                  value: chartType,\n                  onChange: setChartType\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6 flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(PrimaryButton, {\n                      onClick: generateVisualization,\n                      disabled: !canGenerate || loading,\n                      loading: loading,\n                      icon: loading ? null : \"🚀\",\n                      className: \"!py-3 !px-6\",\n                      children: loading ? 'Génération...' : 'Générer la visualisation'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 862,\n                      columnNumber: 25\n                    }, this), isMultiTable() && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-500 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-blue-400\",\n                        children: \"\\uD83D\\uDD17\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 874,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-blue-300 font-medium\",\n                        children: \"Analyse multi-tables\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 875,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 27\n                    }, this), activeFiltersCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2 px-3 py-2 bg-green-600/20 border border-green-500 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-400\",\n                        children: \"\\uD83E\\uDDE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 881,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-green-300 font-medium\",\n                        children: [activeFiltersCount, \" filtre\", activeFiltersCount > 1 ? 's' : '', \" actif\", activeFiltersCount > 1 ? 's' : '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 882,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this), chartData && /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                    onClick: () => setShowDebugJson(!showDebugJson),\n                    icon: \"\\uD83D\\uDD0D\",\n                    className: \"!py-3 !px-4\",\n                    children: showDebugJson ? 'Masquer JSON' : 'Voir JSON'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 21\n                }, this), !canGenerate && selectedTable && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 text-sm\",\n                  children: \"Glissez au moins une colonne dans Axe X ou Valeurs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n              children: [!chartData ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center justify-center py-16 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-4xl\",\n                    children: \"\\uD83C\\uDFAF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-semibold text-gray-100 mb-4\",\n                  children: \"Pr\\xEAt \\xE0 cr\\xE9er votre visualisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 max-w-md\",\n                  children: \"Glissez vos colonnes dans les zones appropri\\xE9es et cliquez sur \\\"G\\xE9n\\xE9rer\\\" pour cr\\xE9er votre graphique\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n                    title: \"Visualisation g\\xE9n\\xE9r\\xE9e\",\n                    subtitle: `${chartData.type.toUpperCase()} - ${chartData.data_points} points de données`,\n                    icon: \"\\uD83D\\uDCCA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 925,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                    onClick: () => setShowSaveModal(true),\n                    icon: \"\\uD83D\\uDCBE\",\n                    disabled: !selectedTable || !xAxis && !values,\n                    className: \"!py-2 !px-4\",\n                    children: \"Enregistrer ce visuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6\",\n                  children: renderChart()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 23\n                }, this), chartData.success && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-green-400 text-sm\",\n                    children: [\"\\u2705 \", chartData.message]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 25\n                }, this), showDebugJson && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-900 border border-gray-700 rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-gray-300 mb-2\",\n                    children: \"JSON de r\\xE9ponse :\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                    className: \"text-xs text-gray-400 overflow-x-auto\",\n                    children: JSON.stringify(chartData, null, 2)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 21\n              }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-red-400\",\n                  children: [\"\\u274C \", error]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 space-y-6\",\n              children: [/*#__PURE__*/_jsxDEV(NarrationSettings, {\n                onGenerate: generateNarrative,\n                loading: narrativeLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 19\n              }, this), narrativeError && /*#__PURE__*/_jsxDEV(DarkCard, {\n                className: \"border-red-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 text-red-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xl\",\n                      children: \"\\u26A0\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"font-medium\",\n                        children: \"Erreur de g\\xE9n\\xE9ration de narration\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-red-300\",\n                        children: \"La g\\xE9n\\xE9ration automatique de l'histoire des donn\\xE9es a \\xE9chou\\xE9\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 987,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-red-900/20 p-4 rounded border border-red-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"text-sm text-red-300 whitespace-pre-wrap font-mono leading-relaxed\",\n                      children: narrativeError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 994,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => generateNarrative({\n                        focus: null,\n                        time_period: '12_months',\n                        include_recommendations: true\n                      }),\n                      icon: \"\\uD83D\\uDD04\",\n                      className: \"!py-2 !px-3 !text-sm\",\n                      disabled: narrativeLoading,\n                      children: \"R\\xE9essayer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => generateNarrative({\n                        focus: null,\n                        time_period: '6_months',\n                        include_recommendations: false\n                      }),\n                      icon: \"\\u26A1\",\n                      className: \"!py-2 !px-3 !text-sm\",\n                      disabled: narrativeLoading,\n                      children: \"Mode simplifi\\xE9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1008,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => window.open('http://localhost:8000/health', '_blank'),\n                      icon: \"\\uD83D\\uDD0D\",\n                      className: \"!py-2 !px-3 !text-sm !text-blue-400 !border-blue-600\",\n                      children: \"Test backend\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => window.open('http://localhost:8000/narrate/test', '_blank'),\n                      icon: \"\\uD83E\\uDDEA\",\n                      className: \"!py-2 !px-3 !text-sm !text-green-400 !border-green-600\",\n                      children: \"Test narration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                      onClick: () => setShowNetworkDiagnostic(true),\n                      icon: \"\\uD83D\\uDD27\",\n                      className: \"!py-2 !px-3 !text-sm !text-purple-400 !border-purple-600\",\n                      children: \"Diagnostic r\\xE9seau\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 25\n                  }, this), (debugInfo === null || debugInfo === void 0 ? void 0 : debugInfo.error_type) === 'connection' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-900/20 p-3 rounded border border-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-yellow-400 mt-1\",\n                        children: \"\\uD83D\\uDCA1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1042,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-yellow-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"font-medium mb-2\",\n                          children: \"Probl\\xE8me de connexion d\\xE9tect\\xE9\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1044,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"space-y-1 text-xs\",\n                          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                            children: \"\\u2022 V\\xE9rifiez que le backend est d\\xE9marr\\xE9 sur le port 8000\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1046,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: [\"\\u2022 Testez l'URL : \", /*#__PURE__*/_jsxDEV(\"code\", {\n                              className: \"bg-yellow-800/30 px-1 rounded\",\n                              children: \"http://localhost:8000\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1047,\n                              columnNumber: 56\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1047,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            children: \"\\u2022 Consultez les logs du serveur backend\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1048,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1045,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1043,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1041,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 21\n              }, this), debugInfo && (debugInfo.status === 'error' || process.env.NODE_ENV === 'development') && /*#__PURE__*/_jsxDEV(DarkCard, {\n                className: \"border-yellow-500\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 text-yellow-400\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-lg\",\n                      children: \"\\uD83D\\uDD27\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-medium\",\n                      children: \"Informations de debug\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1064,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-yellow-900/20 p-3 rounded border border-yellow-700\",\n                    children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"text-xs text-yellow-300 whitespace-pre-wrap font-mono\",\n                      children: JSON.stringify(debugInfo, null, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1067,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 21\n              }, this), showNetworkDiagnostic && /*#__PURE__*/_jsxDEV(NetworkDiagnostic, {\n                onClose: () => setShowNetworkDiagnostic(false)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(DataStorytellingPanel, {\n                narrative: narrative,\n                dataSummary: (narrative === null || narrative === void 0 ? void 0 : narrative.data_summary) || {},\n                onCopy: copyToClipboard,\n                onExport: exportNarrative\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 7\n    }, this), toast.show && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-4 right-4 z-50 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg animate-pulse backdrop-blur-sm border border-green-500\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: toast.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1098,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FixedDropBar, {\n      xAxis: xAxis,\n      yAxis: yAxis,\n      legend: legend,\n      values: values,\n      onClearX: () => setXAxis(null),\n      onClearY: () => setYAxis(null),\n      onClearLegend: () => setLegend(null),\n      onClearValues: () => setValues(null),\n      isVisible: showFixedDropBar,\n      position: \"bottom\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DragOverlay, {\n      children: activeId ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transform rotate-3 scale-105\",\n        children: /*#__PURE__*/_jsxDEV(DraggableColumn, {\n          id: activeId,\n          column: {\n            name: 'Glissement...',\n            type: 'unknown'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 11\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SaveVisualizationModal, {\n      isOpen: showSaveModal,\n      onClose: () => setShowSaveModal(false),\n      onSave: saveVisualizationToBackend,\n      loading: savingVisualization,\n      currentConfig: {\n        chartType,\n        xAxis,\n        yAxis,\n        values,\n        legend,\n        aggFunction,\n        filters\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 647,\n    columnNumber: 5\n  }, this);\n};\n_s(DragVisualPage, \"LxPTMkthdoRsrhdVCWd1gPyNA6E=\", false, function () {\n  return [useNavigate, useSearchParams, useDataStorytelling];\n});\n_c = DragVisualPage;\nexport default DragVisualPage;\nvar _c;\n$RefreshReg$(_c, \"DragVisualPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useSearchParams", "DndContext", "DragOverlay", "closestCenter", "axios", "Plot", "BrAInBIHeader", "DarkCard", "SectionHeader", "PrimaryButton", "SecondaryButton", "Dark<PERSON><PERSON>ner", "DarkBadge", "ColumnsPanel", "DropZonesPanel", "FixedDropBar", "AggregationSelector", "ChartTypeSelector", "DraggableColumn", "FiltersSection", "NarrationSettings", "DataStorytellingPanel", "NetworkDiagnostic", "useDataStorytelling", "SaveVisualizationModal", "jsxDEV", "_jsxDEV", "API_BASE_URL", "DragVisualPage", "_s", "navigate", "searchParams", "tables", "setTables", "columns", "setColumns", "chartData", "setChartData", "selectedTable", "setSelectedTable", "xAxis", "setXAxis", "yAxis", "setYAxis", "legend", "setLegend", "values", "set<PERSON><PERSON><PERSON>", "aggFunction", "setAggFunction", "chartType", "setChartType", "loading", "setLoading", "loadingTables", "setLoadingTables", "loadingColumns", "setLoadingColumns", "error", "setError", "activeId", "setActiveId", "showDebugJson", "setShowDebugJson", "filters", "setFilters", "showNetworkDiagnostic", "setShowNetworkDiagnostic", "showFixedDropBar", "setShowFixedDropBar", "lastScrollY", "setLastScrollY", "autoHideBar", "setAutoHideBar", "toast", "setToast", "show", "message", "type", "showSaveModal", "setShowSaveModal", "savingVisualization", "setSavingVisualization", "savedConfigurations", "setSavedConfigurations", "narrative", "narrativeLoading", "narrativeError", "debugInfo", "generateNarrative", "copyToClipboard", "exportNarrative", "loadTables", "handleScroll", "currentScrollY", "window", "scrollY", "addEventListener", "passive", "removeEventListener", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "key", "preventDefault", "loadColumns", "response", "get", "data", "err", "_err$response", "_err$response$data", "detail", "tableName", "_err$response2", "_err$response2$data", "handleDragStart", "event", "active", "id", "handleDragEnd", "over", "columnId", "dropZoneId", "allColumns", "all_columns", "numeric_columns", "categorical_columns", "date_columns", "column", "find", "col", "name", "columnWithTable", "table", "assignColumnToAxis", "axisId", "handleAddToAxis", "axisNames", "setTimeout", "saveCurrentConfiguration", "config", "Date", "now", "toLocaleTimeString", "timestamp", "toISOString", "newConfigs", "localStorage", "setItem", "JSON", "stringify", "loadConfiguration", "deleteConfiguration", "configId", "filter", "c", "saveVisualizationToBackend", "saveData", "showToast", "requestData", "description", "chart_type", "x_axis", "y_axis", "agg_function", "x_axis_table", "y_axis_table", "legend_table", "values_table", "f", "filter_type", "min_value", "max_value", "length", "map", "column_name", "table_name", "tags", "post", "success", "_error$response", "_error$response$data", "console", "saved", "getItem", "parse", "loadVizId", "loadSavedVisualization", "extractErrorMessage", "_error$response2", "_error$response2$data", "errorMsg", "Array", "isArray", "msg", "input", "join", "String", "vizId", "viz", "visualization", "_error$response3", "_error$response3$data", "generateVisualization", "_err$response3", "_err$response3$data", "clearAll", "<PERSON><PERSON><PERSON>", "plotData", "title", "activeFiltersCount", "layout", "autosize", "height", "margin", "l", "r", "t", "b", "paper_bgcolor", "plot_bgcolor", "font", "color", "y", "for<PERSON>ach", "legendItem", "index", "push", "x", "row", "marker", "xaxis", "xlabel", "yaxis", "ylabel", "mode", "line", "width", "size", "labels", "colors", "textinfo", "textposition", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "responsive", "displayModeBar", "style", "canGenerate", "isMultiTable", "Set", "add", "collisionDetection", "onDragStart", "onDragEnd", "hasData", "onClick", "icon", "disabled", "subtitle", "value", "onChange", "target", "TABLE_NAME", "COLUMN_COUNT", "onAddToAxis", "currentAssignments", "onFiltersChange", "focus", "time_period", "include_recommendations", "onClearX", "onClearY", "onClearLegend", "onClearValues", "toUpperCase", "data_points", "onGenerate", "open", "error_type", "status", "process", "env", "NODE_ENV", "onClose", "dataSummary", "data_summary", "onCopy", "onExport", "isVisible", "position", "isOpen", "onSave", "currentConfig", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/components/DragVisualPage.js"], "sourcesContent": ["/**\n * DragVisualPage - Interface drag & drop pour créer des visualisations\n * Permet de glisser-déposer des colonnes pour construire des graphiques\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport {\n  BrAInBIHeader,\n  DarkCard,\n  SectionHeader,\n  PrimaryButton,\n  SecondaryButton,\n  DarkSpinner,\n  DarkBadge\n} from './YellowMindUI';\nimport {\n  ColumnsPanel,\n  DropZonesPanel,\n  FixedDropBar,\n  AggregationSelector,\n  ChartTypeSelector,\n  DraggableColumn\n} from './DragDropComponents';\nimport { FiltersSection } from './FilterComponents';\nimport {\n  NarrationSettings,\n  DataStorytellingPanel,\n  NetworkDiagnostic,\n  useDataStorytelling\n} from './DataStorytellingComponents';\nimport SaveVisualizationModal from './SaveVisualizationModal';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\nconst DragVisualPage = () => {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  \n  // États pour les données\n  const [tables, setTables] = useState([]);\n  const [columns, setColumns] = useState({});\n  const [chartData, setChartData] = useState(null);\n  \n  // États pour les sélections\n  const [selectedTable, setSelectedTable] = useState('');\n  const [xAxis, setXAxis] = useState(null);\n  const [yAxis, setYAxis] = useState(null);\n  const [legend, setLegend] = useState(null);\n  const [values, setValues] = useState(null);\n  const [aggFunction, setAggFunction] = useState('SUM');\n  const [chartType, setChartType] = useState('bar');\n  \n  // États pour l'interface\n  const [loading, setLoading] = useState(false);\n  const [loadingTables, setLoadingTables] = useState(true);\n  const [loadingColumns, setLoadingColumns] = useState(false);\n  const [error, setError] = useState('');\n  const [activeId, setActiveId] = useState(null);\n  const [showDebugJson, setShowDebugJson] = useState(false);\n  const [filters, setFilters] = useState([]);\n  const [showNetworkDiagnostic, setShowNetworkDiagnostic] = useState(false);\n  const [showFixedDropBar, setShowFixedDropBar] = useState(true);\n  const [lastScrollY, setLastScrollY] = useState(0);\n  const [autoHideBar, setAutoHideBar] = useState(false);\n  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });\n\n  // États pour la sauvegarde des visualisations\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const [savingVisualization, setSavingVisualization] = useState(false);\n  const [savedConfigurations, setSavedConfigurations] = useState([]);\n\n  // Hook pour la narration intelligente\n  const {\n    narrative,\n    loading: narrativeLoading,\n    error: narrativeError,\n    debugInfo,\n    generateNarrative,\n    copyToClipboard,\n    exportNarrative\n  } = useDataStorytelling();\n\n  // Charger les tables au démarrage\n  useEffect(() => {\n    loadTables();\n  }, []);\n\n  // Gestion du scroll pour auto-masquer la barre fixe\n  useEffect(() => {\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n\n      if (autoHideBar) {\n        if (currentScrollY > lastScrollY && currentScrollY > 100) {\n          // Scroll vers le bas - masquer la barre\n          setShowFixedDropBar(false);\n        } else if (currentScrollY < lastScrollY) {\n          // Scroll vers le haut - montrer la barre\n          setShowFixedDropBar(true);\n        }\n      }\n\n      setLastScrollY(currentScrollY);\n    };\n\n    if (autoHideBar) {\n      window.addEventListener('scroll', handleScroll, { passive: true });\n      return () => window.removeEventListener('scroll', handleScroll);\n    }\n  }, [lastScrollY, autoHideBar]);\n\n  // Raccourci clavier pour basculer la barre fixe (Ctrl/Cmd + B)\n  useEffect(() => {\n    const handleKeyDown = (e) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {\n        e.preventDefault();\n        setShowFixedDropBar(!showFixedDropBar);\n        if (!showFixedDropBar) setAutoHideBar(false);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [showFixedDropBar]);\n\n  // Charger les colonnes quand une table est sélectionnée\n  useEffect(() => {\n    if (selectedTable) {\n      loadColumns(selectedTable);\n      // NE PAS reset les sélections pour permettre l'analyse multi-tables\n      // Les utilisateurs peuvent analyser des colonnes de différentes tables\n      setChartData(null); // Reset seulement le graphique\n    }\n  }, [selectedTable]);\n\n  const loadTables = async () => {\n    try {\n      setLoadingTables(true);\n      setError('');\n      const response = await axios.get(`${API_BASE_URL}/schema/tables`);\n      setTables(response.data.tables || []);\n    } catch (err) {\n      setError('Erreur lors du chargement des tables: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoadingTables(false);\n    }\n  };\n\n  const loadColumns = async (tableName) => {\n    try {\n      setLoadingColumns(true);\n      const response = await axios.get(`${API_BASE_URL}/tables/${tableName}/columns`);\n      setColumns(response.data);\n    } catch (err) {\n      setError('Erreur lors du chargement des colonnes: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoadingColumns(false);\n    }\n  };\n\n  const handleDragStart = (event) => {\n    setActiveId(event.active.id);\n  };\n\n  const handleDragEnd = (event) => {\n    const { active, over } = event;\n    setActiveId(null);\n\n    if (!over) return;\n\n    const columnId = active.id;\n    const dropZoneId = over.id;\n\n    // Trouver la colonne correspondante\n    const allColumns = [\n      ...(columns.all_columns || []),\n      ...(columns.numeric_columns || []),\n      ...(columns.categorical_columns || []),\n      ...(columns.date_columns || [])\n    ];\n\n    const column = allColumns.find(col => `column-${col.name}` === columnId);\n    if (!column) return;\n\n    // Ajouter la table source à la colonne\n    const columnWithTable = { ...column, table: selectedTable };\n\n    // Assigner la colonne à la zone appropriée\n    assignColumnToAxis(dropZoneId, columnWithTable);\n  };\n\n  // Fonction pour assigner une colonne à un axe (utilisée par drag & drop et menu contextuel)\n  const assignColumnToAxis = (axisId, columnWithTable) => {\n    switch (axisId) {\n      case 'x-axis':\n      case 'x-axis-fixed':\n        setXAxis(columnWithTable);\n        break;\n      case 'y-axis':\n      case 'y-axis-fixed':\n        setYAxis(columnWithTable);\n        break;\n      case 'legend':\n      case 'legend-fixed':\n        setLegend(columnWithTable);\n        break;\n      case 'values':\n      case 'values-fixed':\n        setValues(columnWithTable);\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Fonction pour le menu contextuel\n  const handleAddToAxis = (axisId, columnWithTable) => {\n    assignColumnToAxis(axisId, columnWithTable);\n\n    // Afficher un toast de confirmation\n    const axisNames = {\n      'x-axis': 'Axe X',\n      'y-axis': 'Axe Y',\n      'legend': 'Légende',\n      'values': 'Valeurs'\n    };\n\n    setToast({\n      show: true,\n      message: `${columnWithTable.name} ajouté à ${axisNames[axisId]}`,\n      type: 'success'\n    });\n\n    // Masquer le toast après 3 secondes\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 3000);\n  };\n\n  // Fonctions pour sauvegarder/restaurer les configurations\n  const saveCurrentConfiguration = () => {\n    const config = {\n      id: Date.now(),\n      name: `Configuration ${new Date().toLocaleTimeString()}`,\n      table: selectedTable,\n      xAxis,\n      yAxis,\n      legend,\n      values,\n      aggFunction,\n      chartType,\n      filters,\n      timestamp: new Date().toISOString()\n    };\n\n    const newConfigs = [...savedConfigurations, config];\n    setSavedConfigurations(newConfigs);\n    localStorage.setItem('brainbi_saved_configs', JSON.stringify(newConfigs));\n\n    setToast({\n      show: true,\n      message: 'Configuration sauvegardée',\n      type: 'success'\n    });\n\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 3000);\n  };\n\n  const loadConfiguration = (config) => {\n    setSelectedTable(config.table);\n    setXAxis(config.xAxis);\n    setYAxis(config.yAxis);\n    setLegend(config.legend);\n    setValues(config.values);\n    setAggFunction(config.aggFunction);\n    setChartType(config.chartType);\n    setFilters(config.filters || []);\n\n    setToast({\n      show: true,\n      message: `Configuration \"${config.name}\" chargée`,\n      type: 'success'\n    });\n\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 3000);\n  };\n\n  const deleteConfiguration = (configId) => {\n    const newConfigs = savedConfigurations.filter(c => c.id !== configId);\n    setSavedConfigurations(newConfigs);\n    localStorage.setItem('brainbi_saved_configs', JSON.stringify(newConfigs));\n  };\n\n  // Fonction pour sauvegarder la visualisation dans le backend\n  const saveVisualizationToBackend = async (saveData) => {\n    if (!selectedTable || (!xAxis && !values)) {\n      showToast('Configuration incomplète pour la sauvegarde', 'error');\n      return;\n    }\n\n    try {\n      setSavingVisualization(true);\n\n      const requestData = {\n        name: saveData.name,\n        description: saveData.description,\n        chart_type: chartType,\n        table: selectedTable,\n        x_axis: xAxis?.name || null,\n        y_axis: yAxis?.name || null,\n        legend: legend?.name || null,\n        values: values?.name || null,\n        agg_function: aggFunction,\n        x_axis_table: xAxis?.table || null,\n        y_axis_table: yAxis?.table || null,\n        legend_table: legend?.table || null,\n        values_table: values?.table || null,\n        filters: filters.filter(f =>\n          (f.filter_type === 'range' && (f.min_value || f.max_value)) ||\n          (f.filter_type !== 'range' && f.values && f.values.length > 0)\n        ).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        })),\n        tags: saveData.tags || []\n      };\n\n      const response = await axios.post(`${API_BASE_URL}/saved-visuals`, requestData);\n\n      if (response.data.success) {\n        setShowSaveModal(false);\n        showToast(`Visualisation \"${saveData.name}\" sauvegardée avec succès`);\n      }\n    } catch (error) {\n      console.error('Erreur lors de la sauvegarde:', error);\n      showToast('Erreur lors de la sauvegarde: ' + (error.response?.data?.detail || error.message), 'error');\n    } finally {\n      setSavingVisualization(false);\n    }\n  };\n\n  // Charger les configurations sauvegardées au démarrage\n  useEffect(() => {\n    const saved = localStorage.getItem('brainbi_saved_configs');\n    if (saved) {\n      try {\n        setSavedConfigurations(JSON.parse(saved));\n      } catch (error) {\n        console.error('Erreur lors du chargement des configurations:', error);\n      }\n    }\n  }, []);\n\n  // Charger une visualisation sauvegardée si spécifiée dans l'URL\n  useEffect(() => {\n    const loadVizId = searchParams.get('load');\n    if (loadVizId) {\n      // Attendre que les tables soient chargées avant de charger la visualisation\n      if (!loadingTables && tables.length > 0) {\n        loadSavedVisualization(loadVizId);\n      }\n    }\n  }, [searchParams, loadingTables, tables]);\n\n  // Fonction utilitaire pour afficher les toasts\n  const showToast = (message, type = 'success') => {\n    setToast({ show: true, message, type });\n    setTimeout(() => {\n      setToast({ show: false, message: '', type: 'success' });\n    }, 4000);\n  };\n\n  // Fonction utilitaire pour extraire les messages d'erreur\n  const extractErrorMessage = (error) => {\n    let errorMsg = error.response?.data?.detail || error.message;\n\n    // Si l'erreur est un objet (erreur de validation Pydantic), la convertir en chaîne\n    if (typeof errorMsg === 'object') {\n      if (Array.isArray(errorMsg)) {\n        errorMsg = errorMsg.map(err => {\n          if (typeof err === 'object') {\n            return err.msg || `${err.type}: ${err.input}` || JSON.stringify(err);\n          }\n          return err;\n        }).join(', ');\n      } else {\n        errorMsg = errorMsg.msg || `${errorMsg.type}: ${errorMsg.input}` || JSON.stringify(errorMsg);\n      }\n    }\n\n    return String(errorMsg);\n  };\n\n  // Fonction pour charger une visualisation sauvegardée\n  const loadSavedVisualization = async (vizId) => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/saved-visuals/${vizId}`);\n\n      if (response.data.success) {\n        const viz = response.data.visualization;\n\n        // Restaurer la configuration\n        setSelectedTable(viz.table);\n        setChartType(viz.chart_type);\n        setAggFunction(viz.agg_function);\n\n        // Restaurer les axes (nous devons reconstruire les objets colonnes)\n        if (viz.x_axis) {\n          setXAxis({\n            name: viz.x_axis,\n            table: viz.x_axis_table || viz.table,\n            type: 'unknown' // Le type sera déterminé lors du chargement des colonnes\n          });\n        }\n\n        if (viz.y_axis) {\n          setYAxis({\n            name: viz.y_axis,\n            table: viz.y_axis_table || viz.table,\n            type: 'unknown'\n          });\n        }\n\n        if (viz.legend) {\n          setLegend({\n            name: viz.legend,\n            table: viz.legend_table || viz.table,\n            type: 'unknown'\n          });\n        }\n\n        if (viz.values) {\n          setValues({\n            name: viz.values,\n            table: viz.values_table || viz.table,\n            type: 'unknown'\n          });\n        }\n\n        // Restaurer les filtres\n        setFilters(viz.filters || []);\n\n        showToast(`Visualisation \"${viz.name}\" chargée avec succès`);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement de la visualisation:', error);\n      showToast('Erreur lors du chargement: ' + (error.response?.data?.detail || error.message), 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generateVisualization = async () => {\n    if (!selectedTable) {\n      setError('Veuillez sélectionner une table');\n      return;\n    }\n\n    if (!xAxis && !values) {\n      setError('Veuillez glisser au moins une colonne dans Axe X ou Valeurs');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      \n      const requestData = {\n        table: selectedTable, // Table principale pour compatibilité\n        x_axis: xAxis?.name || null,\n        y_axis: yAxis?.name || null,\n        legend: legend?.name || null,\n        values: values?.name || null,\n        agg_function: aggFunction,\n        chart_type: chartType,\n        // Nouvelles propriétés pour multi-tables\n        x_axis_table: xAxis?.table || null,\n        y_axis_table: yAxis?.table || null,\n        legend_table: legend?.table || null,\n        values_table: values?.table || null,\n        // Filtres\n        filters: filters.filter(f =>\n          (f.filter_type === 'range' && (f.min_value || f.max_value)) ||\n          (f.filter_type !== 'range' && f.values && f.values.length > 0)\n        ).map(f => ({\n          column_name: f.column_name,\n          table_name: f.table_name,\n          filter_type: f.filter_type,\n          values: f.values || [],\n          min_value: f.min_value || null,\n          max_value: f.max_value || null\n        }))\n      };\n\n      const response = await axios.post(`${API_BASE_URL}/drag-visual`, requestData);\n      setChartData(response.data);\n    } catch (err) {\n      setError('Erreur lors de la génération: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const clearAll = () => {\n    setXAxis(null);\n    setYAxis(null);\n    setLegend(null);\n    setValues(null);\n    setChartData(null);\n  };\n\n  const renderChart = () => {\n    if (!chartData) return null;\n\n    const plotData = [];\n\n    // Construire le titre avec les filtres actifs\n    let title = chartData.title;\n    if (activeFiltersCount > 0) {\n      title += ` (${activeFiltersCount} filtre${activeFiltersCount > 1 ? 's' : ''} appliqué${activeFiltersCount > 1 ? 's' : ''})`;\n    }\n\n    const layout = {\n      title: title,\n      autosize: true,\n      height: 500,\n      margin: { l: 50, r: 50, t: 80, b: 50 },\n      paper_bgcolor: 'rgba(0,0,0,0)',\n      plot_bgcolor: 'rgba(0,0,0,0)',\n      font: { color: '#F3F4F6' }\n    };\n\n    switch (chartData.type) {\n      case 'bar':\n        if (chartData.legend && Array.isArray(chartData.y[0])) {\n          // Graphique avec légende\n          chartData.legend.forEach((legendItem, index) => {\n            plotData.push({\n              x: chartData.x,\n              y: chartData.y.map(row => row[index] || 0),\n              type: 'bar',\n              name: legendItem,\n              marker: { \n                color: `hsl(${(index * 60) % 360}, 70%, 60%)`\n              }\n            });\n          });\n        } else {\n          plotData.push({\n            x: chartData.x,\n            y: chartData.y,\n            type: 'bar',\n            marker: { color: '#a74eff' }\n          });\n        }\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: { color: '#a74eff', width: 3 },\n          marker: { color: '#3db5ff', size: 8 }\n        });\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      case 'pie':\n        plotData.push({\n          labels: chartData.x,\n          values: chartData.y,\n          type: 'pie',\n          marker: { \n            colors: ['#a74eff', '#3db5ff', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#43e97b']\n          },\n          textinfo: 'label+percent',\n          textposition: 'outside'\n        });\n        break;\n\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: { \n            color: '#a74eff',\n            size: 10,\n            line: { color: '#3db5ff', width: 1 }\n          }\n        });\n        layout.xaxis = { title: chartData.xlabel };\n        layout.yaxis = { title: chartData.ylabel };\n        break;\n\n      default:\n        return <p className=\"text-red-400\">Type de graphique non supporté: {chartData.type}</p>;\n    }\n\n    return (\n      <Plot\n        data={plotData}\n        layout={layout}\n        config={{ responsive: true, displayModeBar: true }}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n  const canGenerate = selectedTable && (xAxis || values);\n\n  // Détecter si c'est une analyse multi-tables\n  const isMultiTable = () => {\n    const tables = new Set();\n    [xAxis, yAxis, legend, values].forEach(col => {\n      if (col?.table) tables.add(col.table);\n    });\n    return tables.size > 1;\n  };\n\n  // Compter les filtres actifs\n  const activeFiltersCount = filters.filter(f =>\n    (f.filter_type === 'range' && (f.min_value || f.max_value)) ||\n    (f.filter_type !== 'range' && f.values && f.values.length > 0)\n  ).length;\n\n  return (\n    <DndContext\n      collisionDetection={closestCenter}\n      onDragStart={handleDragStart}\n      onDragEnd={handleDragEnd}\n    >\n      <div className=\"min-h-screen bg-gray-950 text-gray-100\">\n        <BrAInBIHeader hasData={true} />\n        \n        <div className={`max-w-7xl mx-auto px-6 py-8 ${showFixedDropBar ? 'pb-32' : 'pb-8'}`}>\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <div className=\"flex items-center space-x-3 mb-2\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center\">\n                    <span className=\"text-xl\">🎯</span>\n                  </div>\n                  <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                    Créateur Drag & Drop\n                  </h1>\n                </div>\n                <p className=\"text-gray-400\">\n                  Glissez-déposez vos colonnes pour créer des visualisations interactives\n                </p>\n                <p className=\"text-sm text-purple-400 mt-1\">\n                  💡 Jointures automatiques : analysez des colonnes de différentes tables\n                </p>\n                {searchParams.get('load') && (\n                  <div className=\"mt-2 flex items-center space-x-2 text-sm\">\n                    <span className=\"text-blue-400\">📂</span>\n                    <span className=\"text-blue-300\">Visualisation chargée depuis le dashboard</span>\n                  </div>\n                )}\n                <p className=\"text-sm text-blue-400 mt-1\">\n                  📖 Narration IA : générez des histoires intelligentes à partir de vos données\n                </p>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  💡 Astuce : Utilisez Ctrl+B pour basculer la barre fixe\n                </p>\n              </div>\n              <div className=\"flex space-x-3\">\n                <SecondaryButton\n                  onClick={() => {\n                    setShowFixedDropBar(!showFixedDropBar);\n                    if (!showFixedDropBar) setAutoHideBar(false);\n                  }}\n                  icon={showFixedDropBar ? \"👁️\" : \"👁️‍🗨️\"}\n                  className={showFixedDropBar ? \"!border-purple-500 !text-purple-400\" : \"\"}\n                >\n                  Barre fixe\n                </SecondaryButton>\n                <SecondaryButton\n                  onClick={() => setAutoHideBar(!autoHideBar)}\n                  icon={autoHideBar ? \"🔄\" : \"📌\"}\n                  className={autoHideBar ? \"!border-blue-500 !text-blue-400\" : \"\"}\n                  disabled={!showFixedDropBar}\n                >\n                  Auto-masquer\n                </SecondaryButton>\n                <SecondaryButton\n                  onClick={saveCurrentConfiguration}\n                  icon=\"💾\"\n                  disabled={!selectedTable || (!xAxis && !values)}\n                >\n                  Sauvegarder (local)\n                </SecondaryButton>\n                <PrimaryButton\n                  onClick={() => setShowSaveModal(true)}\n                  icon=\"🔒\"\n                  disabled={!selectedTable || (!xAxis && !values)}\n                  className=\"!py-2 !px-4\"\n                >\n                  Enregistrer\n                </PrimaryButton>\n                <SecondaryButton onClick={() => navigate('/dashboard')} icon=\"📊\">\n                  Dashboard\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/visual-builder')} icon=\"📈\">\n                  Visual Builder\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/tables')} icon=\"📋\">\n                  Tables\n                </SecondaryButton>\n                <SecondaryButton onClick={() => navigate('/')} icon=\"🏠\">\n                  Accueil\n                </SecondaryButton>\n              </div>\n            </div>\n          </div>\n\n          {/* Sélection de table */}\n          <div className=\"mb-8\">\n            <DarkCard>\n              <SectionHeader\n                title=\"1. Sélectionner la table\"\n                subtitle=\"Choisissez la source de données\"\n                icon=\"🗂️\"\n              />\n              \n              {loadingTables ? (\n                <div className=\"flex items-center justify-center py-4\">\n                  <DarkSpinner />\n                  <span className=\"ml-2 text-gray-400\">Chargement...</span>\n                </div>\n              ) : (\n                <select\n                  value={selectedTable}\n                  onChange={(e) => setSelectedTable(e.target.value)}\n                  className=\"w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-gray-100 focus:border-purple-500 focus:outline-none\"\n                >\n                  <option value=\"\">-- Sélectionner une table --</option>\n                  {tables.map((table, index) => (\n                    <option key={index} value={table.TABLE_NAME}>\n                      {table.TABLE_NAME} ({table.COLUMN_COUNT} colonnes)\n                    </option>\n                  ))}\n                </select>\n              )}\n            </DarkCard>\n          </div>\n\n          {selectedTable && (\n            <div className=\"grid grid-cols-1 xl:grid-cols-5 gap-8\">\n              {/* Panneau des colonnes - Plus large */}\n              <div className=\"xl:col-span-2\">\n                <DarkCard className=\"h-fit\">\n                  {loadingColumns ? (\n                    <div className=\"flex items-center justify-center py-8\">\n                      <DarkSpinner />\n                      <span className=\"ml-2 text-gray-400\">Chargement des colonnes...</span>\n                    </div>\n                  ) : (\n                    <ColumnsPanel\n                      columns={columns.all_columns || []}\n                      title=\"Colonnes disponibles\"\n                      onAddToAxis={handleAddToAxis}\n                      selectedTable={selectedTable}\n                      currentAssignments={{\n                        'x-axis': xAxis,\n                        'y-axis': yAxis,\n                        'legend': legend,\n                        'values': values\n                      }}\n                    />\n                  )}\n                </DarkCard>\n              </div>\n\n              {/* Zone principale - Plus compacte */}\n              <div className=\"xl:col-span-3 space-y-6\">\n                {/* Section des filtres */}\n                <FiltersSection\n                  filters={filters}\n                  onFiltersChange={setFilters}\n                />\n\n                {/* Zones de drop */}\n                <DarkCard>\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <SectionHeader\n                      title=\"2. Configurer les axes\"\n                      subtitle=\"Glissez les colonnes dans les zones appropriées\"\n                      icon=\"🎯\"\n                    />\n                    <div className=\"flex space-x-3\">\n                      <SecondaryButton\n                        onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}\n                        icon=\"📖\"\n                        disabled={narrativeLoading}\n                        className=\"!text-blue-400 !border-blue-600 hover:!bg-blue-600/20\"\n                      >\n                        {narrativeLoading ? 'Génération...' : 'Narration rapide'}\n                      </SecondaryButton>\n                      <SecondaryButton onClick={clearAll} icon=\"🗑️\" className=\"!text-red-400 !border-red-600 hover:!bg-red-600/20\">\n                        Tout effacer\n                      </SecondaryButton>\n                    </div>\n                  </div>\n                  \n                  <DropZonesPanel\n                    xAxis={xAxis}\n                    yAxis={yAxis}\n                    legend={legend}\n                    values={values}\n                    onClearX={() => setXAxis(null)}\n                    onClearY={() => setYAxis(null)}\n                    onClearLegend={() => setLegend(null)}\n                    onClearValues={() => setValues(null)}\n                  />\n                </DarkCard>\n\n                {/* Configuration */}\n                <DarkCard>\n                  <SectionHeader\n                    title=\"3. Configuration\"\n                    subtitle=\"Choisissez l'agrégation et le type de graphique\"\n                    icon=\"⚙️\"\n                  />\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <AggregationSelector\n                      value={aggFunction}\n                      onChange={setAggFunction}\n                      disabled={!values && aggFunction !== 'COUNT'}\n                    />\n                    \n                    <ChartTypeSelector\n                      value={chartType}\n                      onChange={setChartType}\n                    />\n                  </div>\n                  \n                  <div className=\"mt-6 flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <PrimaryButton\n                          onClick={generateVisualization}\n                          disabled={!canGenerate || loading}\n                          loading={loading}\n                          icon={loading ? null : \"🚀\"}\n                          className=\"!py-3 !px-6\"\n                        >\n                          {loading ? 'Génération...' : 'Générer la visualisation'}\n                        </PrimaryButton>\n\n                        {isMultiTable() && (\n                          <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-600/20 border border-blue-500 rounded-lg\">\n                            <span className=\"text-blue-400\">🔗</span>\n                            <span className=\"text-sm text-blue-300 font-medium\">Analyse multi-tables</span>\n                          </div>\n                        )}\n\n                        {activeFiltersCount > 0 && (\n                          <div className=\"flex items-center space-x-2 px-3 py-2 bg-green-600/20 border border-green-500 rounded-lg\">\n                            <span className=\"text-green-400\">🧩</span>\n                            <span className=\"text-sm text-green-300 font-medium\">\n                              {activeFiltersCount} filtre{activeFiltersCount > 1 ? 's' : ''} actif{activeFiltersCount > 1 ? 's' : ''}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                      \n                      {chartData && (\n                        <SecondaryButton\n                          onClick={() => setShowDebugJson(!showDebugJson)}\n                          icon=\"🔍\"\n                          className=\"!py-3 !px-4\"\n                        >\n                          {showDebugJson ? 'Masquer JSON' : 'Voir JSON'}\n                        </SecondaryButton>\n                      )}\n                    </div>\n                    \n                    {!canGenerate && selectedTable && (\n                      <p className=\"text-gray-500 text-sm\">\n                        Glissez au moins une colonne dans Axe X ou Valeurs\n                      </p>\n                    )}\n                  </div>\n                </DarkCard>\n\n                {/* Affichage du graphique */}\n                <DarkCard>\n                  {!chartData ? (\n                    <div className=\"flex flex-col items-center justify-center py-16 text-center\">\n                      <div className=\"w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 flex items-center justify-center\">\n                        <span className=\"text-4xl\">🎯</span>\n                      </div>\n                      <h3 className=\"text-xl font-semibold text-gray-100 mb-4\">\n                        Prêt à créer votre visualisation\n                      </h3>\n                      <p className=\"text-gray-400 max-w-md\">\n                        Glissez vos colonnes dans les zones appropriées et cliquez sur \"Générer\" pour créer votre graphique\n                      </p>\n                    </div>\n                  ) : (\n                    <div>\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <SectionHeader\n                          title=\"Visualisation générée\"\n                          subtitle={`${chartData.type.toUpperCase()} - ${chartData.data_points} points de données`}\n                          icon=\"📊\"\n                        />\n                        <PrimaryButton\n                          onClick={() => setShowSaveModal(true)}\n                          icon=\"💾\"\n                          disabled={!selectedTable || (!xAxis && !values)}\n                          className=\"!py-2 !px-4\"\n                        >\n                          Enregistrer ce visuel\n                        </PrimaryButton>\n                      </div>\n                      \n                      <div className=\"mt-6\">\n                        {renderChart()}\n                      </div>\n                      \n                      {chartData.success && (\n                        <div className=\"mt-4 p-3 bg-green-900/20 border border-green-700 rounded-lg\">\n                          <p className=\"text-green-400 text-sm\">\n                            ✅ {chartData.message}\n                          </p>\n                        </div>\n                      )}\n                      \n                      {/* Debug JSON */}\n                      {showDebugJson && (\n                        <div className=\"mt-4 p-4 bg-gray-900 border border-gray-700 rounded-lg\">\n                          <h4 className=\"text-sm font-medium text-gray-300 mb-2\">JSON de réponse :</h4>\n                          <pre className=\"text-xs text-gray-400 overflow-x-auto\">\n                            {JSON.stringify(chartData, null, 2)}\n                          </pre>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                  \n                  {error && (\n                    <div className=\"mt-4 p-4 bg-red-900/20 border border-red-700 rounded-lg\">\n                      <p className=\"text-red-400\">\n                        ❌ {error}\n                      </p>\n                    </div>\n                  )}\n                </DarkCard>\n\n                {/* Section Narration intelligente */}\n                <div className=\"mt-8 space-y-6\">\n                  <NarrationSettings\n                    onGenerate={generateNarrative}\n                    loading={narrativeLoading}\n                  />\n\n                  {narrativeError && (\n                    <DarkCard className=\"border-red-500\">\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center space-x-3 text-red-400\">\n                          <span className=\"text-xl\">⚠️</span>\n                          <div>\n                            <p className=\"font-medium\">Erreur de génération de narration</p>\n                            <p className=\"text-sm text-red-300\">\n                              La génération automatique de l'histoire des données a échoué\n                            </p>\n                          </div>\n                        </div>\n\n                        <div className=\"bg-red-900/20 p-4 rounded border border-red-700\">\n                          <pre className=\"text-sm text-red-300 whitespace-pre-wrap font-mono leading-relaxed\">\n                            {narrativeError}\n                          </pre>\n                        </div>\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-3\">\n                          <SecondaryButton\n                            onClick={() => generateNarrative({ focus: null, time_period: '12_months', include_recommendations: true })}\n                            icon=\"🔄\"\n                            className=\"!py-2 !px-3 !text-sm\"\n                            disabled={narrativeLoading}\n                          >\n                            Réessayer\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => generateNarrative({ focus: null, time_period: '6_months', include_recommendations: false })}\n                            icon=\"⚡\"\n                            className=\"!py-2 !px-3 !text-sm\"\n                            disabled={narrativeLoading}\n                          >\n                            Mode simplifié\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => window.open('http://localhost:8000/health', '_blank')}\n                            icon=\"🔍\"\n                            className=\"!py-2 !px-3 !text-sm !text-blue-400 !border-blue-600\"\n                          >\n                            Test backend\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => window.open('http://localhost:8000/narrate/test', '_blank')}\n                            icon=\"🧪\"\n                            className=\"!py-2 !px-3 !text-sm !text-green-400 !border-green-600\"\n                          >\n                            Test narration\n                          </SecondaryButton>\n                          <SecondaryButton\n                            onClick={() => setShowNetworkDiagnostic(true)}\n                            icon=\"🔧\"\n                            className=\"!py-2 !px-3 !text-sm !text-purple-400 !border-purple-600\"\n                          >\n                            Diagnostic réseau\n                          </SecondaryButton>\n                        </div>\n\n                        {debugInfo?.error_type === 'connection' && (\n                          <div className=\"bg-yellow-900/20 p-3 rounded border border-yellow-700\">\n                            <div className=\"flex items-start space-x-2\">\n                              <span className=\"text-yellow-400 mt-1\">💡</span>\n                              <div className=\"text-sm text-yellow-300\">\n                                <p className=\"font-medium mb-2\">Problème de connexion détecté</p>\n                                <ul className=\"space-y-1 text-xs\">\n                                  <li>• Vérifiez que le backend est démarré sur le port 8000</li>\n                                  <li>• Testez l'URL : <code className=\"bg-yellow-800/30 px-1 rounded\">http://localhost:8000</code></li>\n                                  <li>• Consultez les logs du serveur backend</li>\n                                </ul>\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </DarkCard>\n                  )}\n\n                  {/* Debug info pour diagnostiquer les problèmes */}\n                  {debugInfo && (debugInfo.status === 'error' || process.env.NODE_ENV === 'development') && (\n                    <DarkCard className=\"border-yellow-500\">\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center space-x-3 text-yellow-400\">\n                          <span className=\"text-lg\">🔧</span>\n                          <h4 className=\"font-medium\">Informations de debug</h4>\n                        </div>\n\n                        <div className=\"bg-yellow-900/20 p-3 rounded border border-yellow-700\">\n                          <pre className=\"text-xs text-yellow-300 whitespace-pre-wrap font-mono\">\n                            {JSON.stringify(debugInfo, null, 2)}\n                          </pre>\n                        </div>\n                      </div>\n                    </DarkCard>\n                  )}\n\n                  {/* Diagnostic réseau */}\n                  {showNetworkDiagnostic && (\n                    <NetworkDiagnostic\n                      onClose={() => setShowNetworkDiagnostic(false)}\n                    />\n                  )}\n\n                  <DataStorytellingPanel\n                    narrative={narrative}\n                    dataSummary={narrative?.data_summary || {}}\n                    onCopy={copyToClipboard}\n                    onExport={exportNarrative}\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Toast de notification */}\n      {toast.show && (\n        <div className=\"fixed top-4 right-4 z-50 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg\n                        animate-pulse backdrop-blur-sm border border-green-500\">\n          <div className=\"flex items-center space-x-2\">\n            <span>✅</span>\n            <span className=\"text-sm font-medium\">{toast.message}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Barre fixe avec zones de drop */}\n      <FixedDropBar\n        xAxis={xAxis}\n        yAxis={yAxis}\n        legend={legend}\n        values={values}\n        onClearX={() => setXAxis(null)}\n        onClearY={() => setYAxis(null)}\n        onClearLegend={() => setLegend(null)}\n        onClearValues={() => setValues(null)}\n        isVisible={showFixedDropBar}\n        position=\"bottom\"\n      />\n\n      {/* Drag Overlay */}\n      <DragOverlay>\n        {activeId ? (\n          <div className=\"transform rotate-3 scale-105\">\n            <DraggableColumn\n              id={activeId}\n              column={{ name: 'Glissement...', type: 'unknown' }}\n            />\n          </div>\n        ) : null}\n      </DragOverlay>\n\n      {/* Modal de sauvegarde */}\n      <SaveVisualizationModal\n        isOpen={showSaveModal}\n        onClose={() => setShowSaveModal(false)}\n        onSave={saveVisualizationToBackend}\n        loading={savingVisualization}\n        currentConfig={{\n          chartType,\n          xAxis,\n          yAxis,\n          values,\n          legend,\n          aggFunction,\n          filters\n        }}\n      />\n    </DndContext>\n  );\n};\n\nexport default DragVisualPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,SAASC,UAAU,EAAEC,WAAW,EAAEC,aAAa,QAAQ,eAAe;AACtE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SACEC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,WAAW,EACXC,SAAS,QACJ,gBAAgB;AACvB,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,iBAAiB,EACjBC,eAAe,QACV,sBAAsB;AAC7B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SACEC,iBAAiB,EACjBC,qBAAqB,EACrBC,iBAAiB,EACjBC,mBAAmB,QACd,8BAA8B;AACrC,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,YAAY,GAAG,uBAAuB;AAE5C,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,YAAY,CAAC,GAAG/B,eAAe,CAAC,CAAC;;EAExC;EACA,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqD,SAAS,EAAEC,YAAY,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmE,OAAO,EAAEC,UAAU,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6E,KAAK,EAAEC,QAAQ,CAAC,GAAG9E,QAAQ,CAAC;IAAE+E,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAU,CAAC,CAAC;;EAEjF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM;IACJwF,SAAS;IACTjC,OAAO,EAAEkC,gBAAgB;IACzB5B,KAAK,EAAE6B,cAAc;IACrBC,SAAS;IACTC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAGpE,mBAAmB,CAAC,CAAC;;EAEzB;EACAzB,SAAS,CAAC,MAAM;IACd8F,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9F,SAAS,CAAC,MAAM;IACd,MAAM+F,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO;MAErC,IAAIxB,WAAW,EAAE;QACf,IAAIsB,cAAc,GAAGxB,WAAW,IAAIwB,cAAc,GAAG,GAAG,EAAE;UACxD;UACAzB,mBAAmB,CAAC,KAAK,CAAC;QAC5B,CAAC,MAAM,IAAIyB,cAAc,GAAGxB,WAAW,EAAE;UACvC;UACAD,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF;MAEAE,cAAc,CAACuB,cAAc,CAAC;IAChC,CAAC;IAED,IAAItB,WAAW,EAAE;MACfuB,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,EAAE;QAAEK,OAAO,EAAE;MAAK,CAAC,CAAC;MAClE,OAAO,MAAMH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;IACjE;EACF,CAAC,EAAE,CAACvB,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;EACA1E,SAAS,CAAC,MAAM;IACd,MAAMsG,aAAa,GAAIC,CAAC,IAAK;MAC3B,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACG,GAAG,KAAK,GAAG,EAAE;QAC7CH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBpC,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;QACtC,IAAI,CAACA,gBAAgB,EAAEK,cAAc,CAAC,KAAK,CAAC;MAC9C;IACF,CAAC;IAEDsB,MAAM,CAACE,gBAAgB,CAAC,SAAS,EAAEG,aAAa,CAAC;IACjD,OAAO,MAAML,MAAM,CAACI,mBAAmB,CAAC,SAAS,EAAEC,aAAa,CAAC;EACnE,CAAC,EAAE,CAAChC,gBAAgB,CAAC,CAAC;;EAEtB;EACAtE,SAAS,CAAC,MAAM;IACd,IAAIwC,aAAa,EAAE;MACjBoE,WAAW,CAACpE,aAAa,CAAC;MAC1B;MACA;MACAD,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACC,aAAa,CAAC,CAAC;EAEnB,MAAMsD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,gBAAgB,CAAC,IAAI,CAAC;MACtBI,QAAQ,CAAC,EAAE,CAAC;MACZ,MAAMgD,QAAQ,GAAG,MAAMvG,KAAK,CAACwG,GAAG,CAAC,GAAGjF,YAAY,gBAAgB,CAAC;MACjEM,SAAS,CAAC0E,QAAQ,CAACE,IAAI,CAAC7E,MAAM,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAO8E,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZrD,QAAQ,CAAC,wCAAwC,IAAI,EAAAoD,aAAA,GAAAD,GAAG,CAACH,QAAQ,cAAAI,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcF,IAAI,cAAAG,kBAAA,uBAAlBA,kBAAA,CAAoBC,MAAM,KAAIH,GAAG,CAACjC,OAAO,CAAC,CAAC;IAClG,CAAC,SAAS;MACRtB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMmD,WAAW,GAAG,MAAOQ,SAAS,IAAK;IACvC,IAAI;MACFzD,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMkD,QAAQ,GAAG,MAAMvG,KAAK,CAACwG,GAAG,CAAC,GAAGjF,YAAY,WAAWuF,SAAS,UAAU,CAAC;MAC/E/E,UAAU,CAACwE,QAAQ,CAACE,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAK,cAAA,EAAAC,mBAAA;MACZzD,QAAQ,CAAC,0CAA0C,IAAI,EAAAwD,cAAA,GAAAL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcN,IAAI,cAAAO,mBAAA,uBAAlBA,mBAAA,CAAoBH,MAAM,KAAIH,GAAG,CAACjC,OAAO,CAAC,CAAC;IACpG,CAAC,SAAS;MACRpB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM4D,eAAe,GAAIC,KAAK,IAAK;IACjCzD,WAAW,CAACyD,KAAK,CAACC,MAAM,CAACC,EAAE,CAAC;EAC9B,CAAC;EAED,MAAMC,aAAa,GAAIH,KAAK,IAAK;IAC/B,MAAM;MAAEC,MAAM;MAAEG;IAAK,CAAC,GAAGJ,KAAK;IAC9BzD,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI,CAAC6D,IAAI,EAAE;IAEX,MAAMC,QAAQ,GAAGJ,MAAM,CAACC,EAAE;IAC1B,MAAMI,UAAU,GAAGF,IAAI,CAACF,EAAE;;IAE1B;IACA,MAAMK,UAAU,GAAG,CACjB,IAAI3F,OAAO,CAAC4F,WAAW,IAAI,EAAE,CAAC,EAC9B,IAAI5F,OAAO,CAAC6F,eAAe,IAAI,EAAE,CAAC,EAClC,IAAI7F,OAAO,CAAC8F,mBAAmB,IAAI,EAAE,CAAC,EACtC,IAAI9F,OAAO,CAAC+F,YAAY,IAAI,EAAE,CAAC,CAChC;IAED,MAAMC,MAAM,GAAGL,UAAU,CAACM,IAAI,CAACC,GAAG,IAAI,UAAUA,GAAG,CAACC,IAAI,EAAE,KAAKV,QAAQ,CAAC;IACxE,IAAI,CAACO,MAAM,EAAE;;IAEb;IACA,MAAMI,eAAe,GAAG;MAAE,GAAGJ,MAAM;MAAEK,KAAK,EAAEjG;IAAc,CAAC;;IAE3D;IACAkG,kBAAkB,CAACZ,UAAU,EAAEU,eAAe,CAAC;EACjD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAACC,MAAM,EAAEH,eAAe,KAAK;IACtD,QAAQG,MAAM;MACZ,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBhG,QAAQ,CAAC6F,eAAe,CAAC;QACzB;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjB3F,QAAQ,CAAC2F,eAAe,CAAC;QACzB;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBzF,SAAS,CAACyF,eAAe,CAAC;QAC1B;MACF,KAAK,QAAQ;MACb,KAAK,cAAc;QACjBvF,SAAS,CAACuF,eAAe,CAAC;QAC1B;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAMI,eAAe,GAAGA,CAACD,MAAM,EAAEH,eAAe,KAAK;IACnDE,kBAAkB,CAACC,MAAM,EAAEH,eAAe,CAAC;;IAE3C;IACA,MAAMK,SAAS,GAAG;MAChB,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,SAAS;MACnB,QAAQ,EAAE;IACZ,CAAC;IAEDhE,QAAQ,CAAC;MACPC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,GAAGyD,eAAe,CAACD,IAAI,aAAaM,SAAS,CAACF,MAAM,CAAC,EAAE;MAChE3D,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACA8D,UAAU,CAAC,MAAM;MACfjE,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM+D,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,MAAM,GAAG;MACbtB,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdX,IAAI,EAAE,iBAAiB,IAAIU,IAAI,CAAC,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;MACxDV,KAAK,EAAEjG,aAAa;MACpBE,KAAK;MACLE,KAAK;MACLE,MAAM;MACNE,MAAM;MACNE,WAAW;MACXE,SAAS;MACTc,OAAO;MACPkF,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,UAAU,GAAG,CAAC,GAAGjE,mBAAmB,EAAE2D,MAAM,CAAC;IACnD1D,sBAAsB,CAACgE,UAAU,CAAC;IAClCC,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,CAAC;IAEzEzE,QAAQ,CAAC;MACPC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE;IACR,CAAC,CAAC;IAEF8D,UAAU,CAAC,MAAM;MACfjE,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM2E,iBAAiB,GAAIX,MAAM,IAAK;IACpCvG,gBAAgB,CAACuG,MAAM,CAACP,KAAK,CAAC;IAC9B9F,QAAQ,CAACqG,MAAM,CAACtG,KAAK,CAAC;IACtBG,QAAQ,CAACmG,MAAM,CAACpG,KAAK,CAAC;IACtBG,SAAS,CAACiG,MAAM,CAAClG,MAAM,CAAC;IACxBG,SAAS,CAAC+F,MAAM,CAAChG,MAAM,CAAC;IACxBG,cAAc,CAAC6F,MAAM,CAAC9F,WAAW,CAAC;IAClCG,YAAY,CAAC2F,MAAM,CAAC5F,SAAS,CAAC;IAC9Be,UAAU,CAAC6E,MAAM,CAAC9E,OAAO,IAAI,EAAE,CAAC;IAEhCW,QAAQ,CAAC;MACPC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,kBAAkBiE,MAAM,CAACT,IAAI,WAAW;MACjDvD,IAAI,EAAE;IACR,CAAC,CAAC;IAEF8D,UAAU,CAAC,MAAM;MACfjE,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM4E,mBAAmB,GAAIC,QAAQ,IAAK;IACxC,MAAMP,UAAU,GAAGjE,mBAAmB,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKmC,QAAQ,CAAC;IACrEvE,sBAAsB,CAACgE,UAAU,CAAC;IAClCC,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAC,CAAC;EAC3E,CAAC;;EAED;EACA,MAAMU,0BAA0B,GAAG,MAAOC,QAAQ,IAAK;IACrD,IAAI,CAACzH,aAAa,IAAK,CAACE,KAAK,IAAI,CAACM,MAAO,EAAE;MACzCkH,SAAS,CAAC,6CAA6C,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,IAAI;MACF9E,sBAAsB,CAAC,IAAI,CAAC;MAE5B,MAAM+E,WAAW,GAAG;QAClB5B,IAAI,EAAE0B,QAAQ,CAAC1B,IAAI;QACnB6B,WAAW,EAAEH,QAAQ,CAACG,WAAW;QACjCC,UAAU,EAAEjH,SAAS;QACrBqF,KAAK,EAAEjG,aAAa;QACpB8H,MAAM,EAAE,CAAA5H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,IAAI,KAAI,IAAI;QAC3BgC,MAAM,EAAE,CAAA3H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2F,IAAI,KAAI,IAAI;QAC3BzF,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyF,IAAI,KAAI,IAAI;QAC5BvF,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuF,IAAI,KAAI,IAAI;QAC5BiC,YAAY,EAAEtH,WAAW;QACzBuH,YAAY,EAAE,CAAA/H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+F,KAAK,KAAI,IAAI;QAClCiC,YAAY,EAAE,CAAA9H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,KAAK,KAAI,IAAI;QAClCkC,YAAY,EAAE,CAAA7H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2F,KAAK,KAAI,IAAI;QACnCmC,YAAY,EAAE,CAAA5H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyF,KAAK,KAAI,IAAI;QACnCvE,OAAO,EAAEA,OAAO,CAAC4F,MAAM,CAACe,CAAC,IACtBA,CAAC,CAACC,WAAW,KAAK,OAAO,KAAKD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,SAAS,CAAC,IACzDH,CAAC,CAACC,WAAW,KAAK,OAAO,IAAID,CAAC,CAAC7H,MAAM,IAAI6H,CAAC,CAAC7H,MAAM,CAACiI,MAAM,GAAG,CAC9D,CAAC,CAACC,GAAG,CAACL,CAAC,KAAK;UACVM,WAAW,EAAEN,CAAC,CAACM,WAAW;UAC1BC,UAAU,EAAEP,CAAC,CAACO,UAAU;UACxBN,WAAW,EAAED,CAAC,CAACC,WAAW;UAC1B9H,MAAM,EAAE6H,CAAC,CAAC7H,MAAM,IAAI,EAAE;UACtB+H,SAAS,EAAEF,CAAC,CAACE,SAAS,IAAI,IAAI;UAC9BC,SAAS,EAAEH,CAAC,CAACG,SAAS,IAAI;QAC5B,CAAC,CAAC,CAAC;QACHK,IAAI,EAAEpB,QAAQ,CAACoB,IAAI,IAAI;MACzB,CAAC;MAED,MAAMxE,QAAQ,GAAG,MAAMvG,KAAK,CAACgL,IAAI,CAAC,GAAGzJ,YAAY,gBAAgB,EAAEsI,WAAW,CAAC;MAE/E,IAAItD,QAAQ,CAACE,IAAI,CAACwE,OAAO,EAAE;QACzBrG,gBAAgB,CAAC,KAAK,CAAC;QACvBgF,SAAS,CAAC,kBAAkBD,QAAQ,CAAC1B,IAAI,2BAA2B,CAAC;MACvE;IACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;MAAA,IAAA4H,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAAC9H,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDsG,SAAS,CAAC,gCAAgC,IAAI,EAAAsB,eAAA,GAAA5H,KAAK,CAACiD,QAAQ,cAAA2E,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBzE,IAAI,cAAA0E,oBAAA,uBAApBA,oBAAA,CAAsBtE,MAAM,KAAIvD,KAAK,CAACmB,OAAO,CAAC,EAAE,OAAO,CAAC;IACxG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACApF,SAAS,CAAC,MAAM;IACd,MAAM2L,KAAK,GAAGpC,YAAY,CAACqC,OAAO,CAAC,uBAAuB,CAAC;IAC3D,IAAID,KAAK,EAAE;MACT,IAAI;QACFrG,sBAAsB,CAACmE,IAAI,CAACoC,KAAK,CAACF,KAAK,CAAC,CAAC;MAC3C,CAAC,CAAC,OAAO/H,KAAK,EAAE;QACd8H,OAAO,CAAC9H,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACvE;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5D,SAAS,CAAC,MAAM;IACd,MAAM8L,SAAS,GAAG7J,YAAY,CAAC6E,GAAG,CAAC,MAAM,CAAC;IAC1C,IAAIgF,SAAS,EAAE;MACb;MACA,IAAI,CAACtI,aAAa,IAAItB,MAAM,CAAC+I,MAAM,GAAG,CAAC,EAAE;QACvCc,sBAAsB,CAACD,SAAS,CAAC;MACnC;IACF;EACF,CAAC,EAAE,CAAC7J,YAAY,EAAEuB,aAAa,EAAEtB,MAAM,CAAC,CAAC;;EAEzC;EACA,MAAMgI,SAAS,GAAGA,CAACnF,OAAO,EAAEC,IAAI,GAAG,SAAS,KAAK;IAC/CH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAK,CAAC,CAAC;IACvC8D,UAAU,CAAC,MAAM;MACfjE,QAAQ,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC;IACzD,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAMgH,mBAAmB,GAAIpI,KAAK,IAAK;IAAA,IAAAqI,gBAAA,EAAAC,qBAAA;IACrC,IAAIC,QAAQ,GAAG,EAAAF,gBAAA,GAAArI,KAAK,CAACiD,QAAQ,cAAAoF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlF,IAAI,cAAAmF,qBAAA,uBAApBA,qBAAA,CAAsB/E,MAAM,KAAIvD,KAAK,CAACmB,OAAO;;IAE5D;IACA,IAAI,OAAOoH,QAAQ,KAAK,QAAQ,EAAE;MAChC,IAAIC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,EAAE;QAC3BA,QAAQ,GAAGA,QAAQ,CAACjB,GAAG,CAAClE,GAAG,IAAI;UAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAOA,GAAG,CAACsF,GAAG,IAAI,GAAGtF,GAAG,CAAChC,IAAI,KAAKgC,GAAG,CAACuF,KAAK,EAAE,IAAI9C,IAAI,CAACC,SAAS,CAAC1C,GAAG,CAAC;UACtE;UACA,OAAOA,GAAG;QACZ,CAAC,CAAC,CAACwF,IAAI,CAAC,IAAI,CAAC;MACf,CAAC,MAAM;QACLL,QAAQ,GAAGA,QAAQ,CAACG,GAAG,IAAI,GAAGH,QAAQ,CAACnH,IAAI,KAAKmH,QAAQ,CAACI,KAAK,EAAE,IAAI9C,IAAI,CAACC,SAAS,CAACyC,QAAQ,CAAC;MAC9F;IACF;IAEA,OAAOM,MAAM,CAACN,QAAQ,CAAC;EACzB,CAAC;;EAED;EACA,MAAMJ,sBAAsB,GAAG,MAAOW,KAAK,IAAK;IAC9C,IAAI;MACFnJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsD,QAAQ,GAAG,MAAMvG,KAAK,CAACwG,GAAG,CAAC,GAAGjF,YAAY,kBAAkB6K,KAAK,EAAE,CAAC;MAE1E,IAAI7F,QAAQ,CAACE,IAAI,CAACwE,OAAO,EAAE;QACzB,MAAMoB,GAAG,GAAG9F,QAAQ,CAACE,IAAI,CAAC6F,aAAa;;QAEvC;QACAnK,gBAAgB,CAACkK,GAAG,CAAClE,KAAK,CAAC;QAC3BpF,YAAY,CAACsJ,GAAG,CAACtC,UAAU,CAAC;QAC5BlH,cAAc,CAACwJ,GAAG,CAACnC,YAAY,CAAC;;QAEhC;QACA,IAAImC,GAAG,CAACrC,MAAM,EAAE;UACd3H,QAAQ,CAAC;YACP4F,IAAI,EAAEoE,GAAG,CAACrC,MAAM;YAChB7B,KAAK,EAAEkE,GAAG,CAAClC,YAAY,IAAIkC,GAAG,CAAClE,KAAK;YACpCzD,IAAI,EAAE,SAAS,CAAC;UAClB,CAAC,CAAC;QACJ;QAEA,IAAI2H,GAAG,CAACpC,MAAM,EAAE;UACd1H,QAAQ,CAAC;YACP0F,IAAI,EAAEoE,GAAG,CAACpC,MAAM;YAChB9B,KAAK,EAAEkE,GAAG,CAACjC,YAAY,IAAIiC,GAAG,CAAClE,KAAK;YACpCzD,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QAEA,IAAI2H,GAAG,CAAC7J,MAAM,EAAE;UACdC,SAAS,CAAC;YACRwF,IAAI,EAAEoE,GAAG,CAAC7J,MAAM;YAChB2F,KAAK,EAAEkE,GAAG,CAAChC,YAAY,IAAIgC,GAAG,CAAClE,KAAK;YACpCzD,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QAEA,IAAI2H,GAAG,CAAC3J,MAAM,EAAE;UACdC,SAAS,CAAC;YACRsF,IAAI,EAAEoE,GAAG,CAAC3J,MAAM;YAChByF,KAAK,EAAEkE,GAAG,CAAC/B,YAAY,IAAI+B,GAAG,CAAClE,KAAK;YACpCzD,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;;QAEA;QACAb,UAAU,CAACwI,GAAG,CAACzI,OAAO,IAAI,EAAE,CAAC;QAE7BgG,SAAS,CAAC,kBAAkByC,GAAG,CAACpE,IAAI,uBAAuB,CAAC;MAC9D;IACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;MAAA,IAAAiJ,gBAAA,EAAAC,qBAAA;MACdpB,OAAO,CAAC9H,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;MACtEsG,SAAS,CAAC,6BAA6B,IAAI,EAAA2C,gBAAA,GAAAjJ,KAAK,CAACiD,QAAQ,cAAAgG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9F,IAAI,cAAA+F,qBAAA,uBAApBA,qBAAA,CAAsB3F,MAAM,KAAIvD,KAAK,CAACmB,OAAO,CAAC,EAAE,OAAO,CAAC;IACrG,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwJ,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACvK,aAAa,EAAE;MAClBqB,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAI,CAACnB,KAAK,IAAI,CAACM,MAAM,EAAE;MACrBa,QAAQ,CAAC,6DAA6D,CAAC;MACvE;IACF;IAEA,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;MAChBM,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMsG,WAAW,GAAG;QAClB1B,KAAK,EAAEjG,aAAa;QAAE;QACtB8H,MAAM,EAAE,CAAA5H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,IAAI,KAAI,IAAI;QAC3BgC,MAAM,EAAE,CAAA3H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2F,IAAI,KAAI,IAAI;QAC3BzF,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyF,IAAI,KAAI,IAAI;QAC5BvF,MAAM,EAAE,CAAAA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuF,IAAI,KAAI,IAAI;QAC5BiC,YAAY,EAAEtH,WAAW;QACzBmH,UAAU,EAAEjH,SAAS;QACrB;QACAqH,YAAY,EAAE,CAAA/H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+F,KAAK,KAAI,IAAI;QAClCiC,YAAY,EAAE,CAAA9H,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6F,KAAK,KAAI,IAAI;QAClCkC,YAAY,EAAE,CAAA7H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2F,KAAK,KAAI,IAAI;QACnCmC,YAAY,EAAE,CAAA5H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyF,KAAK,KAAI,IAAI;QACnC;QACAvE,OAAO,EAAEA,OAAO,CAAC4F,MAAM,CAACe,CAAC,IACtBA,CAAC,CAACC,WAAW,KAAK,OAAO,KAAKD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,SAAS,CAAC,IACzDH,CAAC,CAACC,WAAW,KAAK,OAAO,IAAID,CAAC,CAAC7H,MAAM,IAAI6H,CAAC,CAAC7H,MAAM,CAACiI,MAAM,GAAG,CAC9D,CAAC,CAACC,GAAG,CAACL,CAAC,KAAK;UACVM,WAAW,EAAEN,CAAC,CAACM,WAAW;UAC1BC,UAAU,EAAEP,CAAC,CAACO,UAAU;UACxBN,WAAW,EAAED,CAAC,CAACC,WAAW;UAC1B9H,MAAM,EAAE6H,CAAC,CAAC7H,MAAM,IAAI,EAAE;UACtB+H,SAAS,EAAEF,CAAC,CAACE,SAAS,IAAI,IAAI;UAC9BC,SAAS,EAAEH,CAAC,CAACG,SAAS,IAAI;QAC5B,CAAC,CAAC;MACJ,CAAC;MAED,MAAMnE,QAAQ,GAAG,MAAMvG,KAAK,CAACgL,IAAI,CAAC,GAAGzJ,YAAY,cAAc,EAAEsI,WAAW,CAAC;MAC7E5H,YAAY,CAACsE,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAgG,cAAA,EAAAC,mBAAA;MACZpJ,QAAQ,CAAC,gCAAgC,IAAI,EAAAmJ,cAAA,GAAAhG,GAAG,CAACH,QAAQ,cAAAmG,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjG,IAAI,cAAAkG,mBAAA,uBAAlBA,mBAAA,CAAoB9F,MAAM,KAAIH,GAAG,CAACjC,OAAO,CAAC,CAAC;IAC1F,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2J,QAAQ,GAAGA,CAAA,KAAM;IACrBvK,QAAQ,CAAC,IAAI,CAAC;IACdE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IACfE,SAAS,CAAC,IAAI,CAAC;IACfV,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAM4K,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC7K,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAM8K,QAAQ,GAAG,EAAE;;IAEnB;IACA,IAAIC,KAAK,GAAG/K,SAAS,CAAC+K,KAAK;IAC3B,IAAIC,kBAAkB,GAAG,CAAC,EAAE;MAC1BD,KAAK,IAAI,KAAKC,kBAAkB,UAAUA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,YAAYA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG;IAC7H;IAEA,MAAMC,MAAM,GAAG;MACbF,KAAK,EAAEA,KAAK;MACZG,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAG,CAAC;MACtCC,aAAa,EAAE,eAAe;MAC9BC,YAAY,EAAE,eAAe;MAC7BC,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAU;IAC3B,CAAC;IAED,QAAQ5L,SAAS,CAAC0C,IAAI;MACpB,KAAK,KAAK;QACR,IAAI1C,SAAS,CAACQ,MAAM,IAAIsJ,KAAK,CAACC,OAAO,CAAC/J,SAAS,CAAC6L,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrD;UACA7L,SAAS,CAACQ,MAAM,CAACsL,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;YAC9ClB,QAAQ,CAACmB,IAAI,CAAC;cACZC,CAAC,EAAElM,SAAS,CAACkM,CAAC;cACdL,CAAC,EAAE7L,SAAS,CAAC6L,CAAC,CAACjD,GAAG,CAACuD,GAAG,IAAIA,GAAG,CAACH,KAAK,CAAC,IAAI,CAAC,CAAC;cAC1CtJ,IAAI,EAAE,KAAK;cACXuD,IAAI,EAAE8F,UAAU;cAChBK,MAAM,EAAE;gBACNR,KAAK,EAAE,OAAQI,KAAK,GAAG,EAAE,GAAI,GAAG;cAClC;YACF,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLlB,QAAQ,CAACmB,IAAI,CAAC;YACZC,CAAC,EAAElM,SAAS,CAACkM,CAAC;YACdL,CAAC,EAAE7L,SAAS,CAAC6L,CAAC;YACdnJ,IAAI,EAAE,KAAK;YACX0J,MAAM,EAAE;cAAER,KAAK,EAAE;YAAU;UAC7B,CAAC,CAAC;QACJ;QACAX,MAAM,CAACoB,KAAK,GAAG;UAAEtB,KAAK,EAAE/K,SAAS,CAACsM;QAAO,CAAC;QAC1CrB,MAAM,CAACsB,KAAK,GAAG;UAAExB,KAAK,EAAE/K,SAAS,CAACwM;QAAO,CAAC;QAC1C;MAEF,KAAK,MAAM;QACT1B,QAAQ,CAACmB,IAAI,CAAC;UACZC,CAAC,EAAElM,SAAS,CAACkM,CAAC;UACdL,CAAC,EAAE7L,SAAS,CAAC6L,CAAC;UACdnJ,IAAI,EAAE,SAAS;UACf+J,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAEd,KAAK,EAAE,SAAS;YAAEe,KAAK,EAAE;UAAE,CAAC;UACpCP,MAAM,EAAE;YAAER,KAAK,EAAE,SAAS;YAAEgB,IAAI,EAAE;UAAE;QACtC,CAAC,CAAC;QACF3B,MAAM,CAACoB,KAAK,GAAG;UAAEtB,KAAK,EAAE/K,SAAS,CAACsM;QAAO,CAAC;QAC1CrB,MAAM,CAACsB,KAAK,GAAG;UAAExB,KAAK,EAAE/K,SAAS,CAACwM;QAAO,CAAC;QAC1C;MAEF,KAAK,KAAK;QACR1B,QAAQ,CAACmB,IAAI,CAAC;UACZY,MAAM,EAAE7M,SAAS,CAACkM,CAAC;UACnBxL,MAAM,EAAEV,SAAS,CAAC6L,CAAC;UACnBnJ,IAAI,EAAE,KAAK;UACX0J,MAAM,EAAE;YACNU,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;UACtF,CAAC;UACDC,QAAQ,EAAE,eAAe;UACzBC,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MAEF,KAAK,SAAS;QACZlC,QAAQ,CAACmB,IAAI,CAAC;UACZC,CAAC,EAAElM,SAAS,CAACkM,CAAC;UACdL,CAAC,EAAE7L,SAAS,CAAC6L,CAAC;UACdnJ,IAAI,EAAE,SAAS;UACf+J,IAAI,EAAE,SAAS;UACfL,MAAM,EAAE;YACNR,KAAK,EAAE,SAAS;YAChBgB,IAAI,EAAE,EAAE;YACRF,IAAI,EAAE;cAAEd,KAAK,EAAE,SAAS;cAAEe,KAAK,EAAE;YAAE;UACrC;QACF,CAAC,CAAC;QACF1B,MAAM,CAACoB,KAAK,GAAG;UAAEtB,KAAK,EAAE/K,SAAS,CAACsM;QAAO,CAAC;QAC1CrB,MAAM,CAACsB,KAAK,GAAG;UAAExB,KAAK,EAAE/K,SAAS,CAACwM;QAAO,CAAC;QAC1C;MAEF;QACE,oBAAOlN,OAAA;UAAG2N,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,qCAAgC,EAAClN,SAAS,CAAC0C,IAAI;QAAA;UAAAyK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;IAC3F;IAEA,oBACEhO,OAAA,CAACrB,IAAI;MACHwG,IAAI,EAAEqG,QAAS;MACfG,MAAM,EAAEA,MAAO;MACfvE,MAAM,EAAE;QAAE6G,UAAU,EAAE,IAAI;QAAEC,cAAc,EAAE;MAAK,CAAE;MACnDC,KAAK,EAAE;QAAEd,KAAK,EAAE;MAAO;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAED,MAAMI,WAAW,GAAGxN,aAAa,KAAKE,KAAK,IAAIM,MAAM,CAAC;;EAEtD;EACA,MAAMiN,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM/N,MAAM,GAAG,IAAIgO,GAAG,CAAC,CAAC;IACxB,CAACxN,KAAK,EAAEE,KAAK,EAAEE,MAAM,EAAEE,MAAM,CAAC,CAACoL,OAAO,CAAC9F,GAAG,IAAI;MAC5C,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEG,KAAK,EAAEvG,MAAM,CAACiO,GAAG,CAAC7H,GAAG,CAACG,KAAK,CAAC;IACvC,CAAC,CAAC;IACF,OAAOvG,MAAM,CAACgN,IAAI,GAAG,CAAC;EACxB,CAAC;;EAED;EACA,MAAM5B,kBAAkB,GAAGpJ,OAAO,CAAC4F,MAAM,CAACe,CAAC,IACxCA,CAAC,CAACC,WAAW,KAAK,OAAO,KAAKD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,SAAS,CAAC,IACzDH,CAAC,CAACC,WAAW,KAAK,OAAO,IAAID,CAAC,CAAC7H,MAAM,IAAI6H,CAAC,CAAC7H,MAAM,CAACiI,MAAM,GAAG,CAC9D,CAAC,CAACA,MAAM;EAER,oBACErJ,OAAA,CAACzB,UAAU;IACTiQ,kBAAkB,EAAE/P,aAAc;IAClCgQ,WAAW,EAAE9I,eAAgB;IAC7B+I,SAAS,EAAE3I,aAAc;IAAA6H,QAAA,gBAEzB5N,OAAA;MAAK2N,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD5N,OAAA,CAACpB,aAAa;QAAC+P,OAAO,EAAE;MAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEhChO,OAAA;QAAK2N,SAAS,EAAE,+BAA+BjL,gBAAgB,GAAG,OAAO,GAAG,MAAM,EAAG;QAAAkL,QAAA,gBAEnF5N,OAAA;UAAK2N,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5N,OAAA;YAAK2N,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrD5N,OAAA;cAAA4N,QAAA,gBACE5N,OAAA;gBAAK2N,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C5N,OAAA;kBAAK2N,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,eACjH5N,OAAA;oBAAM2N,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACNhO,OAAA;kBAAI2N,SAAS,EAAC,+FAA+F;kBAAAC,QAAA,EAAC;gBAE9G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACNhO,OAAA;gBAAG2N,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAE7B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhO,OAAA;gBAAG2N,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACH3N,YAAY,CAAC6E,GAAG,CAAC,MAAM,CAAC,iBACvBlF,OAAA;gBAAK2N,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBACvD5N,OAAA;kBAAM2N,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzChO,OAAA;kBAAM2N,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAyC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CACN,eACDhO,OAAA;gBAAG2N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJhO,OAAA;gBAAG2N,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNhO,OAAA;cAAK2N,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5N,OAAA,CAAChB,eAAe;gBACd4P,OAAO,EAAEA,CAAA,KAAM;kBACbjM,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;kBACtC,IAAI,CAACA,gBAAgB,EAAEK,cAAc,CAAC,KAAK,CAAC;gBAC9C,CAAE;gBACF8L,IAAI,EAAEnM,gBAAgB,GAAG,KAAK,GAAG,SAAU;gBAC3CiL,SAAS,EAAEjL,gBAAgB,GAAG,qCAAqC,GAAG,EAAG;gBAAAkL,QAAA,EAC1E;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;gBACd4P,OAAO,EAAEA,CAAA,KAAM7L,cAAc,CAAC,CAACD,WAAW,CAAE;gBAC5C+L,IAAI,EAAE/L,WAAW,GAAG,IAAI,GAAG,IAAK;gBAChC6K,SAAS,EAAE7K,WAAW,GAAG,iCAAiC,GAAG,EAAG;gBAChEgM,QAAQ,EAAE,CAACpM,gBAAiB;gBAAAkL,QAAA,EAC7B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;gBACd4P,OAAO,EAAEzH,wBAAyB;gBAClC0H,IAAI,EAAC,cAAI;gBACTC,QAAQ,EAAE,CAAClO,aAAa,IAAK,CAACE,KAAK,IAAI,CAACM,MAAQ;gBAAAwM,QAAA,EACjD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAACjB,aAAa;gBACZ6P,OAAO,EAAEA,CAAA,KAAMtL,gBAAgB,CAAC,IAAI,CAAE;gBACtCuL,IAAI,EAAC,cAAI;gBACTC,QAAQ,EAAE,CAAClO,aAAa,IAAK,CAACE,KAAK,IAAI,CAACM,MAAQ;gBAChDuM,SAAS,EAAC,aAAa;gBAAAC,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC,eAChBhO,OAAA,CAAChB,eAAe;gBAAC4P,OAAO,EAAEA,CAAA,KAAMxO,QAAQ,CAAC,YAAY,CAAE;gBAACyO,IAAI,EAAC,cAAI;gBAAAjB,QAAA,EAAC;cAElE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;gBAAC4P,OAAO,EAAEA,CAAA,KAAMxO,QAAQ,CAAC,iBAAiB,CAAE;gBAACyO,IAAI,EAAC,cAAI;gBAAAjB,QAAA,EAAC;cAEvE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;gBAAC4P,OAAO,EAAEA,CAAA,KAAMxO,QAAQ,CAAC,SAAS,CAAE;gBAACyO,IAAI,EAAC,cAAI;gBAAAjB,QAAA,EAAC;cAE/D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;gBAAC4P,OAAO,EAAEA,CAAA,KAAMxO,QAAQ,CAAC,GAAG,CAAE;gBAACyO,IAAI,EAAC,cAAI;gBAAAjB,QAAA,EAAC;cAEzD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhO,OAAA;UAAK2N,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB5N,OAAA,CAACnB,QAAQ;YAAA+O,QAAA,gBACP5N,OAAA,CAAClB,aAAa;cACZ2M,KAAK,EAAC,6BAA0B;cAChCsD,QAAQ,EAAC,oCAAiC;cAC1CF,IAAI,EAAC;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,EAEDpM,aAAa,gBACZ5B,OAAA;cAAK2N,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpD5N,OAAA,CAACf,WAAW;gBAAA4O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfhO,OAAA;gBAAM2N,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,gBAENhO,OAAA;cACEgP,KAAK,EAAEpO,aAAc;cACrBqO,QAAQ,EAAGtK,CAAC,IAAK9D,gBAAgB,CAAC8D,CAAC,CAACuK,MAAM,CAACF,KAAK,CAAE;cAClDrB,SAAS,EAAC,mHAAmH;cAAAC,QAAA,gBAE7H5N,OAAA;gBAAQgP,KAAK,EAAC,EAAE;gBAAApB,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACrD1N,MAAM,CAACgJ,GAAG,CAAC,CAACzC,KAAK,EAAE6F,KAAK,kBACvB1M,OAAA;gBAAoBgP,KAAK,EAAEnI,KAAK,CAACsI,UAAW;gBAAAvB,QAAA,GACzC/G,KAAK,CAACsI,UAAU,EAAC,IAAE,EAACtI,KAAK,CAACuI,YAAY,EAAC,YAC1C;cAAA,GAFa1C,KAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAELpN,aAAa,iBACZZ,OAAA;UAAK2N,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD5N,OAAA;YAAK2N,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B5N,OAAA,CAACnB,QAAQ;cAAC8O,SAAS,EAAC,OAAO;cAAAC,QAAA,EACxB9L,cAAc,gBACb9B,OAAA;gBAAK2N,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD5N,OAAA,CAACf,WAAW;kBAAA4O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACfhO,OAAA;kBAAM2N,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,gBAENhO,OAAA,CAACb,YAAY;gBACXqB,OAAO,EAAEA,OAAO,CAAC4F,WAAW,IAAI,EAAG;gBACnCqF,KAAK,EAAC,sBAAsB;gBAC5B4D,WAAW,EAAErI,eAAgB;gBAC7BpG,aAAa,EAAEA,aAAc;gBAC7B0O,kBAAkB,EAAE;kBAClB,QAAQ,EAAExO,KAAK;kBACf,QAAQ,EAAEE,KAAK;kBACf,QAAQ,EAAEE,MAAM;kBAChB,QAAQ,EAAEE;gBACZ;cAAE;gBAAAyM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGNhO,OAAA;YAAK2N,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAEtC5N,OAAA,CAACP,cAAc;cACb6C,OAAO,EAAEA,OAAQ;cACjBiN,eAAe,EAAEhN;YAAW;cAAAsL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eAGFhO,OAAA,CAACnB,QAAQ;cAAA+O,QAAA,gBACP5N,OAAA;gBAAK2N,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD5N,OAAA,CAAClB,aAAa;kBACZ2M,KAAK,EAAC,wBAAwB;kBAC9BsD,QAAQ,EAAC,oDAAiD;kBAC1DF,IAAI,EAAC;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFhO,OAAA;kBAAK2N,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7B5N,OAAA,CAAChB,eAAe;oBACd4P,OAAO,EAAEA,CAAA,KAAM7K,iBAAiB,CAAC;sBAAEyL,KAAK,EAAE,IAAI;sBAAEC,WAAW,EAAE,WAAW;sBAAEC,uBAAuB,EAAE;oBAAK,CAAC,CAAE;oBAC3Gb,IAAI,EAAC,cAAI;oBACTC,QAAQ,EAAElL,gBAAiB;oBAC3B+J,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,EAEhEhK,gBAAgB,GAAG,eAAe,GAAG;kBAAkB;oBAAAiK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eAClBhO,OAAA,CAAChB,eAAe;oBAAC4P,OAAO,EAAEtD,QAAS;oBAACuD,IAAI,EAAC,oBAAK;oBAAClB,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAE9G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAiB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhO,OAAA,CAACZ,cAAc;gBACb0B,KAAK,EAAEA,KAAM;gBACbE,KAAK,EAAEA,KAAM;gBACbE,MAAM,EAAEA,MAAO;gBACfE,MAAM,EAAEA,MAAO;gBACfuO,QAAQ,EAAEA,CAAA,KAAM5O,QAAQ,CAAC,IAAI,CAAE;gBAC/B6O,QAAQ,EAAEA,CAAA,KAAM3O,QAAQ,CAAC,IAAI,CAAE;gBAC/B4O,aAAa,EAAEA,CAAA,KAAM1O,SAAS,CAAC,IAAI,CAAE;gBACrC2O,aAAa,EAAEA,CAAA,KAAMzO,SAAS,CAAC,IAAI;cAAE;gBAAAwM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAGXhO,OAAA,CAACnB,QAAQ;cAAA+O,QAAA,gBACP5N,OAAA,CAAClB,aAAa;gBACZ2M,KAAK,EAAC,kBAAkB;gBACxBsD,QAAQ,EAAC,oDAAiD;gBAC1DF,IAAI,EAAC;cAAI;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAEFhO,OAAA;gBAAK2N,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD5N,OAAA,CAACV,mBAAmB;kBAClB0P,KAAK,EAAE1N,WAAY;kBACnB2N,QAAQ,EAAE1N,cAAe;kBACzBuN,QAAQ,EAAE,CAAC1N,MAAM,IAAIE,WAAW,KAAK;gBAAQ;kBAAAuM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eAEFhO,OAAA,CAACT,iBAAiB;kBAChByP,KAAK,EAAExN,SAAU;kBACjByN,QAAQ,EAAExN;gBAAa;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhO,OAAA;gBAAK2N,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrD5N,OAAA;kBAAK2N,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1C5N,OAAA;oBAAK2N,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C5N,OAAA,CAACjB,aAAa;sBACZ6P,OAAO,EAAEzD,qBAAsB;sBAC/B2D,QAAQ,EAAE,CAACV,WAAW,IAAI1M,OAAQ;sBAClCA,OAAO,EAAEA,OAAQ;sBACjBmN,IAAI,EAAEnN,OAAO,GAAG,IAAI,GAAG,IAAK;sBAC5BiM,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAEtBlM,OAAO,GAAG,eAAe,GAAG;oBAA0B;sBAAAmM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,EAEfK,YAAY,CAAC,CAAC,iBACbrO,OAAA;sBAAK2N,SAAS,EAAC,wFAAwF;sBAAAC,QAAA,gBACrG5N,OAAA;wBAAM2N,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzChO,OAAA;wBAAM2N,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CACN,EAEAtC,kBAAkB,GAAG,CAAC,iBACrB1L,OAAA;sBAAK2N,SAAS,EAAC,0FAA0F;sBAAAC,QAAA,gBACvG5N,OAAA;wBAAM2N,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC1ChO,OAAA;wBAAM2N,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,GACjDlC,kBAAkB,EAAC,SAAO,EAACA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,QAAM,EAACA,kBAAkB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;sBAAA;wBAAAmC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAELtN,SAAS,iBACRV,OAAA,CAAChB,eAAe;oBACd4P,OAAO,EAAEA,CAAA,KAAMvM,gBAAgB,CAAC,CAACD,aAAa,CAAE;oBAChDyM,IAAI,EAAC,cAAI;oBACTlB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAEtBxL,aAAa,GAAG,cAAc,GAAG;kBAAW;oBAAAyL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAClB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAEL,CAACI,WAAW,IAAIxN,aAAa,iBAC5BZ,OAAA;kBAAG2N,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGXhO,OAAA,CAACnB,QAAQ;cAAA+O,QAAA,GACN,CAAClN,SAAS,gBACTV,OAAA;gBAAK2N,SAAS,EAAC,6DAA6D;gBAAAC,QAAA,gBAC1E5N,OAAA;kBAAK2N,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,eACvH5N,OAAA;oBAAM2N,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNhO,OAAA;kBAAI2N,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAEzD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhO,OAAA;kBAAG2N,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,gBAENhO,OAAA;gBAAA4N,QAAA,gBACE5N,OAAA;kBAAK2N,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,gBACrD5N,OAAA,CAAClB,aAAa;oBACZ2M,KAAK,EAAC,gCAAuB;oBAC7BsD,QAAQ,EAAE,GAAGrO,SAAS,CAAC0C,IAAI,CAAC2M,WAAW,CAAC,CAAC,MAAMrP,SAAS,CAACsP,WAAW,oBAAqB;oBACzFnB,IAAI,EAAC;kBAAI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACFhO,OAAA,CAACjB,aAAa;oBACZ6P,OAAO,EAAEA,CAAA,KAAMtL,gBAAgB,CAAC,IAAI,CAAE;oBACtCuL,IAAI,EAAC,cAAI;oBACTC,QAAQ,EAAE,CAAClO,aAAa,IAAK,CAACE,KAAK,IAAI,CAACM,MAAQ;oBAChDuM,SAAS,EAAC,aAAa;oBAAAC,QAAA,EACxB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAe,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eAENhO,OAAA;kBAAK2N,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAClBrC,WAAW,CAAC;gBAAC;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,EAELtN,SAAS,CAACiJ,OAAO,iBAChB3J,OAAA;kBAAK2N,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1E5N,OAAA;oBAAG2N,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,GAAC,SAClC,EAAClN,SAAS,CAACyC,OAAO;kBAAA;oBAAA0K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CACN,EAGA5L,aAAa,iBACZpC,OAAA;kBAAK2N,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrE5N,OAAA;oBAAI2N,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EhO,OAAA;oBAAK2N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACnD/F,IAAI,CAACC,SAAS,CAACpH,SAAS,EAAE,IAAI,EAAE,CAAC;kBAAC;oBAAAmN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EAEAhM,KAAK,iBACJhC,OAAA;gBAAK2N,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,eACtE5N,OAAA;kBAAG2N,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAAC,SACxB,EAAC5L,KAAK;gBAAA;kBAAA6L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAGXhO,OAAA;cAAK2N,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5N,OAAA,CAACN,iBAAiB;gBAChBuQ,UAAU,EAAElM,iBAAkB;gBAC9BrC,OAAO,EAAEkC;cAAiB;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,EAEDnK,cAAc,iBACb7D,OAAA,CAACnB,QAAQ;gBAAC8O,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAClC5N,OAAA;kBAAK2N,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB5N,OAAA;oBAAK2N,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,gBACvD5N,OAAA;sBAAM2N,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnChO,OAAA;sBAAA4N,QAAA,gBACE5N,OAAA;wBAAG2N,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAiC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAChEhO,OAAA;wBAAG2N,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAEpC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENhO,OAAA;oBAAK2N,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,eAC9D5N,OAAA;sBAAK2N,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,EAChF/J;oBAAc;sBAAAgK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENhO,OAAA;oBAAK2N,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpD5N,OAAA,CAAChB,eAAe;sBACd4P,OAAO,EAAEA,CAAA,KAAM7K,iBAAiB,CAAC;wBAAEyL,KAAK,EAAE,IAAI;wBAAEC,WAAW,EAAE,WAAW;wBAAEC,uBAAuB,EAAE;sBAAK,CAAC,CAAE;sBAC3Gb,IAAI,EAAC,cAAI;sBACTlB,SAAS,EAAC,sBAAsB;sBAChCmB,QAAQ,EAAElL,gBAAiB;sBAAAgK,QAAA,EAC5B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;sBACd4P,OAAO,EAAEA,CAAA,KAAM7K,iBAAiB,CAAC;wBAAEyL,KAAK,EAAE,IAAI;wBAAEC,WAAW,EAAE,UAAU;wBAAEC,uBAAuB,EAAE;sBAAM,CAAC,CAAE;sBAC3Gb,IAAI,EAAC,QAAG;sBACRlB,SAAS,EAAC,sBAAsB;sBAChCmB,QAAQ,EAAElL,gBAAiB;sBAAAgK,QAAA,EAC5B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;sBACd4P,OAAO,EAAEA,CAAA,KAAMvK,MAAM,CAAC6L,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAE;sBACrErB,IAAI,EAAC,cAAI;sBACTlB,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,EACjE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;sBACd4P,OAAO,EAAEA,CAAA,KAAMvK,MAAM,CAAC6L,IAAI,CAAC,oCAAoC,EAAE,QAAQ,CAAE;sBAC3ErB,IAAI,EAAC,cAAI;sBACTlB,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eAClBhO,OAAA,CAAChB,eAAe;sBACd4P,OAAO,EAAEA,CAAA,KAAMnM,wBAAwB,CAAC,IAAI,CAAE;sBAC9CoM,IAAI,EAAC,cAAI;sBACTlB,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,EACrE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EAEL,CAAAlK,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEqM,UAAU,MAAK,YAAY,iBACrCnQ,OAAA;oBAAK2N,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpE5N,OAAA;sBAAK2N,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACzC5N,OAAA;wBAAM2N,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAChDhO,OAAA;wBAAK2N,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,gBACtC5N,OAAA;0BAAG2N,SAAS,EAAC,kBAAkB;0BAAAC,QAAA,EAAC;wBAA6B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACjEhO,OAAA;0BAAI2N,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAC/B5N,OAAA;4BAAA4N,QAAA,EAAI;0BAAsD;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC/DhO,OAAA;4BAAA4N,QAAA,GAAI,wBAAiB,eAAA5N,OAAA;8BAAM2N,SAAS,EAAC,+BAA+B;8BAAAC,QAAA,EAAC;4BAAqB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACtGhO,OAAA;4BAAA4N,QAAA,EAAI;0BAAuC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACX,EAGAlK,SAAS,KAAKA,SAAS,CAACsM,MAAM,KAAK,OAAO,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,CAAC,iBACpFvQ,OAAA,CAACnB,QAAQ;gBAAC8O,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,eACrC5N,OAAA;kBAAK2N,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB5N,OAAA;oBAAK2N,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,gBAC1D5N,OAAA;sBAAM2N,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnChO,OAAA;sBAAI2N,SAAS,EAAC,aAAa;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eAENhO,OAAA;oBAAK2N,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpE5N,OAAA;sBAAK2N,SAAS,EAAC,uDAAuD;sBAAAC,QAAA,EACnE/F,IAAI,CAACC,SAAS,CAAChE,SAAS,EAAE,IAAI,EAAE,CAAC;oBAAC;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACX,EAGAxL,qBAAqB,iBACpBxC,OAAA,CAACJ,iBAAiB;gBAChB4Q,OAAO,EAAEA,CAAA,KAAM/N,wBAAwB,CAAC,KAAK;cAAE;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CACF,eAEDhO,OAAA,CAACL,qBAAqB;gBACpBgE,SAAS,EAAEA,SAAU;gBACrB8M,WAAW,EAAE,CAAA9M,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+M,YAAY,KAAI,CAAC,CAAE;gBAC3CC,MAAM,EAAE3M,eAAgB;gBACxB4M,QAAQ,EAAE3M;cAAgB;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhL,KAAK,CAACE,IAAI,iBACTlD,OAAA;MAAK2N,SAAS,EAAC,wIACwD;MAAAC,QAAA,eACrE5N,OAAA;QAAK2N,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C5N,OAAA;UAAA4N,QAAA,EAAM;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACdhO,OAAA;UAAM2N,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAE5K,KAAK,CAACG;QAAO;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhO,OAAA,CAACX,YAAY;MACXyB,KAAK,EAAEA,KAAM;MACbE,KAAK,EAAEA,KAAM;MACbE,MAAM,EAAEA,MAAO;MACfE,MAAM,EAAEA,MAAO;MACfuO,QAAQ,EAAEA,CAAA,KAAM5O,QAAQ,CAAC,IAAI,CAAE;MAC/B6O,QAAQ,EAAEA,CAAA,KAAM3O,QAAQ,CAAC,IAAI,CAAE;MAC/B4O,aAAa,EAAEA,CAAA,KAAM1O,SAAS,CAAC,IAAI,CAAE;MACrC2O,aAAa,EAAEA,CAAA,KAAMzO,SAAS,CAAC,IAAI,CAAE;MACrCwP,SAAS,EAAEnO,gBAAiB;MAC5BoO,QAAQ,EAAC;IAAQ;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFhO,OAAA,CAACxB,WAAW;MAAAoP,QAAA,EACT1L,QAAQ,gBACPlC,OAAA;QAAK2N,SAAS,EAAC,8BAA8B;QAAAC,QAAA,eAC3C5N,OAAA,CAACR,eAAe;UACdsG,EAAE,EAAE5D,QAAS;UACbsE,MAAM,EAAE;YAAEG,IAAI,EAAE,eAAe;YAAEvD,IAAI,EAAE;UAAU;QAAE;UAAAyK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,GACJ;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGdhO,OAAA,CAACF,sBAAsB;MACrBiR,MAAM,EAAE1N,aAAc;MACtBmN,OAAO,EAAEA,CAAA,KAAMlN,gBAAgB,CAAC,KAAK,CAAE;MACvC0N,MAAM,EAAE5I,0BAA2B;MACnC1G,OAAO,EAAE6B,mBAAoB;MAC7B0N,aAAa,EAAE;QACbzP,SAAS;QACTV,KAAK;QACLE,KAAK;QACLI,MAAM;QACNF,MAAM;QACNI,WAAW;QACXgB;MACF;IAAE;MAAAuL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEjB,CAAC;AAAC7N,EAAA,CAxlCID,cAAc;EAAA,QACD7B,WAAW,EACLC,eAAe,EA4ClCuB,mBAAmB;AAAA;AAAAqR,EAAA,GA9CnBhR,cAAc;AA0lCpB,eAAeA,cAAc;AAAC,IAAAgR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}