/**
 * SQLConfigPage - Page de configuration de la base de données SQL Server
 * Permet de configurer dynamiquement la connexion à SQL Server
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  BrAInBIHeader,
  DarkCard,
  SectionHeader,
  PrimaryButton,
  SecondaryButton,
  DarkInput,
  DarkSpinner
} from './YellowMindUI';

const API_BASE_URL = 'http://localhost:8000';

const SQLConfigPage = () => {
  const navigate = useNavigate();
  
  // États du formulaire
  const [config, setConfig] = useState({
    server: 'localhost\\SQLSERVER',
    database: '',
    driver: 'ODBC Driver 17 for SQL Server',
    username: '',
    password: '',
    use_windows_auth: true,
    port: '',
    timeout: 30
  });

  // États de l'interface
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [currentConfig, setCurrentConfig] = useState(null);
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' });

  // Fonction utilitaire pour afficher les toasts
  const showToast = (message, type = 'success') => {
    setToast({ show: true, message, type });
    setTimeout(() => {
      setToast({ show: false, message: '', type: 'success' });
    }, 5000);
  };

  // Fonction utilitaire pour extraire les messages d'erreur
  const extractErrorMessage = (error) => {
    let errorMsg = error.response?.data?.detail || error.message;

    // Si l'erreur est un objet (erreur de validation Pydantic), la convertir en chaîne
    if (typeof errorMsg === 'object') {
      if (Array.isArray(errorMsg)) {
        errorMsg = errorMsg.map(err => {
          if (typeof err === 'object') {
            return err.msg || `${err.type}: ${err.input}` || JSON.stringify(err);
          }
          return err;
        }).join(', ');
      } else {
        errorMsg = errorMsg.msg || `${errorMsg.type}: ${errorMsg.input}` || JSON.stringify(errorMsg);
      }
    }

    return String(errorMsg);
  };

  // Charger la configuration actuelle au démarrage
  useEffect(() => {
    loadCurrentConfig();
  }, []);

  const loadCurrentConfig = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);
      if (response.data.configured) {
        setCurrentConfig(response.data.config);
        // Pré-remplir le formulaire avec la config actuelle (sans le mot de passe)
        setConfig(prev => ({
          ...prev,
          ...response.data.config,
          password: '' // Ne pas pré-remplir le mot de passe pour la sécurité
        }));
      }
    } catch (error) {
      console.error('Erreur lors du chargement de la configuration:', error);
    }
  };

  // Gérer les changements dans le formulaire
  const handleInputChange = (field, value) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    // Réinitialiser le résultat du test quand on modifie la config
    setTestResult(null);
  };

  // Tester la connexion
  const testConnection = async () => {
    if (!config.server || !config.database) {
      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');
      return;
    }

    try {
      setTesting(true);
      setTestResult(null);

      const response = await axios.post(`${API_BASE_URL}/config/sql/test`, config);
      setTestResult(response.data);

      if (response.data.success) {
        showToast(`Connexion réussie ! (${response.data.connection_time?.toFixed(2)}s)`);
      } else {
        showToast(response.data.message, 'error');
      }
    } catch (error) {
      const errorMsg = extractErrorMessage(error);

      setTestResult({
        success: false,
        message: errorMsg,
        error_code: 'REQUEST_ERROR'
      });
      showToast('Erreur lors du test: ' + errorMsg, 'error');
    } finally {
      setTesting(false);
    }
  };

  // Sauvegarder la configuration
  const saveConfiguration = async () => {
    if (!config.server || !config.database) {
      showToast('Veuillez renseigner au minimum le serveur et la base de données', 'error');
      return;
    }

    try {
      setLoading(true);

      const response = await axios.post(`${API_BASE_URL}/config/sql`, config);

      if (response.data.success) {
        showToast('Configuration sauvegardée avec succès !');
        setCurrentConfig(response.data.config);
        
        // Rediriger vers la page principale après un délai
        setTimeout(() => {
          navigate('/');
        }, 2000);
      } else {
        showToast(response.data.message, 'error');
      }
    } catch (error) {
      const errorMsg = extractErrorMessage(error);
      showToast('Erreur lors de la sauvegarde: ' + errorMsg, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 text-gray-100">
      <BrAInBIHeader hasData={false} />
      
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center">
              <span className="text-xl">⚙️</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                Configuration SQL Server
              </h1>
              <p className="text-gray-400">
                Configurez votre connexion à la base de données SQL Server
              </p>
            </div>
          </div>

          {/* État de la configuration actuelle */}
          {currentConfig && (
            <div className="bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-green-400">✅</span>
                <span className="font-medium text-green-300">Configuration actuelle</span>
              </div>
              <div className="text-sm text-gray-300">
                <div>Serveur: {currentConfig.server}</div>
                <div>Base: {currentConfig.database}</div>
                <div>Authentification: {currentConfig.use_windows_auth ? 'Windows' : 'SQL Server'}</div>
              </div>
            </div>
          )}
        </div>

        {/* Toast de notification */}
        {toast.show && (
          <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg border ${
            toast.type === 'success' 
              ? 'bg-green-900/90 border-green-700 text-green-100' 
              : 'bg-red-900/90 border-red-700 text-red-100'
          } backdrop-blur-sm`}>
            <div className="flex items-center space-x-2">
              <span>{toast.type === 'success' ? '✅' : '❌'}</span>
              <span>{toast.message}</span>
            </div>
          </div>
        )}

        {/* Formulaire de configuration */}
        <DarkCard>
          <SectionHeader
            title="Paramètres de connexion"
            subtitle="Configurez les détails de votre serveur SQL Server"
            icon="🔧"
          />

          <div className="mt-6 space-y-6">
            {/* Serveur */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Serveur SQL Server *
                </label>
                <DarkInput
                  type="text"
                  value={config.server}
                  onChange={(e) => handleInputChange('server', e.target.value)}
                  placeholder="localhost\\SQLSERVER"
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Exemple: localhost\\SQLSERVER, *************, myserver.domain.com
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Base de données *
                </label>
                <DarkInput
                  type="text"
                  value={config.database}
                  onChange={(e) => handleInputChange('database', e.target.value)}
                  placeholder="BrAInBIDemo"
                  className="w-full"
                />
              </div>
            </div>

            {/* Driver et Port */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Driver ODBC
                </label>
                <DarkInput
                  type="text"
                  value={config.driver}
                  onChange={(e) => handleInputChange('driver', e.target.value)}
                  placeholder="ODBC Driver 17 for SQL Server"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Port (optionnel)
                </label>
                <DarkInput
                  type="number"
                  value={config.port}
                  onChange={(e) => handleInputChange('port', e.target.value)}
                  placeholder="1433"
                  className="w-full"
                />
              </div>
            </div>

            {/* Type d'authentification */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-3">
                Type d'authentification
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    checked={config.use_windows_auth}
                    onChange={() => handleInputChange('use_windows_auth', true)}
                    className="text-purple-500 focus:ring-purple-500"
                  />
                  <span className="text-gray-300">Authentification Windows</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    checked={!config.use_windows_auth}
                    onChange={() => handleInputChange('use_windows_auth', false)}
                    className="text-purple-500 focus:ring-purple-500"
                  />
                  <span className="text-gray-300">Authentification SQL Server</span>
                </label>
              </div>
            </div>

            {/* Credentials SQL Server */}
            {!config.use_windows_auth && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Nom d'utilisateur
                  </label>
                  <DarkInput
                    type="text"
                    value={config.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder="sa"
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Mot de passe
                  </label>
                  <DarkInput
                    type="password"
                    value={config.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder="••••••••"
                    className="w-full"
                  />
                </div>
              </div>
            )}

            {/* Timeout */}
            <div className="w-full md:w-1/2">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Timeout de connexion (secondes)
              </label>
              <DarkInput
                type="number"
                value={config.timeout}
                onChange={(e) => handleInputChange('timeout', parseInt(e.target.value) || 30)}
                placeholder="30"
                className="w-full"
                min="5"
                max="300"
              />
            </div>
          </div>

          {/* Résultat du test */}
          {testResult && (
            <div className={`mt-6 p-4 rounded-lg border ${
              testResult.success 
                ? 'bg-green-900/20 border-green-700' 
                : 'bg-red-900/20 border-red-700'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <span>{testResult.success ? '✅' : '❌'}</span>
                <span className={`font-medium ${
                  testResult.success ? 'text-green-300' : 'text-red-300'
                }`}>
                  {testResult.success ? 'Test réussi' : 'Test échoué'}
                </span>
              </div>
              <p className="text-sm text-gray-300 mb-2">{testResult.message}</p>
              
              {testResult.success && testResult.server_info && (
                <div className="text-xs text-gray-400 space-y-1">
                  <div>Version: {testResult.server_info.version?.substring(0, 50)}...</div>
                  <div>Base actuelle: {testResult.server_info.current_database}</div>
                  <div>Temps de connexion: {testResult.connection_time?.toFixed(3)}s</div>
                </div>
              )}
              
              {!testResult.success && testResult.error_code && (
                <div className="text-xs text-red-400">
                  Code d'erreur: {testResult.error_code}
                </div>
              )}
            </div>
          )}

          {/* Boutons d'action */}
          <div className="flex space-x-4 mt-8">
            <SecondaryButton
              onClick={() => navigate('/')}
              icon="🏠"
              disabled={loading || testing}
            >
              Retour
            </SecondaryButton>
            
            <SecondaryButton
              onClick={testConnection}
              icon={testing ? null : "🔍"}
              disabled={loading || testing || !config.server || !config.database}
              loading={testing}
            >
              {testing ? 'Test en cours...' : 'Tester la connexion'}
            </SecondaryButton>
            
            <PrimaryButton
              onClick={saveConfiguration}
              icon={loading ? null : "💾"}
              disabled={loading || testing || !config.server || !config.database}
              loading={loading}
              className="flex-1"
            >
              {loading ? 'Sauvegarde...' : 'Valider la configuration'}
            </PrimaryButton>
          </div>
        </DarkCard>

        {/* Aide */}
        <DarkCard className="mt-8">
          <SectionHeader
            title="Aide à la configuration"
            subtitle="Conseils pour configurer votre connexion SQL Server"
            icon="💡"
          />
          
          <div className="mt-6 space-y-4 text-sm text-gray-400">
            <div>
              <h4 className="font-medium text-gray-300 mb-2">🔧 Formats de serveur courants :</h4>
              <ul className="space-y-1 ml-4">
                <li>• <code className="text-purple-400">localhost</code> - Instance par défaut locale</li>
                <li>• <code className="text-purple-400">localhost\\SQLEXPRESS</code> - Instance nommée locale</li>
                <li>• <code className="text-purple-400">*************</code> - Serveur distant par IP</li>
                <li>• <code className="text-purple-400">server.domain.com</code> - Serveur distant par nom</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-300 mb-2">🔐 Authentification :</h4>
              <ul className="space-y-1 ml-4">
                <li>• <strong>Windows</strong> : Utilise votre compte Windows actuel</li>
                <li>• <strong>SQL Server</strong> : Nécessite un nom d'utilisateur et mot de passe SQL</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-300 mb-2">⚠️ Dépannage :</h4>
              <ul className="space-y-1 ml-4">
                <li>• Vérifiez que SQL Server est démarré</li>
                <li>• Assurez-vous que TCP/IP est activé dans SQL Server Configuration Manager</li>
                <li>• Vérifiez les paramètres de pare-feu</li>
                <li>• Testez d'abord avec SQL Server Management Studio</li>
              </ul>
            </div>
          </div>
        </DarkCard>
      </div>
    </div>
  );
};

export default SQLConfigPage;
