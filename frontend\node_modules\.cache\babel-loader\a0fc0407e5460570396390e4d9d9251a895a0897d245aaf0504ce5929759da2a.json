{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIZekri\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Routes, Route, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport './App.css';\nimport { BrAInBIHeader, HeroTitle, PrimaryButton, SecondaryButton, DarkCard, KPICard, ChatBubble, DarkInput, DarkBadge, DarkSpinner, SectionHeader } from './components/YellowMindUI';\nimport TablesOverview from './components/TablesOverview';\nimport TablesListPage from './components/TablesListPage';\nimport TableDetailPage from './components/TableDetailPage';\nimport VisualBuilderPage from './components/VisualBuilderPage';\nimport DragVisualPage from './components/DragVisualPage';\nimport DashboardPage from './components/DashboardPage';\nimport SQLConfigPage from './components/SQLConfigPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:8000';\n\n// Composant principal de la page d'accueil\nfunction HomePage() {\n  _s();\n  var _dataInfo$total_rows, _dataInfo$columns, _dataInfo$columns2, _dataInfo$columns3, _dataInfo$sample_data, _fullSchemaData$datab, _fullSchemaData$datab2, _fullSchemaData$datab3, _fullSchemaData$datab4;\n  const navigate = useNavigate();\n  const [dataInfo, setDataInfo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [question, setQuestion] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [dataQuality, setDataQuality] = useState(null);\n  const [kpiExplanation, setKpiExplanation] = useState('');\n  const [showKpiModal, setShowKpiModal] = useState(false);\n  const [dataLoaded, setDataLoaded] = useState(false);\n  const [showTablesOverview, setShowTablesOverview] = useState(false);\n  const [fullSchemaData, setFullSchemaData] = useState(null);\n  const [sqlConfigured, setSqlConfigured] = useState(false);\n  const [sqlConfigStatus, setSqlConfigStatus] = useState(null);\n\n  // Fonction pour vérifier l'état de la configuration SQL\n  const checkSqlConfiguration = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      setSqlConfigured(response.data.configured);\n      setSqlConfigStatus(response.data.config);\n      return response.data.configured;\n    } catch (error) {\n      console.error('Erreur lors de la vérification de la configuration SQL:', error);\n      setSqlConfigured(false);\n      setSqlConfigStatus(null);\n      return false;\n    }\n  };\n\n  // Fonction pour charger les informations des données SQL Server\n  const loadDataInfo = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Vérifier d'abord la configuration SQL\n      const isConfigured = await checkSqlConfiguration();\n      if (!isConfigured) {\n        setError('Configuration SQL non définie. Veuillez configurer votre base de données.');\n        setLoading(false);\n        return;\n      }\n      const response = await axios.get(`${API_BASE_URL}/data/info`);\n      setDataInfo(response.data);\n      setDataQuality(response.data.data_quality);\n      setDataLoaded(true);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Erreur lors du chargement des données SQL Server: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n      setDataLoaded(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction pour charger l'aperçu complet des tables\n  const loadFullSchema = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/schema/full`);\n      setFullSchemaData(response.data);\n      setShowTablesOverview(true);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      setError('Erreur lors du chargement de l\\'aperçu des tables: ' + (((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger automatiquement les données au démarrage\n  React.useEffect(() => {\n    // D'abord vérifier la configuration, puis charger les données si configuré\n    checkSqlConfiguration().then(isConfigured => {\n      if (isConfigured) {\n        loadDataInfo();\n      } else {\n        setLoading(false);\n      }\n    });\n  }, []);\n\n  // Poser une question personnalisée\n  const handleAskQuestion = async () => {\n    if (!question.trim()) {\n      setError('Veuillez saisir une question');\n      return;\n    }\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n    setLoading(true);\n    setError('');\n\n    // Ajouter la question de l'utilisateur au chat\n    const newHistory = [...chatHistory, {\n      type: 'user',\n      content: question,\n      timestamp: new Date().toLocaleTimeString()\n    }];\n    setChatHistory(newHistory);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/ask`, {\n        question: question\n      });\n\n      // Ajouter la réponse de l'IA au chat\n      setChatHistory([...newHistory, {\n        type: 'ai',\n        content: response.data.analysis,\n        chart: response.data.chart_data,\n        chartJson: response.data.chart_json,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n      setQuestion('');\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      setError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || 'Erreur lors de l\\'analyse');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mode pédagogique - Expliquer un KPI\n  const handleExplainKPI = async kpiName => {\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await axios.post(`${API_BASE_URL}/explain-kpi`, {\n        kpi_name: kpiName,\n        context: `Données de la table SQL Server ${(dataInfo === null || dataInfo === void 0 ? void 0 : dataInfo.table_name) || 'Sales'}`\n      });\n      setKpiExplanation(response.data.analysis);\n      setShowKpiModal(true);\n\n      // Ajouter à l'historique du chat\n      setChatHistory(prev => [...prev, {\n        type: 'pedagogical',\n        content: response.data.analysis,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n    } catch (err) {\n      var _err$response4, _err$response4$data;\n      setError(((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.detail) || 'Erreur lors de l\\'explication');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effacer l'historique de conversation\n  const clearHistory = async () => {\n    try {\n      await axios.delete(`${API_BASE_URL}/conversation/history`);\n      setChatHistory([]);\n    } catch (err) {\n      console.error('Erreur lors de l\\'effacement de l\\'historique:', err);\n    }\n  };\n\n  // Rendu d'un graphique dynamique\n  const renderDynamicChart = chartData => {\n    if (!chartData) return null;\n    const plotData = [];\n    const layout = {\n      title: chartData.title || 'Graphique',\n      autosize: true,\n      height: 400,\n      margin: {\n        l: 50,\n        r: 50,\n        t: 50,\n        b: 50\n      }\n    };\n    switch (chartData.type) {\n      case 'bar':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'pie':\n        plotData.push({\n          labels: chartData.labels,\n          values: chartData.values,\n          type: 'pie',\n          marker: {\n            colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']\n          }\n        });\n        break;\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: {\n            color: '#667eea',\n            size: 8\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || ''\n        };\n        break;\n      case 'histogram':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: {\n            color: '#667eea'\n          }\n        });\n        layout.xaxis = {\n          title: chartData.xlabel || ''\n        };\n        layout.yaxis = {\n          title: chartData.ylabel || 'Fréquence'\n        };\n        break;\n      default:\n        return /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Type de graphique non support\\xE9: \", chartData.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 16\n        }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Plot, {\n      data: plotData,\n      layout: layout,\n      config: {\n        responsive: true\n      },\n      style: {\n        width: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-950 text-gray-100 fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(BrAInBIHeader, {\n      hasData: dataLoaded,\n      sqlConfigured: sqlConfigured,\n      sqlConfig: sqlConfigStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"slide-in-top\",\n        children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n          title: \"BrAInBI\",\n          subtitle: \"Explore your Data. Amplified by AI.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/brAInBI-logo.png\",\n            alt: \"BrAInBI Logo\",\n            className: \"w-24 h-24 mx-auto mb-4 rounded-2xl shadow-lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-300 max-w-3xl mx-auto\",\n            children: \"Plateforme de Business Intelligence augment\\xE9e par l'IA pour explorer, comprendre et visualiser vos donn\\xE9es simplement via le langage naturel.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this), !dataLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDDC4\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-100 mb-4\",\n              children: sqlConfigured ? 'Connexion SQL Server' : 'Configuration SQL Server'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 mb-8 max-w-md mx-auto\",\n              children: sqlConfigured ? 'BrAInBI se connecte à votre base de données SQL Server en mode live' : 'Configurez votre connexion à SQL Server pour commencer'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), sqlConfigured && sqlConfigStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6 max-w-md mx-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-400\",\n                  children: \"\\u2705\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-300\",\n                  children: \"Configuration active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Serveur: \", sqlConfigStatus.server]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Base: \", sqlConfigStatus.database]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-3 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-400\",\n                children: sqlConfigured ? 'Connexion en cours...' : 'Vérification de la configuration...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-red-400 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u274C \", sqlConfigured ? 'Erreur de connexion' : 'Configuration requise']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm mt-2 max-w-md mx-auto\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 21\n              }, this), sqlConfigured ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4 justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                  onClick: () => navigate('/config/sql'),\n                  icon: \"\\u2699\\uFE0F\",\n                  className: \"text-lg px-6 py-3\",\n                  children: \"Reconfigurer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                  onClick: loadDataInfo,\n                  icon: \"\\uD83D\\uDD04\",\n                  className: \"text-lg px-6 py-3\",\n                  children: \"R\\xE9essayer\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: () => navigate('/config/sql'),\n                icon: \"\\u2699\\uFE0F\",\n                className: \"text-lg px-8 py-4\",\n                children: \"Configurer SQL Server\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-green-400 mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [\"\\u2705 \", sqlConfigured ? 'Connexion établie' : 'Prêt à configurer']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: loadDataInfo,\n                icon: \"\\uD83D\\uDE80\",\n                className: \"text-lg px-8 py-4\",\n                children: \"Charger les donn\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), dataLoaded && dataInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n          title: \"Aper\\xE7u des donn\\xE9es SQL Server\",\n          subtitle: \"Statistiques principales de votre base de donn\\xE9es\",\n          icon: \"\\uD83D\\uDCC8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Lignes totales\",\n            value: ((_dataInfo$total_rows = dataInfo.total_rows) === null || _dataInfo$total_rows === void 0 ? void 0 : _dataInfo$total_rows.toLocaleString()) || '0',\n            icon: \"\\uD83D\\uDCCA\",\n            trend: \"up\",\n            change: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Colonnes\",\n            value: ((_dataInfo$columns = dataInfo.columns) === null || _dataInfo$columns === void 0 ? void 0 : _dataInfo$columns.length) || '0',\n            icon: \"\\uD83D\\uDCCB\",\n            trend: \"neutral\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Tables\",\n            value: `${dataInfo.tables_count || 0} tables`,\n            icon: \"\\uD83D\\uDDC4\\uFE0F\",\n            trend: \"neutral\",\n            onClick: () => navigate('/tables'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Visualisations\",\n            value: \"Drag & Drop\",\n            icon: \"\\uD83C\\uDFAF\",\n            trend: \"up\",\n            change: \"Nouveau\",\n            onClick: () => navigate('/drag-visual'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Dashboard\",\n            value: \"Mes Visuels\",\n            icon: \"\\uD83D\\uDCCA\",\n            trend: \"up\",\n            change: \"G\\xE9rer\",\n            onClick: () => navigate('/dashboard'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Configuration\",\n            value: \"SQL Server\",\n            icon: \"\\u2699\\uFE0F\",\n            trend: \"neutral\",\n            change: \"Configurer\",\n            onClick: () => navigate('/config/sql'),\n            className: \"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(KPICard, {\n            title: \"Statut\",\n            value: \"Connect\\xE9\",\n            icon: \"\\u2705\",\n            trend: \"up\",\n            change: \"Live\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n              title: \"Structure des donn\\xE9es\",\n              subtitle: \"Explorez les colonnes et la structure compl\\xE8te de la base\",\n              icon: \"\\uD83C\\uDFF7\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/drag-visual'),\n                disabled: loading,\n                icon: \"\\uD83C\\uDFAF\",\n                className: \"!py-2 !px-4\",\n                children: \"Drag & Drop\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/visual-builder'),\n                disabled: loading,\n                icon: \"\\uD83D\\uDCCA\",\n                className: \"!py-2 !px-4\",\n                children: \"Visual Builder\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/dashboard'),\n                disabled: loading,\n                icon: \"\\uD83D\\uDCCB\",\n                className: \"!py-2 !px-4\",\n                children: \"Mes Visuels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => navigate('/config/sql'),\n                disabled: loading,\n                icon: \"\\u2699\\uFE0F\",\n                className: \"!py-2 !px-4\",\n                children: \"Configuration SQL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n                onClick: () => navigate('/tables'),\n                disabled: loading,\n                icon: \"\\uD83D\\uDD0D\",\n                className: \"!py-2 !px-4\",\n                children: \"Explorer les tables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-3\",\n            children: dataInfo.tables && Object.keys(dataInfo.tables).length > 0 ? Object.entries(dataInfo.tables).slice(0, 1).map(([tableName, tableData]) => {\n              var _tableData$columns;\n              return (_tableData$columns = tableData.columns) === null || _tableData$columns === void 0 ? void 0 : _tableData$columns.map((col, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(DarkBadge, {\n                  variant: \"info\",\n                  className: \"cursor-pointer hover:bg-blue-800/70 transition-colors\",\n                  onClick: () => handleExplainKPI(col),\n                  children: col\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                  onClick: () => handleExplainKPI(col),\n                  className: \"!p-1 !min-w-0 w-6 h-6 text-xs\",\n                  icon: \"?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 25\n                }, this)]\n              }, `${tableName}-${index}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 23\n              }, this));\n            }) : (_dataInfo$columns2 = dataInfo.columns) === null || _dataInfo$columns2 === void 0 ? void 0 : _dataInfo$columns2.map((col, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(DarkBadge, {\n                variant: \"info\",\n                className: \"cursor-pointer hover:bg-blue-800/70 transition-colors\",\n                onClick: () => handleExplainKPI(col),\n                children: col\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => handleExplainKPI(col),\n                className: \"!p-1 !min-w-0 w-6 h-6 text-xs\",\n                icon: \"?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this), dataQuality && /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"mb-8 border-yellow-500/30 bg-yellow-900/10\",\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Qualit\\xE9 des donn\\xE9es\",\n            subtitle: \"Analyse automatique de la qualit\\xE9\",\n            icon: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300 whitespace-pre-wrap leading-relaxed\",\n            children: dataQuality\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(DarkCard, {\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Aper\\xE7u des donn\\xE9es SQL Server\",\n            subtitle: \"5 premi\\xE8res lignes de votre table\",\n            icon: \"\\uD83D\\uDC41\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b border-gray-700\",\n                  children: (_dataInfo$columns3 = dataInfo.columns) === null || _dataInfo$columns3 === void 0 ? void 0 : _dataInfo$columns3.map((col, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left py-3 px-4 font-semibold text-gray-300 bg-gray-800/50\",\n                    children: col\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: (_dataInfo$sample_data = dataInfo.sample_data) === null || _dataInfo$sample_data === void 0 ? void 0 : _dataInfo$sample_data.map((row, rowIndex) => {\n                  var _dataInfo$columns4;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"border-b border-gray-800 hover:bg-gray-800/30\",\n                    children: (_dataInfo$columns4 = dataInfo.columns) === null || _dataInfo$columns4 === void 0 ? void 0 : _dataInfo$columns4.map((col, colIndex) => /*#__PURE__*/_jsxDEV(\"td\", {\n                      className: \"py-3 px-4 text-gray-400\",\n                      children: row[col] || 'N/A'\n                    }, colIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 27\n                    }, this))\n                  }, rowIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 11\n      }, this), showTablesOverview && fullSchemaData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-900 rounded-xl max-w-6xl w-full min-h-[90vh] my-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sticky top-0 bg-gray-900 rounded-t-xl border-b border-gray-800 z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-100\",\n                  children: \"Aper\\xE7u des Tables\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-400 mt-1\",\n                  children: [\"Base de donn\\xE9es: \", (_fullSchemaData$datab = fullSchemaData.database_info) === null || _fullSchemaData$datab === void 0 ? void 0 : _fullSchemaData$datab.name, \" \\u2022 \", (_fullSchemaData$datab2 = fullSchemaData.database_info) === null || _fullSchemaData$datab2 === void 0 ? void 0 : _fullSchemaData$datab2.tables_count, \" tables \\u2022 \", (_fullSchemaData$datab3 = fullSchemaData.database_info) === null || _fullSchemaData$datab3 === void 0 ? void 0 : (_fullSchemaData$datab4 = _fullSchemaData$datab3.total_rows) === null || _fullSchemaData$datab4 === void 0 ? void 0 : _fullSchemaData$datab4.toLocaleString(), \" lignes totales\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(SecondaryButton, {\n                onClick: () => setShowTablesOverview(false),\n                icon: \"\\u2715\",\n                className: \"!p-2 hover:bg-gray-800 transition-colors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(TablesOverview, {\n              data: fullSchemaData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this), dataLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n            title: \"Assistant IA BrAInBI\",\n            subtitle: \"Posez vos questions sur vos donn\\xE9es en langage naturel\",\n            icon: \"\\uD83D\\uDCAC\",\n            action: /*#__PURE__*/_jsxDEV(SecondaryButton, {\n              onClick: clearHistory,\n              disabled: loading || chatHistory.length === 0,\n              icon: \"\\uD83D\\uDDD1\\uFE0F\",\n              className: \"!py-2 !px-4\",\n              children: \"Effacer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 654,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-96 overflow-y-auto mb-6 p-4 bg-gray-900/30 rounded-lg border border-gray-800\",\n            children: chatHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center justify-center h-full text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-2xl mb-4 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl\",\n                  children: \"\\uD83E\\uDD16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-300 mb-2\",\n                children: \"Pr\\xEAt \\xE0 analyser vos donn\\xE9es avec BrAInBI\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500 max-w-md\",\n                children: \"Posez votre premi\\xE8re question pour commencer l'analyse intelligente de vos donn\\xE9es\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: chatHistory.map((message, index) => /*#__PURE__*/_jsxDEV(ChatBubble, {\n                type: message.type,\n                timestamp: message.timestamp,\n                children: [message.content, message.chart && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(Plot, {\n                    data: message.chart.data,\n                    layout: {\n                      ...message.chart.layout,\n                      autosize: true,\n                      height: 400,\n                      paper_bgcolor: 'rgba(0,0,0,0)',\n                      plot_bgcolor: 'rgba(0,0,0,0)',\n                      font: {\n                        color: '#F3F4F6'\n                      }\n                    },\n                    config: {\n                      responsive: true\n                    },\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 27\n                }, this), message.chartJson && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\",\n                  children: renderDynamicChart(JSON.parse(message.chartJson))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 27\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-400 mb-3\",\n              children: \"\\uD83D\\uDCA1 Suggestions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: ['Montre-moi un graphique en barres', 'Analyse les tendances', 'Y a-t-il des anomalies ?', 'Crée un diagramme circulaire', 'Résume les données'].map((suggestion, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setQuestion(suggestion),\n                disabled: loading || !dataLoaded,\n                className: \"px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full border border-gray-700 hover:border-gray-600 transition-colors disabled:opacity-50\",\n                children: suggestion\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(DarkInput, {\n                placeholder: \"Posez votre question sur les donn\\xE9es...\",\n                value: question,\n                onChange: e => setQuestion(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && !loading && dataLoaded && question.trim() && handleAskQuestion(),\n                disabled: loading || !dataLoaded,\n                icon: \"\\uD83D\\uDCAC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(PrimaryButton, {\n              onClick: handleAskQuestion,\n              disabled: loading || !dataLoaded || !question.trim(),\n              loading: loading,\n              icon: !loading ? \"🚀\" : null,\n              className: \"!py-3\",\n              children: \"Envoyer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 11\n      }, this), showKpiModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/_jsxDEV(DarkCard, {\n          className: \"max-w-2xl w-full max-h-[80vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-100\",\n              children: \"\\uD83D\\uDCDA Explication p\\xE9dagogique\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowKpiModal(false),\n              className: \"w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors\",\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-300 whitespace-pre-wrap leading-relaxed\",\n            children: kpiExplanation\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 776,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 11\n      }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-lg p-4 shadow-xl z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(DarkSpinner, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 799,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-300 font-medium\",\n            children: \"Traitement en cours...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 290,\n    columnNumber: 5\n  }, this);\n}\n\n// Composant App principal avec routing\n_s(HomePage, \"Tlzo+rgrhCtlUbo8C9SMbsK6Jdo=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/tables\",\n      element: /*#__PURE__*/_jsxDEV(TablesListPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/table/:tableName\",\n      element: /*#__PURE__*/_jsxDEV(TableDetailPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 48\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/visual-builder\",\n      element: /*#__PURE__*/_jsxDEV(VisualBuilderPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 46\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/drag-visual\",\n      element: /*#__PURE__*/_jsxDEV(DragVisualPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 41\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 820,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/mes-visuels\",\n      element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/config/sql\",\n      element: /*#__PURE__*/_jsxDEV(SQLConfigPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 42\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 822,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/configuration\",\n      element: /*#__PURE__*/_jsxDEV(SQLConfigPage, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 45\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 814,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"HomePage\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "Routes", "Route", "useNavigate", "axios", "Plot", "BrAInBIHeader", "<PERSON><PERSON><PERSON><PERSON>", "PrimaryButton", "SecondaryButton", "DarkCard", "KPICard", "ChatBubble", "DarkInput", "DarkBadge", "Dark<PERSON><PERSON>ner", "SectionHeader", "TablesOverview", "TablesListPage", "TableDetailPage", "VisualBuilderPage", "DragVisualPage", "DashboardPage", "SQLConfigPage", "jsxDEV", "_jsxDEV", "API_BASE_URL", "HomePage", "_s", "_dataInfo$total_rows", "_dataInfo$columns", "_dataInfo$columns2", "_dataInfo$columns3", "_dataInfo$sample_data", "_fullSchemaData$datab", "_fullSchemaData$datab2", "_fullSchemaData$datab3", "_fullSchemaData$datab4", "navigate", "dataInfo", "setDataInfo", "loading", "setLoading", "error", "setError", "question", "setQuestion", "chatHistory", "setChatHistory", "dataQuality", "setDataQuality", "kpiExplanation", "setKpiExplanation", "showKpiModal", "setShowKpiModal", "dataLoaded", "setDataLoaded", "showTablesOverview", "setShowTablesOverview", "fullSchemaData", "setFullSchemaData", "sqlConfigured", "setSqlConfigured", "sqlConfigStatus", "setSqlConfigStatus", "checkSqlConfiguration", "response", "get", "data", "configured", "config", "console", "loadDataInfo", "isConfigured", "data_quality", "err", "_err$response", "_err$response$data", "detail", "message", "loadFullSchema", "_err$response2", "_err$response2$data", "useEffect", "then", "handleAskQuestion", "trim", "newHistory", "type", "content", "timestamp", "Date", "toLocaleTimeString", "post", "analysis", "chart", "chart_data", "chartJson", "chart_json", "_err$response3", "_err$response3$data", "handleExplainKPI", "kpiName", "kpi_name", "context", "table_name", "prev", "_err$response4", "_err$response4$data", "clearHistory", "delete", "renderDynamicChart", "chartData", "plotData", "layout", "title", "autosize", "height", "margin", "l", "r", "t", "b", "push", "x", "y", "marker", "color", "xaxis", "xlabel", "yaxis", "ylabel", "labels", "values", "colors", "mode", "line", "size", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "responsive", "style", "width", "className", "hasData", "sqlConfig", "subtitle", "src", "alt", "server", "database", "onClick", "icon", "value", "total_rows", "toLocaleString", "trend", "change", "columns", "length", "tables_count", "disabled", "tables", "Object", "keys", "entries", "slice", "map", "tableName", "tableData", "_tableData$columns", "col", "index", "variant", "sample_data", "row", "rowIndex", "_dataInfo$columns4", "colIndex", "database_info", "name", "action", "paper_bgcolor", "plot_bgcolor", "font", "JSON", "parse", "suggestion", "placeholder", "onChange", "e", "target", "onKeyPress", "key", "_c", "App", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/AIZekri/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Routes, Route, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport Plot from 'react-plotly.js';\nimport './App.css';\nimport {\n  BrA<PERSON>nBIHeader,\n  HeroTitle,\n  PrimaryButton,\n  SecondaryButton,\n  DarkCard,\n  KPICard,\n  ChatBubble,\n  DarkInput,\n  DarkBadge,\n  DarkSpinner,\n  SectionHeader\n} from './components/YellowMindUI';\nimport TablesOverview from './components/TablesOverview';\nimport TablesListPage from './components/TablesListPage';\nimport TableDetailPage from './components/TableDetailPage';\nimport VisualBuilderPage from './components/VisualBuilderPage';\nimport DragVisualPage from './components/DragVisualPage';\nimport DashboardPage from './components/DashboardPage';\nimport SQLConfigPage from './components/SQLConfigPage';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\n// Composant principal de la page d'accueil\nfunction HomePage() {\n  const navigate = useNavigate();\n  const [dataInfo, setDataInfo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [question, setQuestion] = useState('');\n  const [chatHistory, setChatHistory] = useState([]);\n  const [dataQuality, setDataQuality] = useState(null);\n  const [kpiExplanation, setKpiExplanation] = useState('');\n  const [showKpiModal, setShowKpiModal] = useState(false);\n  const [dataLoaded, setDataLoaded] = useState(false);\n  const [showTablesOverview, setShowTablesOverview] = useState(false);\n  const [fullSchemaData, setFullSchemaData] = useState(null);\n  const [sqlConfigured, setSqlConfigured] = useState(false);\n  const [sqlConfigStatus, setSqlConfigStatus] = useState(null);\n\n  // Fonction pour vérifier l'état de la configuration SQL\n  const checkSqlConfiguration = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/config/sql/current`);\n      setSqlConfigured(response.data.configured);\n      setSqlConfigStatus(response.data.config);\n      return response.data.configured;\n    } catch (error) {\n      console.error('Erreur lors de la vérification de la configuration SQL:', error);\n      setSqlConfigured(false);\n      setSqlConfigStatus(null);\n      return false;\n    }\n  };\n\n  // Fonction pour charger les informations des données SQL Server\n  const loadDataInfo = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Vérifier d'abord la configuration SQL\n      const isConfigured = await checkSqlConfiguration();\n      if (!isConfigured) {\n        setError('Configuration SQL non définie. Veuillez configurer votre base de données.');\n        setLoading(false);\n        return;\n      }\n\n      const response = await axios.get(`${API_BASE_URL}/data/info`);\n      setDataInfo(response.data);\n      setDataQuality(response.data.data_quality);\n      setDataLoaded(true);\n\n    } catch (err) {\n      setError('Erreur lors du chargement des données SQL Server: ' + (err.response?.data?.detail || err.message));\n      setDataLoaded(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fonction pour charger l'aperçu complet des tables\n  const loadFullSchema = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/schema/full`);\n      setFullSchemaData(response.data);\n      setShowTablesOverview(true);\n    } catch (err) {\n      setError('Erreur lors du chargement de l\\'aperçu des tables: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger automatiquement les données au démarrage\n  React.useEffect(() => {\n    // D'abord vérifier la configuration, puis charger les données si configuré\n    checkSqlConfiguration().then(isConfigured => {\n      if (isConfigured) {\n        loadDataInfo();\n      } else {\n        setLoading(false);\n      }\n    });\n  }, []);\n\n  // Poser une question personnalisée\n  const handleAskQuestion = async () => {\n    if (!question.trim()) {\n      setError('Veuillez saisir une question');\n      return;\n    }\n\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    // Ajouter la question de l'utilisateur au chat\n    const newHistory = [...chatHistory, {\n      type: 'user',\n      content: question,\n      timestamp: new Date().toLocaleTimeString()\n    }];\n    setChatHistory(newHistory);\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/ask`, {\n        question: question\n      });\n\n      // Ajouter la réponse de l'IA au chat\n      setChatHistory([...newHistory, {\n        type: 'ai',\n        content: response.data.analysis,\n        chart: response.data.chart_data,\n        chartJson: response.data.chart_json,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n\n      setQuestion('');\n\n    } catch (err) {\n      setError(err.response?.data?.detail || 'Erreur lors de l\\'analyse');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mode pédagogique - Expliquer un KPI\n  const handleExplainKPI = async (kpiName) => {\n    if (!dataLoaded) {\n      setError('Données SQL Server non disponibles');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await axios.post(`${API_BASE_URL}/explain-kpi`, {\n        kpi_name: kpiName,\n        context: `Données de la table SQL Server ${dataInfo?.table_name || 'Sales'}`\n      });\n\n      setKpiExplanation(response.data.analysis);\n      setShowKpiModal(true);\n\n      // Ajouter à l'historique du chat\n      setChatHistory(prev => [...prev, {\n        type: 'pedagogical',\n        content: response.data.analysis,\n        timestamp: new Date().toLocaleTimeString()\n      }]);\n\n    } catch (err) {\n      setError(err.response?.data?.detail || 'Erreur lors de l\\'explication');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Effacer l'historique de conversation\n  const clearHistory = async () => {\n    try {\n      await axios.delete(`${API_BASE_URL}/conversation/history`);\n      setChatHistory([]);\n    } catch (err) {\n      console.error('Erreur lors de l\\'effacement de l\\'historique:', err);\n    }\n  };\n\n  // Rendu d'un graphique dynamique\n  const renderDynamicChart = (chartData) => {\n    if (!chartData) return null;\n\n    const plotData = [];\n    const layout = {\n      title: chartData.title || 'Graphique',\n      autosize: true,\n      height: 400,\n      margin: { l: 50, r: 50, t: 50, b: 50 }\n    };\n\n    switch (chartData.type) {\n      case 'bar':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'pie':\n        plotData.push({\n          labels: chartData.labels,\n          values: chartData.values,\n          type: 'pie',\n          marker: { colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'] }\n        });\n        break;\n\n      case 'line':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'lines+markers',\n          line: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'scatter':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'scatter',\n          mode: 'markers',\n          marker: { color: '#667eea', size: 8 }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || '' };\n        break;\n\n      case 'histogram':\n        plotData.push({\n          x: chartData.x,\n          y: chartData.y,\n          type: 'bar',\n          marker: { color: '#667eea' }\n        });\n        layout.xaxis = { title: chartData.xlabel || '' };\n        layout.yaxis = { title: chartData.ylabel || 'Fréquence' };\n        break;\n\n      default:\n        return <p>Type de graphique non supporté: {chartData.type}</p>;\n    }\n\n    return (\n      <Plot\n        data={plotData}\n        layout={layout}\n        config={{ responsive: true }}\n        style={{ width: '100%' }}\n      />\n    );\n  };\n\n\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gray-950 text-gray-100 fade-in\">\n      {/* Header BrAInBI */}\n      <BrAInBIHeader\n        hasData={dataLoaded}\n        sqlConfigured={sqlConfigured}\n        sqlConfig={sqlConfigStatus}\n      />\n\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Hero Section */}\n        <div className=\"slide-in-top\">\n          <HeroTitle\n            title=\"BrAInBI\"\n            subtitle=\"Explore your Data. Amplified by AI.\"\n          />\n          <div className=\"text-center mb-8\">\n            <img\n              src=\"/brAInBI-logo.png\"\n              alt=\"BrAInBI Logo\"\n              className=\"w-24 h-24 mx-auto mb-4 rounded-2xl shadow-lg\"\n            />\n            <p className=\"text-lg text-gray-300 max-w-3xl mx-auto\">\n              Plateforme de Business Intelligence augmentée par l'IA pour explorer, comprendre et visualiser vos données simplement via le langage naturel.\n            </p>\n          </div>\n        </div>\n\n        {/* SQL Server Status Section */}\n        {!dataLoaded && (\n          <div className=\"mb-12\">\n            <DarkCard className=\"text-center\">\n              <div className=\"py-12\">\n                <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mx-auto mb-6 flex items-center justify-center\">\n                  <span className=\"text-2xl\">🗄️</span>\n                </div>\n\n                <h3 className=\"text-xl font-semibold text-gray-100 mb-4\">\n                  {sqlConfigured ? 'Connexion SQL Server' : 'Configuration SQL Server'}\n                </h3>\n                <p className=\"text-gray-400 mb-8 max-w-md mx-auto\">\n                  {sqlConfigured\n                    ? 'BrAInBI se connecte à votre base de données SQL Server en mode live'\n                    : 'Configurez votre connexion à SQL Server pour commencer'\n                  }\n                </p>\n\n                {/* Affichage de la configuration actuelle */}\n                {sqlConfigured && sqlConfigStatus && (\n                  <div className=\"bg-green-900/20 border border-green-700 rounded-lg p-4 mb-6 max-w-md mx-auto\">\n                    <div className=\"flex items-center space-x-2 mb-2\">\n                      <span className=\"text-green-400\">✅</span>\n                      <span className=\"font-medium text-green-300\">Configuration active</span>\n                    </div>\n                    <div className=\"text-sm text-gray-300\">\n                      <div>Serveur: {sqlConfigStatus.server}</div>\n                      <div>Base: {sqlConfigStatus.database}</div>\n                    </div>\n                  </div>\n                )}\n\n                {loading ? (\n                  <div className=\"flex items-center justify-center space-x-3 mb-6\">\n                    <DarkSpinner />\n                    <span className=\"text-gray-400\">\n                      {sqlConfigured ? 'Connexion en cours...' : 'Vérification de la configuration...'}\n                    </span>\n                  </div>\n                ) : error ? (\n                  <div className=\"mb-6\">\n                    <div className=\"text-red-400 mb-4\">\n                      <p>❌ {sqlConfigured ? 'Erreur de connexion' : 'Configuration requise'}</p>\n                      <p className=\"text-sm mt-2 max-w-md mx-auto\">{error}</p>\n                    </div>\n                    {sqlConfigured ? (\n                      <div className=\"flex space-x-4 justify-center\">\n                        <SecondaryButton\n                          onClick={() => navigate('/config/sql')}\n                          icon=\"⚙️\"\n                          className=\"text-lg px-6 py-3\"\n                        >\n                          Reconfigurer\n                        </SecondaryButton>\n                        <PrimaryButton\n                          onClick={loadDataInfo}\n                          icon=\"🔄\"\n                          className=\"text-lg px-6 py-3\"\n                        >\n                          Réessayer\n                        </PrimaryButton>\n                      </div>\n                    ) : (\n                      <PrimaryButton\n                        onClick={() => navigate('/config/sql')}\n                        icon=\"⚙️\"\n                        className=\"text-lg px-8 py-4\"\n                      >\n                        Configurer SQL Server\n                      </PrimaryButton>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"mb-6\">\n                    <div className=\"text-green-400 mb-4\">\n                      <p>✅ {sqlConfigured ? 'Connexion établie' : 'Prêt à configurer'}</p>\n                    </div>\n                    <PrimaryButton\n                      onClick={loadDataInfo}\n                      icon=\"🚀\"\n                      className=\"text-lg px-8 py-4\"\n                    >\n                      Charger les données\n                    </PrimaryButton>\n                  </div>\n                )}\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n\n\n        {/* KPIs Section */}\n        {dataLoaded && dataInfo && (\n          <div className=\"mb-12\">\n            <SectionHeader\n              title=\"Aperçu des données SQL Server\"\n              subtitle=\"Statistiques principales de votre base de données\"\n              icon=\"📈\"\n            />\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n              <KPICard\n                title=\"Lignes totales\"\n                value={dataInfo.total_rows?.toLocaleString() || '0'}\n                icon=\"📊\"\n                trend=\"up\"\n                change=\"Live\"\n              />\n              <KPICard\n                title=\"Colonnes\"\n                value={dataInfo.columns?.length || '0'}\n                icon=\"📋\"\n                trend=\"neutral\"\n              />\n              <KPICard\n                title=\"Tables\"\n                value={`${dataInfo.tables_count || 0} tables`}\n                icon=\"🗄️\"\n                trend=\"neutral\"\n                onClick={() => navigate('/tables')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Visualisations\"\n                value=\"Drag & Drop\"\n                icon=\"🎯\"\n                trend=\"up\"\n                change=\"Nouveau\"\n                onClick={() => navigate('/drag-visual')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Dashboard\"\n                value=\"Mes Visuels\"\n                icon=\"📊\"\n                trend=\"up\"\n                change=\"Gérer\"\n                onClick={() => navigate('/dashboard')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Configuration\"\n                value=\"SQL Server\"\n                icon=\"⚙️\"\n                trend=\"neutral\"\n                change=\"Configurer\"\n                onClick={() => navigate('/config/sql')}\n                className=\"cursor-pointer hover:bg-gray-800/50 transition-colors\"\n              />\n              <KPICard\n                title=\"Statut\"\n                value=\"Connecté\"\n                icon=\"✅\"\n                trend=\"up\"\n                change=\"Live\"\n              />\n            </div>\n\n            {/* Structure des données et exploration des tables */}\n            <DarkCard className=\"mb-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <SectionHeader\n                  title=\"Structure des données\"\n                  subtitle=\"Explorez les colonnes et la structure complète de la base\"\n                  icon=\"🏷️\"\n                />\n                <div className=\"flex space-x-3\">\n                  <SecondaryButton\n                    onClick={() => navigate('/drag-visual')}\n                    disabled={loading}\n                    icon=\"🎯\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Drag & Drop\n                  </SecondaryButton>\n                  <SecondaryButton\n                    onClick={() => navigate('/visual-builder')}\n                    disabled={loading}\n                    icon=\"📊\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Visual Builder\n                  </SecondaryButton>\n                  <SecondaryButton\n                    onClick={() => navigate('/dashboard')}\n                    disabled={loading}\n                    icon=\"📋\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Mes Visuels\n                  </SecondaryButton>\n                  <SecondaryButton\n                    onClick={() => navigate('/config/sql')}\n                    disabled={loading}\n                    icon=\"⚙️\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Configuration SQL\n                  </SecondaryButton>\n                  <PrimaryButton\n                    onClick={() => navigate('/tables')}\n                    disabled={loading}\n                    icon=\"🔍\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Explorer les tables\n                  </PrimaryButton>\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-3\">\n                {dataInfo.tables && Object.keys(dataInfo.tables).length > 0 ? (\n                  Object.entries(dataInfo.tables).slice(0, 1).map(([tableName, tableData]) =>\n                    tableData.columns?.map((col, index) => (\n                      <div key={`${tableName}-${index}`} className=\"flex items-center space-x-2\">\n                        <DarkBadge\n                          variant=\"info\"\n                          className=\"cursor-pointer hover:bg-blue-800/70 transition-colors\"\n                          onClick={() => handleExplainKPI(col)}\n                        >\n                          {col}\n                        </DarkBadge>\n                        <SecondaryButton\n                          onClick={() => handleExplainKPI(col)}\n                          className=\"!p-1 !min-w-0 w-6 h-6 text-xs\"\n                          icon=\"?\"\n                        />\n                      </div>\n                    ))\n                  )\n                ) : (\n                  dataInfo.columns?.map((col, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <DarkBadge\n                        variant=\"info\"\n                        className=\"cursor-pointer hover:bg-blue-800/70 transition-colors\"\n                        onClick={() => handleExplainKPI(col)}\n                      >\n                        {col}\n                      </DarkBadge>\n                      <SecondaryButton\n                        onClick={() => handleExplainKPI(col)}\n                        className=\"!p-1 !min-w-0 w-6 h-6 text-xs\"\n                        icon=\"?\"\n                      />\n                    </div>\n                  ))\n                )}\n              </div>\n            </DarkCard>\n\n            {/* Qualité des données */}\n            {dataQuality && (\n              <DarkCard className=\"mb-8 border-yellow-500/30 bg-yellow-900/10\">\n                <SectionHeader\n                  title=\"Qualité des données\"\n                  subtitle=\"Analyse automatique de la qualité\"\n                  icon=\"🔍\"\n                />\n                <div className=\"text-gray-300 whitespace-pre-wrap leading-relaxed\">\n                  {dataQuality}\n                </div>\n              </DarkCard>\n            )}\n\n            {/* Aperçu du tableau */}\n            <DarkCard>\n              <SectionHeader\n                title=\"Aperçu des données SQL Server\"\n                subtitle=\"5 premières lignes de votre table\"\n                icon=\"👁️\"\n              />\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full text-sm\">\n                  <thead>\n                    <tr className=\"border-b border-gray-700\">\n                      {dataInfo.columns?.map((col, index) => (\n                        <th key={index} className=\"text-left py-3 px-4 font-semibold text-gray-300 bg-gray-800/50\">\n                          {col}\n                        </th>\n                      ))}\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {dataInfo.sample_data?.map((row, rowIndex) => (\n                      <tr key={rowIndex} className=\"border-b border-gray-800 hover:bg-gray-800/30\">\n                        {dataInfo.columns?.map((col, colIndex) => (\n                          <td key={colIndex} className=\"py-3 px-4 text-gray-400\">\n                            {row[col] || 'N/A'}\n                          </td>\n                        ))}\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Tables Overview Modal/Section */}\n        {showTablesOverview && fullSchemaData && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center p-4 overflow-y-auto\">\n            <div className=\"bg-gray-900 rounded-xl max-w-6xl w-full min-h-[90vh] my-4\">\n              {/* Header fixe */}\n              <div className=\"sticky top-0 bg-gray-900 rounded-t-xl border-b border-gray-800 z-10\">\n                <div className=\"flex items-center justify-between p-6\">\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-gray-100\">Aperçu des Tables</h2>\n                    <p className=\"text-gray-400 mt-1\">\n                      Base de données: {fullSchemaData.database_info?.name} • {fullSchemaData.database_info?.tables_count} tables • {fullSchemaData.database_info?.total_rows?.toLocaleString()} lignes totales\n                    </p>\n                  </div>\n                  <SecondaryButton\n                    onClick={() => setShowTablesOverview(false)}\n                    icon=\"✕\"\n                    className=\"!p-2 hover:bg-gray-800 transition-colors\"\n                  />\n                </div>\n              </div>\n\n              {/* Contenu scrollable */}\n              <div className=\"p-6\">\n                <TablesOverview data={fullSchemaData} />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Chat Section - Aligné en bas */}\n        {dataLoaded && (\n          <div className=\"mb-8\">\n            <DarkCard>\n              <SectionHeader\n                title=\"Assistant IA BrAInBI\"\n                subtitle=\"Posez vos questions sur vos données en langage naturel\"\n                icon=\"💬\"\n                action={\n                  <SecondaryButton\n                    onClick={clearHistory}\n                    disabled={loading || chatHistory.length === 0}\n                    icon=\"🗑️\"\n                    className=\"!py-2 !px-4\"\n                  >\n                    Effacer\n                  </SecondaryButton>\n                }\n              />\n\n              {/* Chat History */}\n              <div className=\"h-96 overflow-y-auto mb-6 p-4 bg-gray-900/30 rounded-lg border border-gray-800\">\n                {chatHistory.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full text-center\">\n                    <div className=\"w-16 h-16 bg-gradient-to-r from-indigo-400 to-pink-500 rounded-2xl mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl\">🤖</span>\n                    </div>\n                    <h3 className=\"text-lg font-semibold text-gray-300 mb-2\">\n                      Prêt à analyser vos données avec BrAInBI\n                    </h3>\n                    <p className=\"text-gray-500 max-w-md\">\n                      Posez votre première question pour commencer l'analyse intelligente de vos données\n                    </p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    {chatHistory.map((message, index) => (\n                      <ChatBubble\n                        key={index}\n                        type={message.type}\n                        timestamp={message.timestamp}\n                      >\n                        {message.content}\n\n                        {/* Graphiques */}\n                        {message.chart && (\n                          <div className=\"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\">\n                            <Plot\n                              data={message.chart.data}\n                              layout={{\n                                ...message.chart.layout,\n                                autosize: true,\n                                height: 400,\n                                paper_bgcolor: 'rgba(0,0,0,0)',\n                                plot_bgcolor: 'rgba(0,0,0,0)',\n                                font: { color: '#F3F4F6' }\n                              }}\n                              config={{ responsive: true }}\n                              style={{ width: '100%' }}\n                            />\n                          </div>\n                        )}\n\n                        {message.chartJson && (\n                          <div className=\"mt-4 p-4 bg-gray-950 rounded-lg border border-gray-700\">\n                            {renderDynamicChart(JSON.parse(message.chartJson))}\n                          </div>\n                        )}\n                      </ChatBubble>\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              {/* Suggestions */}\n              <div className=\"mb-6\">\n                <h4 className=\"text-sm font-medium text-gray-400 mb-3\">💡 Suggestions</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {[\n                    'Montre-moi un graphique en barres',\n                    'Analyse les tendances',\n                    'Y a-t-il des anomalies ?',\n                    'Crée un diagramme circulaire',\n                    'Résume les données'\n                  ].map((suggestion, index) => (\n                    <button\n                      key={index}\n                      onClick={() => setQuestion(suggestion)}\n                      disabled={loading || !dataLoaded}\n                      className=\"px-3 py-1 text-xs bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full border border-gray-700 hover:border-gray-600 transition-colors disabled:opacity-50\"\n                    >\n                      {suggestion}\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Input Zone */}\n              <div className=\"flex space-x-4\">\n                <div className=\"flex-1\">\n                  <DarkInput\n                    placeholder=\"Posez votre question sur les données...\"\n                    value={question}\n                    onChange={(e) => setQuestion(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && !loading && dataLoaded && question.trim() && handleAskQuestion()}\n                    disabled={loading || !dataLoaded}\n                    icon=\"💬\"\n                  />\n                </div>\n                <PrimaryButton\n                  onClick={handleAskQuestion}\n                  disabled={loading || !dataLoaded || !question.trim()}\n                  loading={loading}\n                  icon={!loading ? \"🚀\" : null}\n                  className=\"!py-3\"\n                >\n                  Envoyer\n                </PrimaryButton>\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Modal KPI */}\n        {showKpiModal && (\n          <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n            <DarkCard className=\"max-w-2xl w-full max-h-[80vh] overflow-y-auto\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-100\">\n                  📚 Explication pédagogique\n                </h3>\n                <button\n                  onClick={() => setShowKpiModal(false)}\n                  className=\"w-8 h-8 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center text-gray-400 hover:text-gray-200 transition-colors\"\n                >\n                  ×\n                </button>\n              </div>\n              <div className=\"text-gray-300 whitespace-pre-wrap leading-relaxed\">\n                {kpiExplanation}\n              </div>\n            </DarkCard>\n          </div>\n        )}\n\n        {/* Loading Indicator */}\n        {loading && (\n          <div className=\"fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-lg p-4 shadow-xl z-50\">\n            <div className=\"flex items-center space-x-3\">\n              <DarkSpinner />\n              <span className=\"text-gray-300 font-medium\">\n                Traitement en cours...\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Composant App principal avec routing\nfunction App() {\n  return (\n    <Routes>\n      <Route path=\"/\" element={<HomePage />} />\n      <Route path=\"/tables\" element={<TablesListPage />} />\n      <Route path=\"/table/:tableName\" element={<TableDetailPage />} />\n      <Route path=\"/visual-builder\" element={<VisualBuilderPage />} />\n      <Route path=\"/drag-visual\" element={<DragVisualPage />} />\n      <Route path=\"/dashboard\" element={<DashboardPage />} />\n      <Route path=\"/mes-visuels\" element={<DashboardPage />} />\n      <Route path=\"/config/sql\" element={<SQLConfigPage />} />\n      <Route path=\"/configuration\" element={<SQLConfigPage />} />\n    </Routes>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AAC7D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAO,WAAW;AAClB,SACEC,aAAa,EACbC,SAAS,EACTC,aAAa,EACbC,eAAe,EACfC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,SAAS,EACTC,WAAW,EACXC,aAAa,QACR,2BAA2B;AAClC,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,YAAY,GAAG,uBAAuB;;AAE5C;AACA,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,oBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+C,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAMiE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzC,YAAY,qBAAqB,CAAC;MACtEoC,gBAAgB,CAACI,QAAQ,CAACE,IAAI,CAACC,UAAU,CAAC;MAC1CL,kBAAkB,CAACE,QAAQ,CAACE,IAAI,CAACE,MAAM,CAAC;MACxC,OAAOJ,QAAQ,CAACE,IAAI,CAACC,UAAU;IACjC,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd4B,OAAO,CAAC5B,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/EmB,gBAAgB,CAAC,KAAK,CAAC;MACvBE,kBAAkB,CAAC,IAAI,CAAC;MACxB,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAM6B,YAAY,GAAG,MAAMR,qBAAqB,CAAC,CAAC;MAClD,IAAI,CAACQ,YAAY,EAAE;QACjB7B,QAAQ,CAAC,2EAA2E,CAAC;QACrFF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMwB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzC,YAAY,YAAY,CAAC;MAC7Dc,WAAW,CAAC0B,QAAQ,CAACE,IAAI,CAAC;MAC1BlB,cAAc,CAACgB,QAAQ,CAACE,IAAI,CAACM,YAAY,CAAC;MAC1ClB,aAAa,CAAC,IAAI,CAAC;IAErB,CAAC,CAAC,OAAOmB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZjC,QAAQ,CAAC,oDAAoD,IAAI,EAAAgC,aAAA,GAAAD,GAAG,CAACT,QAAQ,cAAAU,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcR,IAAI,cAAAS,kBAAA,uBAAlBA,kBAAA,CAAoBC,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;MAC5GvB,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwB,QAAQ,GAAG,MAAM9D,KAAK,CAAC+D,GAAG,CAAC,GAAGzC,YAAY,cAAc,CAAC;MAC/DkC,iBAAiB,CAACM,QAAQ,CAACE,IAAI,CAAC;MAChCV,qBAAqB,CAAC,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOiB,GAAG,EAAE;MAAA,IAAAM,cAAA,EAAAC,mBAAA;MACZtC,QAAQ,CAAC,qDAAqD,IAAI,EAAAqC,cAAA,GAAAN,GAAG,CAACT,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcb,IAAI,cAAAc,mBAAA,uBAAlBA,mBAAA,CAAoBJ,MAAM,KAAIH,GAAG,CAACI,OAAO,CAAC,CAAC;IAC/G,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA3C,KAAK,CAACoF,SAAS,CAAC,MAAM;IACpB;IACAlB,qBAAqB,CAAC,CAAC,CAACmB,IAAI,CAACX,YAAY,IAAI;MAC3C,IAAIA,YAAY,EAAE;QAChBD,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACL9B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxC,QAAQ,CAACyC,IAAI,CAAC,CAAC,EAAE;MACpB1C,QAAQ,CAAC,8BAA8B,CAAC;MACxC;IACF;IAEA,IAAI,CAACW,UAAU,EAAE;MACfX,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,MAAM2C,UAAU,GAAG,CAAC,GAAGxC,WAAW,EAAE;MAClCyC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE5C,QAAQ;MACjB6C,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC;IACF5C,cAAc,CAACuC,UAAU,CAAC;IAE1B,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAM9D,KAAK,CAACyF,IAAI,CAAC,GAAGnE,YAAY,MAAM,EAAE;QACvDmB,QAAQ,EAAEA;MACZ,CAAC,CAAC;;MAEF;MACAG,cAAc,CAAC,CAAC,GAAGuC,UAAU,EAAE;QAC7BC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAEvB,QAAQ,CAACE,IAAI,CAAC0B,QAAQ;QAC/BC,KAAK,EAAE7B,QAAQ,CAACE,IAAI,CAAC4B,UAAU;QAC/BC,SAAS,EAAE/B,QAAQ,CAACE,IAAI,CAAC8B,UAAU;QACnCR,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MAC3C,CAAC,CAAC,CAAC;MAEH9C,WAAW,CAAC,EAAE,CAAC;IAEjB,CAAC,CAAC,OAAO6B,GAAG,EAAE;MAAA,IAAAwB,cAAA,EAAAC,mBAAA;MACZxD,QAAQ,CAAC,EAAAuD,cAAA,GAAAxB,GAAG,CAACT,QAAQ,cAAAiC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/B,IAAI,cAAAgC,mBAAA,uBAAlBA,mBAAA,CAAoBtB,MAAM,KAAI,2BAA2B,CAAC;IACrE,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2D,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI,CAAC/C,UAAU,EAAE;MACfX,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAM9D,KAAK,CAACyF,IAAI,CAAC,GAAGnE,YAAY,cAAc,EAAE;QAC/D6E,QAAQ,EAAED,OAAO;QACjBE,OAAO,EAAE,kCAAkC,CAAAjE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkE,UAAU,KAAI,OAAO;MAC5E,CAAC,CAAC;MAEFrD,iBAAiB,CAACc,QAAQ,CAACE,IAAI,CAAC0B,QAAQ,CAAC;MACzCxC,eAAe,CAAC,IAAI,CAAC;;MAErB;MACAN,cAAc,CAAC0D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC/BlB,IAAI,EAAE,aAAa;QACnBC,OAAO,EAAEvB,QAAQ,CAACE,IAAI,CAAC0B,QAAQ;QAC/BJ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MAC3C,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOjB,GAAG,EAAE;MAAA,IAAAgC,cAAA,EAAAC,mBAAA;MACZhE,QAAQ,CAAC,EAAA+D,cAAA,GAAAhC,GAAG,CAACT,QAAQ,cAAAyC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcvC,IAAI,cAAAwC,mBAAA,uBAAlBA,mBAAA,CAAoB9B,MAAM,KAAI,+BAA+B,CAAC;IACzE,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMzG,KAAK,CAAC0G,MAAM,CAAC,GAAGpF,YAAY,uBAAuB,CAAC;MAC1DsB,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZJ,OAAO,CAAC5B,KAAK,CAAC,gDAAgD,EAAEgC,GAAG,CAAC;IACtE;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,QAAQ,GAAG,EAAE;IACnB,MAAMC,MAAM,GAAG;MACbC,KAAK,EAAEH,SAAS,CAACG,KAAK,IAAI,WAAW;MACrCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,GAAG;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAG;IACvC,CAAC;IAED,QAAQV,SAAS,CAACxB,IAAI;MACpB,KAAK,KAAK;QACRyB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdrC,IAAI,EAAE,KAAK;UACXsC,MAAM,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC7B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,KAAK;QACRlB,QAAQ,CAACU,IAAI,CAAC;UACZS,MAAM,EAAEpB,SAAS,CAACoB,MAAM;UACxBC,MAAM,EAAErB,SAAS,CAACqB,MAAM;UACxB7C,IAAI,EAAE,KAAK;UACXsC,MAAM,EAAE;YAAEQ,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;UAAE;QAC5E,CAAC,CAAC;QACF;MAEF,KAAK,MAAM;QACTrB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdrC,IAAI,EAAE,SAAS;UACf+C,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE;YAAET,KAAK,EAAE;UAAU;QAC3B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,SAAS;QACZlB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdrC,IAAI,EAAE,SAAS;UACf+C,IAAI,EAAE,SAAS;UACfT,MAAM,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEU,IAAI,EAAE;UAAE;QACtC,CAAC,CAAC;QACFvB,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAG,CAAC;QAChD;MAEF,KAAK,WAAW;QACdlB,QAAQ,CAACU,IAAI,CAAC;UACZC,CAAC,EAAEZ,SAAS,CAACY,CAAC;UACdC,CAAC,EAAEb,SAAS,CAACa,CAAC;UACdrC,IAAI,EAAE,KAAK;UACXsC,MAAM,EAAE;YAAEC,KAAK,EAAE;UAAU;QAC7B,CAAC,CAAC;QACFb,MAAM,CAACc,KAAK,GAAG;UAAEb,KAAK,EAAEH,SAAS,CAACiB,MAAM,IAAI;QAAG,CAAC;QAChDf,MAAM,CAACgB,KAAK,GAAG;UAAEf,KAAK,EAAEH,SAAS,CAACmB,MAAM,IAAI;QAAY,CAAC;QACzD;MAEF;QACE,oBAAO1G,OAAA;UAAAiH,QAAA,GAAG,qCAAgC,EAAC1B,SAAS,CAACxB,IAAI;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;IAClE;IAEA,oBACErH,OAAA,CAACpB,IAAI;MACH+D,IAAI,EAAE6C,QAAS;MACfC,MAAM,EAAEA,MAAO;MACf5C,MAAM,EAAE;QAAEyE,UAAU,EAAE;MAAK,CAAE;MAC7BC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO;IAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAEN,CAAC;EAMD,oBACErH,OAAA;IAAKyH,SAAS,EAAC,gDAAgD;IAAAR,QAAA,gBAE7DjH,OAAA,CAACnB,aAAa;MACZ6I,OAAO,EAAE5F,UAAW;MACpBM,aAAa,EAAEA,aAAc;MAC7BuF,SAAS,EAAErF;IAAgB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFrH,OAAA;MAAKyH,SAAS,EAAC,wBAAwB;MAAAR,QAAA,gBAErCjH,OAAA;QAAKyH,SAAS,EAAC,cAAc;QAAAR,QAAA,gBAC3BjH,OAAA,CAAClB,SAAS;UACR4G,KAAK,EAAC,SAAS;UACfkC,QAAQ,EAAC;QAAqC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACFrH,OAAA;UAAKyH,SAAS,EAAC,kBAAkB;UAAAR,QAAA,gBAC/BjH,OAAA;YACE6H,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,cAAc;YAClBL,SAAS,EAAC;UAA8C;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACFrH,OAAA;YAAGyH,SAAS,EAAC,yCAAyC;YAAAR,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAACvF,UAAU,iBACV9B,OAAA;QAAKyH,SAAS,EAAC,OAAO;QAAAR,QAAA,eACpBjH,OAAA,CAACf,QAAQ;UAACwI,SAAS,EAAC,aAAa;UAAAR,QAAA,eAC/BjH,OAAA;YAAKyH,SAAS,EAAC,OAAO;YAAAR,QAAA,gBACpBjH,OAAA;cAAKyH,SAAS,EAAC,kHAAkH;cAAAR,QAAA,eAC/HjH,OAAA;gBAAMyH,SAAS,EAAC,UAAU;gBAAAR,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eAENrH,OAAA;cAAIyH,SAAS,EAAC,0CAA0C;cAAAR,QAAA,EACrD7E,aAAa,GAAG,sBAAsB,GAAG;YAA0B;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACLrH,OAAA;cAAGyH,SAAS,EAAC,qCAAqC;cAAAR,QAAA,EAC/C7E,aAAa,GACV,qEAAqE,GACrE;YAAwD;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC,EAGHjF,aAAa,IAAIE,eAAe,iBAC/BtC,OAAA;cAAKyH,SAAS,EAAC,8EAA8E;cAAAR,QAAA,gBAC3FjH,OAAA;gBAAKyH,SAAS,EAAC,kCAAkC;gBAAAR,QAAA,gBAC/CjH,OAAA;kBAAMyH,SAAS,EAAC,gBAAgB;kBAAAR,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCrH,OAAA;kBAAMyH,SAAS,EAAC,4BAA4B;kBAAAR,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNrH,OAAA;gBAAKyH,SAAS,EAAC,uBAAuB;gBAAAR,QAAA,gBACpCjH,OAAA;kBAAAiH,QAAA,GAAK,WAAS,EAAC3E,eAAe,CAACyF,MAAM;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CrH,OAAA;kBAAAiH,QAAA,GAAK,QAAM,EAAC3E,eAAe,CAAC0F,QAAQ;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEArG,OAAO,gBACNhB,OAAA;cAAKyH,SAAS,EAAC,iDAAiD;cAAAR,QAAA,gBAC9DjH,OAAA,CAACV,WAAW;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACfrH,OAAA;gBAAMyH,SAAS,EAAC,eAAe;gBAAAR,QAAA,EAC5B7E,aAAa,GAAG,uBAAuB,GAAG;cAAqC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,GACJnG,KAAK,gBACPlB,OAAA;cAAKyH,SAAS,EAAC,MAAM;cAAAR,QAAA,gBACnBjH,OAAA;gBAAKyH,SAAS,EAAC,mBAAmB;gBAAAR,QAAA,gBAChCjH,OAAA;kBAAAiH,QAAA,GAAG,SAAE,EAAC7E,aAAa,GAAG,qBAAqB,GAAG,uBAAuB;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1ErH,OAAA;kBAAGyH,SAAS,EAAC,+BAA+B;kBAAAR,QAAA,EAAE/F;gBAAK;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,EACLjF,aAAa,gBACZpC,OAAA;gBAAKyH,SAAS,EAAC,+BAA+B;gBAAAR,QAAA,gBAC5CjH,OAAA,CAAChB,eAAe;kBACdiJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,aAAa,CAAE;kBACvCqH,IAAI,EAAC,cAAI;kBACTT,SAAS,EAAC,mBAAmB;kBAAAR,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC,eAClBrH,OAAA,CAACjB,aAAa;kBACZkJ,OAAO,EAAElF,YAAa;kBACtBmF,IAAI,EAAC,cAAI;kBACTT,SAAS,EAAC,mBAAmB;kBAAAR,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAe,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,gBAENrH,OAAA,CAACjB,aAAa;gBACZkJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,aAAa,CAAE;gBACvCqH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,mBAAmB;gBAAAR,QAAA,EAC9B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAChB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENrH,OAAA;cAAKyH,SAAS,EAAC,MAAM;cAAAR,QAAA,gBACnBjH,OAAA;gBAAKyH,SAAS,EAAC,qBAAqB;gBAAAR,QAAA,eAClCjH,OAAA;kBAAAiH,QAAA,GAAG,SAAE,EAAC7E,aAAa,GAAG,mBAAmB,GAAG,mBAAmB;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNrH,OAAA,CAACjB,aAAa;gBACZkJ,OAAO,EAAElF,YAAa;gBACtBmF,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,mBAAmB;gBAAAR,QAAA,EAC9B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAKAvF,UAAU,IAAIhB,QAAQ,iBACrBd,OAAA;QAAKyH,SAAS,EAAC,OAAO;QAAAR,QAAA,gBACpBjH,OAAA,CAACT,aAAa;UACZmG,KAAK,EAAC,qCAA+B;UACrCkC,QAAQ,EAAC,sDAAmD;UAC5DM,IAAI,EAAC;QAAI;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEFrH,OAAA;UAAKyH,SAAS,EAAC,2DAA2D;UAAAR,QAAA,gBACxEjH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,gBAAgB;YACtByC,KAAK,EAAE,EAAA/H,oBAAA,GAAAU,QAAQ,CAACsH,UAAU,cAAAhI,oBAAA,uBAAnBA,oBAAA,CAAqBiI,cAAc,CAAC,CAAC,KAAI,GAAI;YACpDH,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAM;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,UAAU;YAChByC,KAAK,EAAE,EAAA9H,iBAAA,GAAAS,QAAQ,CAAC0H,OAAO,cAAAnI,iBAAA,uBAAhBA,iBAAA,CAAkBoI,MAAM,KAAI,GAAI;YACvCP,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,QAAQ;YACdyC,KAAK,EAAE,GAAGrH,QAAQ,CAAC4H,YAAY,IAAI,CAAC,SAAU;YAC9CR,IAAI,EAAC,oBAAK;YACVI,KAAK,EAAC,SAAS;YACfL,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,SAAS,CAAE;YACnC4G,SAAS,EAAC;UAAuD;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,gBAAgB;YACtByC,KAAK,EAAC,aAAa;YACnBD,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,SAAS;YAChBN,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,cAAc,CAAE;YACxC4G,SAAS,EAAC;UAAuD;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,WAAW;YACjByC,KAAK,EAAC,aAAa;YACnBD,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC,UAAO;YACdN,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,YAAY,CAAE;YACtC4G,SAAS,EAAC;UAAuD;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,eAAe;YACrByC,KAAK,EAAC,YAAY;YAClBD,IAAI,EAAC,cAAI;YACTI,KAAK,EAAC,SAAS;YACfC,MAAM,EAAC,YAAY;YACnBN,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,aAAa,CAAE;YACvC4G,SAAS,EAAC;UAAuD;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACFrH,OAAA,CAACd,OAAO;YACNwG,KAAK,EAAC,QAAQ;YACdyC,KAAK,EAAC,aAAU;YAChBD,IAAI,EAAC,QAAG;YACRI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAM;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrH,OAAA,CAACf,QAAQ;UAACwI,SAAS,EAAC,MAAM;UAAAR,QAAA,gBACxBjH,OAAA;YAAKyH,SAAS,EAAC,wCAAwC;YAAAR,QAAA,gBACrDjH,OAAA,CAACT,aAAa;cACZmG,KAAK,EAAC,0BAAuB;cAC7BkC,QAAQ,EAAC,8DAA2D;cACpEM,IAAI,EAAC;YAAK;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eACFrH,OAAA;cAAKyH,SAAS,EAAC,gBAAgB;cAAAR,QAAA,gBAC7BjH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,cAAc,CAAE;gBACxC8H,QAAQ,EAAE3H,OAAQ;gBAClBkH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,aAAa;gBAAAR,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBrH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,iBAAiB,CAAE;gBAC3C8H,QAAQ,EAAE3H,OAAQ;gBAClBkH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,aAAa;gBAAAR,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBrH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,YAAY,CAAE;gBACtC8H,QAAQ,EAAE3H,OAAQ;gBAClBkH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,aAAa;gBAAAR,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBrH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,aAAa,CAAE;gBACvC8H,QAAQ,EAAE3H,OAAQ;gBAClBkH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,aAAa;gBAAAR,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC,eAClBrH,OAAA,CAACjB,aAAa;gBACZkJ,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,SAAS,CAAE;gBACnC8H,QAAQ,EAAE3H,OAAQ;gBAClBkH,IAAI,EAAC,cAAI;gBACTT,SAAS,EAAC,aAAa;gBAAAR,QAAA,EACxB;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrH,OAAA;YAAKyH,SAAS,EAAC,sBAAsB;YAAAR,QAAA,EAClCnG,QAAQ,CAAC8H,MAAM,IAAIC,MAAM,CAACC,IAAI,CAAChI,QAAQ,CAAC8H,MAAM,CAAC,CAACH,MAAM,GAAG,CAAC,GACzDI,MAAM,CAACE,OAAO,CAACjI,QAAQ,CAAC8H,MAAM,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,SAAS,EAAEC,SAAS,CAAC;cAAA,IAAAC,kBAAA;cAAA,QAAAA,kBAAA,GACrED,SAAS,CAACX,OAAO,cAAAY,kBAAA,uBAAjBA,kBAAA,CAAmBH,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAChCtJ,OAAA;gBAAmCyH,SAAS,EAAC,6BAA6B;gBAAAR,QAAA,gBACxEjH,OAAA,CAACX,SAAS;kBACRkK,OAAO,EAAC,MAAM;kBACd9B,SAAS,EAAC,uDAAuD;kBACjEQ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACyE,GAAG,CAAE;kBAAApC,QAAA,EAEpCoC;gBAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZrH,OAAA,CAAChB,eAAe;kBACdiJ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACyE,GAAG,CAAE;kBACrC5B,SAAS,EAAC,+BAA+B;kBACzCS,IAAI,EAAC;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAZM,GAAG6B,SAAS,IAAII,KAAK,EAAE;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAa5B,CACN,CAAC;YAAA,CACJ,CAAC,IAAA/G,kBAAA,GAEDQ,QAAQ,CAAC0H,OAAO,cAAAlI,kBAAA,uBAAhBA,kBAAA,CAAkB2I,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC/BtJ,OAAA;cAAiByH,SAAS,EAAC,6BAA6B;cAAAR,QAAA,gBACtDjH,OAAA,CAACX,SAAS;gBACRkK,OAAO,EAAC,MAAM;gBACd9B,SAAS,EAAC,uDAAuD;gBACjEQ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACyE,GAAG,CAAE;gBAAApC,QAAA,EAEpCoC;cAAG;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eACZrH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACyE,GAAG,CAAE;gBACrC5B,SAAS,EAAC,+BAA+B;gBACzCS,IAAI,EAAC;cAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GAZMiC,KAAK;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaV,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGV7F,WAAW,iBACVxB,OAAA,CAACf,QAAQ;UAACwI,SAAS,EAAC,4CAA4C;UAAAR,QAAA,gBAC9DjH,OAAA,CAACT,aAAa;YACZmG,KAAK,EAAC,2BAAqB;YAC3BkC,QAAQ,EAAC,sCAAmC;YAC5CM,IAAI,EAAC;UAAI;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACFrH,OAAA;YAAKyH,SAAS,EAAC,mDAAmD;YAAAR,QAAA,EAC/DzF;UAAW;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACX,eAGDrH,OAAA,CAACf,QAAQ;UAAAgI,QAAA,gBACPjH,OAAA,CAACT,aAAa;YACZmG,KAAK,EAAC,qCAA+B;YACrCkC,QAAQ,EAAC,sCAAmC;YAC5CM,IAAI,EAAC;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eAEFrH,OAAA;YAAKyH,SAAS,EAAC,iBAAiB;YAAAR,QAAA,eAC9BjH,OAAA;cAAOyH,SAAS,EAAC,gBAAgB;cAAAR,QAAA,gBAC/BjH,OAAA;gBAAAiH,QAAA,eACEjH,OAAA;kBAAIyH,SAAS,EAAC,0BAA0B;kBAAAR,QAAA,GAAA1G,kBAAA,GACrCO,QAAQ,CAAC0H,OAAO,cAAAjI,kBAAA,uBAAhBA,kBAAA,CAAkB0I,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAChCtJ,OAAA;oBAAgByH,SAAS,EAAC,gEAAgE;oBAAAR,QAAA,EACvFoC;kBAAG,GADGC,KAAK;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRrH,OAAA;gBAAAiH,QAAA,GAAAzG,qBAAA,GACGM,QAAQ,CAAC0I,WAAW,cAAAhJ,qBAAA,uBAApBA,qBAAA,CAAsByI,GAAG,CAAC,CAACQ,GAAG,EAAEC,QAAQ;kBAAA,IAAAC,kBAAA;kBAAA,oBACvC3J,OAAA;oBAAmByH,SAAS,EAAC,+CAA+C;oBAAAR,QAAA,GAAA0C,kBAAA,GACzE7I,QAAQ,CAAC0H,OAAO,cAAAmB,kBAAA,uBAAhBA,kBAAA,CAAkBV,GAAG,CAAC,CAACI,GAAG,EAAEO,QAAQ,kBACnC5J,OAAA;sBAAmByH,SAAS,EAAC,yBAAyB;sBAAAR,QAAA,EACnDwC,GAAG,CAACJ,GAAG,CAAC,IAAI;oBAAK,GADXO,QAAQ;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACL;kBAAC,GALKqC,QAAQ;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMb,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGArF,kBAAkB,IAAIE,cAAc,iBACnClC,OAAA;QAAKyH,SAAS,EAAC,qGAAqG;QAAAR,QAAA,eAClHjH,OAAA;UAAKyH,SAAS,EAAC,2DAA2D;UAAAR,QAAA,gBAExEjH,OAAA;YAAKyH,SAAS,EAAC,qEAAqE;YAAAR,QAAA,eAClFjH,OAAA;cAAKyH,SAAS,EAAC,uCAAuC;cAAAR,QAAA,gBACpDjH,OAAA;gBAAAiH,QAAA,gBACEjH,OAAA;kBAAIyH,SAAS,EAAC,kCAAkC;kBAAAR,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvErH,OAAA;kBAAGyH,SAAS,EAAC,oBAAoB;kBAAAR,QAAA,GAAC,sBACf,GAAAxG,qBAAA,GAACyB,cAAc,CAAC2H,aAAa,cAAApJ,qBAAA,uBAA5BA,qBAAA,CAA8BqJ,IAAI,EAAC,UAAG,GAAApJ,sBAAA,GAACwB,cAAc,CAAC2H,aAAa,cAAAnJ,sBAAA,uBAA5BA,sBAAA,CAA8BgI,YAAY,EAAC,iBAAU,GAAA/H,sBAAA,GAACuB,cAAc,CAAC2H,aAAa,cAAAlJ,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8ByH,UAAU,cAAAxH,sBAAA,uBAAxCA,sBAAA,CAA0CyH,cAAc,CAAC,CAAC,EAAC,iBAC5K;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNrH,OAAA,CAAChB,eAAe;gBACdiJ,OAAO,EAAEA,CAAA,KAAMhG,qBAAqB,CAAC,KAAK,CAAE;gBAC5CiG,IAAI,EAAC,QAAG;gBACRT,SAAS,EAAC;cAA0C;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrH,OAAA;YAAKyH,SAAS,EAAC,KAAK;YAAAR,QAAA,eAClBjH,OAAA,CAACR,cAAc;cAACmD,IAAI,EAAET;YAAe;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAvF,UAAU,iBACT9B,OAAA;QAAKyH,SAAS,EAAC,MAAM;QAAAR,QAAA,eACnBjH,OAAA,CAACf,QAAQ;UAAAgI,QAAA,gBACPjH,OAAA,CAACT,aAAa;YACZmG,KAAK,EAAC,sBAAsB;YAC5BkC,QAAQ,EAAC,2DAAwD;YACjEM,IAAI,EAAC,cAAI;YACT6B,MAAM,eACJ/J,OAAA,CAAChB,eAAe;cACdiJ,OAAO,EAAE7C,YAAa;cACtBuD,QAAQ,EAAE3H,OAAO,IAAIM,WAAW,CAACmH,MAAM,KAAK,CAAE;cAC9CP,IAAI,EAAC,oBAAK;cACVT,SAAS,EAAC,aAAa;cAAAR,QAAA,EACxB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB;UAClB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGFrH,OAAA;YAAKyH,SAAS,EAAC,gFAAgF;YAAAR,QAAA,EAC5F3F,WAAW,CAACmH,MAAM,KAAK,CAAC,gBACvBzI,OAAA;cAAKyH,SAAS,EAAC,8DAA8D;cAAAR,QAAA,gBAC3EjH,OAAA;gBAAKyH,SAAS,EAAC,0GAA0G;gBAAAR,QAAA,eACvHjH,OAAA;kBAAMyH,SAAS,EAAC,UAAU;kBAAAR,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNrH,OAAA;gBAAIyH,SAAS,EAAC,0CAA0C;gBAAAR,QAAA,EAAC;cAEzD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrH,OAAA;gBAAGyH,SAAS,EAAC,wBAAwB;gBAAAR,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,gBAENrH,OAAA;cAAKyH,SAAS,EAAC,WAAW;cAAAR,QAAA,EACvB3F,WAAW,CAAC2H,GAAG,CAAC,CAAC3F,OAAO,EAAEgG,KAAK,kBAC9BtJ,OAAA,CAACb,UAAU;gBAET4E,IAAI,EAAET,OAAO,CAACS,IAAK;gBACnBE,SAAS,EAAEX,OAAO,CAACW,SAAU;gBAAAgD,QAAA,GAE5B3D,OAAO,CAACU,OAAO,EAGfV,OAAO,CAACgB,KAAK,iBACZtE,OAAA;kBAAKyH,SAAS,EAAC,wDAAwD;kBAAAR,QAAA,eACrEjH,OAAA,CAACpB,IAAI;oBACH+D,IAAI,EAAEW,OAAO,CAACgB,KAAK,CAAC3B,IAAK;oBACzB8C,MAAM,EAAE;sBACN,GAAGnC,OAAO,CAACgB,KAAK,CAACmB,MAAM;sBACvBE,QAAQ,EAAE,IAAI;sBACdC,MAAM,EAAE,GAAG;sBACXoE,aAAa,EAAE,eAAe;sBAC9BC,YAAY,EAAE,eAAe;sBAC7BC,IAAI,EAAE;wBAAE5D,KAAK,EAAE;sBAAU;oBAC3B,CAAE;oBACFzD,MAAM,EAAE;sBAAEyE,UAAU,EAAE;oBAAK,CAAE;oBAC7BC,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,EAEA/D,OAAO,CAACkB,SAAS,iBAChBxE,OAAA;kBAAKyH,SAAS,EAAC,wDAAwD;kBAAAR,QAAA,EACpE3B,kBAAkB,CAAC6E,IAAI,CAACC,KAAK,CAAC9G,OAAO,CAACkB,SAAS,CAAC;gBAAC;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CACN;cAAA,GA7BIiC,KAAK;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8BA,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNrH,OAAA;YAAKyH,SAAS,EAAC,MAAM;YAAAR,QAAA,gBACnBjH,OAAA;cAAIyH,SAAS,EAAC,wCAAwC;cAAAR,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ErH,OAAA;cAAKyH,SAAS,EAAC,sBAAsB;cAAAR,QAAA,EAClC,CACC,mCAAmC,EACnC,uBAAuB,EACvB,0BAA0B,EAC1B,8BAA8B,EAC9B,oBAAoB,CACrB,CAACgC,GAAG,CAAC,CAACoB,UAAU,EAAEf,KAAK,kBACtBtJ,OAAA;gBAEEiI,OAAO,EAAEA,CAAA,KAAM5G,WAAW,CAACgJ,UAAU,CAAE;gBACvC1B,QAAQ,EAAE3H,OAAO,IAAI,CAACc,UAAW;gBACjC2F,SAAS,EAAC,+JAA+J;gBAAAR,QAAA,EAExKoD;cAAU,GALNf,KAAK;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrH,OAAA;YAAKyH,SAAS,EAAC,gBAAgB;YAAAR,QAAA,gBAC7BjH,OAAA;cAAKyH,SAAS,EAAC,QAAQ;cAAAR,QAAA,eACrBjH,OAAA,CAACZ,SAAS;gBACRkL,WAAW,EAAC,4CAAyC;gBACrDnC,KAAK,EAAE/G,QAAS;gBAChBmJ,QAAQ,EAAGC,CAAC,IAAKnJ,WAAW,CAACmJ,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;gBAC7CuC,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAI,CAAC3J,OAAO,IAAIc,UAAU,IAAIV,QAAQ,CAACyC,IAAI,CAAC,CAAC,IAAID,iBAAiB,CAAC,CAAE;gBACzG+E,QAAQ,EAAE3H,OAAO,IAAI,CAACc,UAAW;gBACjCoG,IAAI,EAAC;cAAI;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrH,OAAA,CAACjB,aAAa;cACZkJ,OAAO,EAAErE,iBAAkB;cAC3B+E,QAAQ,EAAE3H,OAAO,IAAI,CAACc,UAAU,IAAI,CAACV,QAAQ,CAACyC,IAAI,CAAC,CAAE;cACrD7C,OAAO,EAAEA,OAAQ;cACjBkH,IAAI,EAAE,CAAClH,OAAO,GAAG,IAAI,GAAG,IAAK;cAC7ByG,SAAS,EAAC,OAAO;cAAAR,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGAzF,YAAY,iBACX5B,OAAA;QAAKyH,SAAS,EAAC,sFAAsF;QAAAR,QAAA,eACnGjH,OAAA,CAACf,QAAQ;UAACwI,SAAS,EAAC,+CAA+C;UAAAR,QAAA,gBACjEjH,OAAA;YAAKyH,SAAS,EAAC,wCAAwC;YAAAR,QAAA,gBACrDjH,OAAA;cAAIyH,SAAS,EAAC,qCAAqC;cAAAR,QAAA,EAAC;YAEpD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLrH,OAAA;cACEiI,OAAO,EAAEA,CAAA,KAAMpG,eAAe,CAAC,KAAK,CAAE;cACtC4F,SAAS,EAAC,uIAAuI;cAAAR,QAAA,EAClJ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNrH,OAAA;YAAKyH,SAAS,EAAC,mDAAmD;YAAAR,QAAA,EAC/DvF;UAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,EAGArG,OAAO,iBACNhB,OAAA;QAAKyH,SAAS,EAAC,yFAAyF;QAAAR,QAAA,eACtGjH,OAAA;UAAKyH,SAAS,EAAC,6BAA6B;UAAAR,QAAA,gBAC1CjH,OAAA,CAACV,WAAW;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACfrH,OAAA;YAAMyH,SAAS,EAAC,2BAA2B;YAAAR,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAlH,EAAA,CA7wBSD,QAAQ;EAAA,QACExB,WAAW;AAAA;AAAAkM,EAAA,GADrB1K,QAAQ;AA8wBjB,SAAS2K,GAAGA,CAAA,EAAG;EACb,oBACE7K,OAAA,CAACxB,MAAM;IAAAyI,QAAA,gBACLjH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,GAAG;MAACC,OAAO,eAAE/K,OAAA,CAACE,QAAQ;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzCrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,SAAS;MAACC,OAAO,eAAE/K,OAAA,CAACP,cAAc;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrDrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,mBAAmB;MAACC,OAAO,eAAE/K,OAAA,CAACN,eAAe;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChErH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,iBAAiB;MAACC,OAAO,eAAE/K,OAAA,CAACL,iBAAiB;QAAAuH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChErH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,cAAc;MAACC,OAAO,eAAE/K,OAAA,CAACJ,cAAc;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC1DrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,YAAY;MAACC,OAAO,eAAE/K,OAAA,CAACH,aAAa;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvDrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,cAAc;MAACC,OAAO,eAAE/K,OAAA,CAACH,aAAa;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzDrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,aAAa;MAACC,OAAO,eAAE/K,OAAA,CAACF,aAAa;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxDrH,OAAA,CAACvB,KAAK;MAACqM,IAAI,EAAC,gBAAgB;MAACC,OAAO,eAAE/K,OAAA,CAACF,aAAa;QAAAoH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD,CAAC;AAEb;AAAC2D,GAAA,GAdQH,GAAG;AAgBZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}